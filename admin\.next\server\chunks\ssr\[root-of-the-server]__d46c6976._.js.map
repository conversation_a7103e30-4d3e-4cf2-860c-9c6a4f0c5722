{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  })}`\n}\n\n// Format percentage\nexport function formatPercentage(value: number | undefined | null): string {\n  if (value === undefined || value === null || isNaN(value)) {\n    return '0.00%'\n  }\n  return `${value.toFixed(2)}%`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,iBAAiB,KAAgC;IAC/D,IAAI,UAAU,aAAa,UAAU,QAAQ,MAAM,QAAQ;QACzD,OAAO;IACT;IACA,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,wCAAmC,OAAO;;AAE5C;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,wCAAmC,OAAO,gBAAgB;;IAS5D;IAEA,KAAK,CAAI,KAAa;QACpB,wCAAmC;;IAOrC;IAEA,QAAQ,CAAC;QACP,wCAAmC;;IAOrC;IAEA,OAAO;QACL,wCAAmC;;IAOrC;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard,\n  Heart\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"user-wishlists\",\n          label: \"User Wishlists\",\n          icon: Heart,\n          href: \"/wishlists\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Owners & Developers\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-owner\",\n          label: \"Add Owner/Developer\",\n          icon: UserPlus,\n          href: \"/properties/owners/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAyCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/owner/PersonalDetails.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Badge } from '@/components/ui/badge'\nimport { User, Mail, Phone, Calendar, AlertCircle, MapPin, Building } from 'lucide-react'\n\ninterface PersonalData {\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  roles: {\n    isOwner: boolean\n    isDeveloper: boolean\n  }\n}\n\ninterface PersonalDetailsProps {\n  value: PersonalData\n  onChange: (personal: PersonalData) => void\n  errors?: {\n    firstName?: string[]\n    lastName?: string[]\n    email?: string[]\n    phone?: string[]\n    roles?: string[]\n  }\n}\n\nexport default function PersonalDetails({ value, onChange, errors }: PersonalDetailsProps) {\n  const handleInputChange = (field: string, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  const handleRoleChange = (selectedRole: 'owner' | 'developer') => {\n    onChange({\n      ...value,\n      roles: {\n        isOwner: selectedRole === 'owner',\n        isDeveloper: selectedRole === 'developer'\n      }\n    })\n  }\n\n  const getSelectedRole = () => {\n    if (value.roles.isOwner) return 'Owner'\n    if (value.roles.isDeveloper) return 'Developer'\n    return null\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <User className=\"h-5 w-5\" />\n          Personal Information\n        </CardTitle>\n        <CardDescription>\n          Basic personal details and contact information\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Name Fields */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"firstName\">First Name *</Label>\n            <Input\n              id=\"firstName\"\n              placeholder=\"Enter first name\"\n              value={value.firstName}\n              onChange={(e) => handleInputChange('firstName', e.target.value)}\n              className={errors?.firstName ? 'border-red-500' : ''}\n            />\n            {errors?.firstName && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.firstName[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"lastName\">Last Name *</Label>\n            <Input\n              id=\"lastName\"\n              placeholder=\"Enter last name\"\n              value={value.lastName}\n              onChange={(e) => handleInputChange('lastName', e.target.value)}\n              className={errors?.lastName ? 'border-red-500' : ''}\n            />\n            {errors?.lastName && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.lastName[0]}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Contact Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"email\">Email Address *</Label>\n            <div className=\"relative\">\n              <Input\n                id=\"email\"\n                type=\"email\"\n                placeholder=\"Enter email address\"\n                value={value.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                className={errors?.email ? 'border-red-500' : ''}\n              />\n              <Mail className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n            </div>\n            {errors?.email && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.email[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"phone\">Phone Number *</Label>\n            <div className=\"relative\">\n              <Input\n                id=\"phone\"\n                type=\"tel\"\n                placeholder=\"Enter phone number\"\n                value={value.phone}\n                onChange={(e) => handleInputChange('phone', e.target.value)}\n                className={errors?.phone ? 'border-red-500' : ''}\n                maxLength={15}\n              />\n              <Phone className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n            </div>\n            {errors?.phone && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.phone[0]}\n              </p>\n            )}\n          </div>\n        </div>\n\n\n\n\n\n        {/* Role Selection */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium\">Role Selection *</h4>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-3\">\n              <input\n                type=\"radio\"\n                id=\"owner\"\n                name=\"role\"\n                checked={value.roles.isOwner && !value.roles.isDeveloper}\n                onChange={() => handleRoleChange('owner')}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n              />\n              <Label htmlFor=\"owner\" className=\"flex items-center cursor-pointer\">\n                <User className=\"h-5 w-5 mr-2 text-blue-600\" />\n                <div>\n                  <span className=\"font-medium\">Property Owner</span>\n                  <p className=\"text-sm text-gray-600\">Owns properties for investment</p>\n                </div>\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <input\n                type=\"radio\"\n                id=\"developer\"\n                name=\"role\"\n                checked={value.roles.isDeveloper && !value.roles.isOwner}\n                onChange={() => handleRoleChange('developer')}\n                className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300\"\n              />\n              <Label htmlFor=\"developer\" className=\"flex items-center cursor-pointer\">\n                <Building className=\"h-5 w-5 mr-2 text-green-600\" />\n                <div>\n                  <span className=\"font-medium\">Developer</span>\n                  <p className=\"text-sm text-gray-600\">Develops and constructs properties</p>\n                </div>\n              </Label>\n            </div>\n          </div>\n\n          {getSelectedRole() && (\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-sm text-muted-foreground\">Selected role:</span>\n              <Badge variant=\"secondary\">\n                {getSelectedRole()}\n              </Badge>\n            </div>\n          )}\n\n          {errors?.roles && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.roles[0]}\n            </p>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;AAiCe,SAAS,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAwB;IACvF,MAAM,oBAAoB,CAAC,OAAe;QACxC,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS;YACP,GAAG,KAAK;YACR,OAAO;gBACL,SAAS,iBAAiB;gBAC1B,aAAa,iBAAiB;YAChC;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,KAAK,CAAC,OAAO,EAAE,OAAO;QAChC,IAAI,MAAM,KAAK,CAAC,WAAW,EAAE,OAAO;QACpC,OAAO;IACT;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG9B,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,SAAS;wCACtB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAW,QAAQ,YAAY,mBAAmB;;;;;;oCAEnD,QAAQ,2BACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,SAAS,CAAC,EAAE;;;;;;;;;;;;;0CAK1B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,QAAQ;wCACrB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAW,QAAQ,WAAW,mBAAmB;;;;;;oCAElD,QAAQ,0BACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kCAO3B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO,MAAM,KAAK;gDAClB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,WAAW,QAAQ,QAAQ,mBAAmB;;;;;;0DAEhD,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;oCAEjB,QAAQ,uBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;0CAKtB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,aAAY;gDACZ,OAAO,MAAM,KAAK;gDAClB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gDAC1D,WAAW,QAAQ,QAAQ,mBAAmB;gDAC9C,WAAW;;;;;;0DAEb,6WAAC,wRAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;oCAElB,QAAQ,uBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kCAWxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAc;;;;;;0CAC5B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,SAAS,MAAM,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,WAAW;gDACxD,UAAU,IAAM,iBAAiB;gDACjC,WAAU;;;;;;0DAEZ,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAQ,WAAU;;kEAC/B,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6WAAC;;0EACC,6WAAC;gEAAK,WAAU;0EAAc;;;;;;0EAC9B,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAK3C,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,SAAS,MAAM,KAAK,CAAC,WAAW,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO;gDACxD,UAAU,IAAM,iBAAiB;gDACjC,WAAU;;;;;;0DAEZ,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;kEACnC,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6WAAC;;0EACC,6WAAC;gEAAK,WAAU;0EAAc;;;;;;0EAC9B,6WAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAM5C,mCACC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAK,WAAU;kDAAgC;;;;;;kDAChD,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDACZ;;;;;;;;;;;;4BAKN,QAAQ,uBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,6QAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6WAAC,6QAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/owner/BusinessInformation.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Briefcase, Building, CreditCard, AlertCircle, Info } from 'lucide-react'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\ninterface BusinessData {\n  businessInfo?: {\n    company?: string\n    businessType?: string\n    gstNumber?: string\n    panNumber?: string\n    incorporationDate?: string\n    businessAddress?: string\n  }\n  bankDetails: {\n    accountNumber: string\n    ifscCode: string\n    bankName: string\n    accountHolderName: string\n    accountType?: string\n    branchName?: string\n  }\n  developerDetails?: {\n    companyName?: string\n    experience?: number\n    licenseNumber?: string\n    specialization?: string[]\n  }\n  roles?: {\n    isOwner: boolean\n    isDeveloper: boolean\n    isInvestor: boolean\n  }\n}\n\ninterface BusinessInformationProps {\n  value: BusinessData\n  onChange: (business: BusinessData) => void\n  errors?: {\n    businessInfo?: {\n      company?: string[]\n      businessType?: string[]\n      gstNumber?: string[]\n      panNumber?: string[]\n      incorporationDate?: string[]\n      businessAddress?: string[]\n    }\n    bankDetails?: {\n      accountNumber?: string[]\n      ifscCode?: string[]\n      bankName?: string[]\n      accountHolderName?: string[]\n      accountType?: string[]\n      branchName?: string[]\n    }\n    developerDetails?: {\n      companyName?: string[]\n      experience?: string[]\n      licenseNumber?: string[]\n      specialization?: string[]\n    }\n  }\n  showBusinessFields?: boolean\n}\n\nconst businessTypes = [\n  'Sole Proprietorship',\n  'Partnership',\n  'Private Limited Company',\n  'Public Limited Company',\n  'Limited Liability Partnership (LLP)',\n  'One Person Company (OPC)',\n  'Trust',\n  'Society',\n  'Cooperative Society',\n  'Other'\n]\n\nexport default function BusinessInformation({ \n  value, \n  onChange, \n  errors, \n  showBusinessFields = true \n}: BusinessInformationProps) {\n  const handleInputChange = (field: string, inputValue: string | number) => {\n    const keys = field.split('.')\n    if (keys.length === 1) {\n      onChange({\n        ...value,\n        [field]: inputValue\n      })\n    } else if (keys.length === 2) {\n      const [parent, child] = keys\n      onChange({\n        ...value,\n        [parent as string]: {\n          ...(value[parent as keyof BusinessData] as any),\n          [child as string]: inputValue\n        }\n      })\n    }\n  }\n\n  // Validate GST number format\n  const validateGST = (gst: string) => {\n    const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/\n    return gstRegex.test(gst)\n  }\n\n  // Validate PAN number format\n  const validatePAN = (pan: string) => {\n    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/\n    return panRegex.test(pan)\n  }\n\n  // Validate IFSC code format\n  const validateIFSC = (ifsc: string) => {\n    const ifscRegex = /^[A-Z]{4}0[A-Z0-9]{6}$/\n    return ifscRegex.test(ifsc)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Business Information */}\n      {showBusinessFields && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Briefcase className=\"h-5 w-5\" />\n                Business Information\n              </div>\n              <Badge variant=\"secondary\" className=\"text-xs\">Optional</Badge>\n            </CardTitle>\n            <CardDescription>\n              Company details and business registration information (can be added later)\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"company\">Company Name</Label>\n                <div className=\"relative\">\n                  <Input\n                    id=\"company\"\n                    placeholder=\"Enter company name\"\n                    value={value.businessInfo?.company || ''}\n                    onChange={(e) => handleInputChange('businessInfo.company', e.target.value)}\n                    className={errors?.businessInfo?.company ? 'border-red-500' : ''}\n                  />\n                  <Building className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n                </div>\n                {errors?.businessInfo?.company && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.businessInfo.company?.[0]}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"businessType\">Business Type</Label>\n                <Select\n                  value={value.businessInfo?.businessType || ''}\n                  onValueChange={(val) => handleInputChange('businessInfo.businessType', val)}\n                >\n                  <SelectTrigger className={errors?.businessInfo?.businessType ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Select business type\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {businessTypes.map((type) => (\n                      <SelectItem key={type} value={type}>\n                        {type}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                {errors?.businessInfo?.businessType && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.businessInfo.businessType?.[0]}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <Label htmlFor=\"gstNumber\">GST Number</Label>\n                  <TooltipProvider>\n                    <Tooltip>\n                      <TooltipTrigger>\n                        <Info className=\"h-4 w-4 text-muted-foreground\" />\n                      </TooltipTrigger>\n                      <TooltipContent>\n                        <p>15-digit GST identification number</p>\n                      </TooltipContent>\n                    </Tooltip>\n                  </TooltipProvider>\n                </div>\n                <Input\n                  id=\"gstNumber\"\n                  placeholder=\"e.g., 22AAAAA0000A1Z5\"\n                  value={value.businessInfo?.gstNumber || ''}\n                  onChange={(e) => handleInputChange('businessInfo.gstNumber', e.target.value.toUpperCase())}\n                  className={errors?.businessInfo?.gstNumber ? 'border-red-500' : ''}\n                  maxLength={15}\n                />\n                {errors?.businessInfo?.gstNumber && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.businessInfo.gstNumber?.[0]}\n                  </p>\n                )}\n                {value.businessInfo?.gstNumber && !validateGST(value.businessInfo.gstNumber) && value.businessInfo.gstNumber.length === 15 && (\n                  <p className=\"text-sm text-amber-600\">\n                    Invalid GST number format\n                  </p>\n                )}\n                {value.businessInfo?.gstNumber && validateGST(value.businessInfo.gstNumber) && (\n                  <div className=\"flex items-center gap-1\">\n                    <Badge variant=\"secondary\" className=\"text-green-600\">\n                      Valid GST Format\n                    </Badge>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <Label htmlFor=\"panNumber\">PAN Number</Label>\n                  <TooltipProvider>\n                    <Tooltip>\n                      <TooltipTrigger>\n                        <Info className=\"h-4 w-4 text-muted-foreground\" />\n                      </TooltipTrigger>\n                      <TooltipContent>\n                        <p>10-character PAN (Permanent Account Number)</p>\n                      </TooltipContent>\n                    </Tooltip>\n                  </TooltipProvider>\n                </div>\n                <Input\n                  id=\"panNumber\"\n                  placeholder=\"e.g., **********\"\n                  value={value.businessInfo?.panNumber || ''}\n                  onChange={(e) => handleInputChange('businessInfo.panNumber', e.target.value.toUpperCase())}\n                  className={errors?.businessInfo?.panNumber ? 'border-red-500' : ''}\n                  maxLength={10}\n                />\n                {errors?.businessInfo?.panNumber && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.businessInfo.panNumber?.[0]}\n                  </p>\n                )}\n                {value.businessInfo?.panNumber && !validatePAN(value.businessInfo.panNumber) && value.businessInfo.panNumber.length === 10 && (\n                  <p className=\"text-sm text-amber-600\">\n                    Invalid PAN number format\n                  </p>\n                )}\n                {value.businessInfo?.panNumber && validatePAN(value.businessInfo.panNumber) && (\n                  <div className=\"flex items-center gap-1\">\n                    <Badge variant=\"secondary\" className=\"text-green-600\">\n                      Valid PAN Format\n                    </Badge>\n                  </div>\n                )}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Bank Details */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <CreditCard className=\"h-5 w-5\" />\n              Bank Details\n            </div>\n            <Badge variant=\"secondary\" className=\"text-xs\">Optional</Badge>\n          </CardTitle>\n          <CardDescription>\n            Banking information for transactions and payments (can be added later)\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"accountHolderName\">Account Holder Name *</Label>\n              <Input\n                id=\"accountHolderName\"\n                placeholder=\"Enter account holder name\"\n                value={value.bankDetails.accountHolderName}\n                onChange={(e) => handleInputChange('bankDetails.accountHolderName', e.target.value)}\n                className={errors?.bankDetails?.accountHolderName ? 'border-red-500' : ''}\n              />\n              {errors?.bankDetails?.accountHolderName && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.bankDetails.accountHolderName?.[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"bankName\">Bank Name *</Label>\n              <Input\n                id=\"bankName\"\n                placeholder=\"Enter bank name\"\n                value={value.bankDetails.bankName}\n                onChange={(e) => handleInputChange('bankDetails.bankName', e.target.value)}\n                className={errors?.bankDetails?.bankName ? 'border-red-500' : ''}\n              />\n              {errors?.bankDetails?.bankName && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.bankDetails.bankName?.[0]}\n                </p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"accountNumber\">Account Number *</Label>\n              <Input\n                id=\"accountNumber\"\n                placeholder=\"Enter account number\"\n                value={value.bankDetails.accountNumber}\n                onChange={(e) => handleInputChange('bankDetails.accountNumber', e.target.value)}\n                className={errors?.bankDetails?.accountNumber ? 'border-red-500' : ''}\n              />\n              {errors?.bankDetails?.accountNumber && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.bankDetails.accountNumber?.[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center gap-2\">\n                <Label htmlFor=\"ifscCode\">IFSC Code *</Label>\n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger>\n                      <Info className=\"h-4 w-4 text-muted-foreground\" />\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>11-character bank IFSC code</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              </div>\n              <Input\n                id=\"ifscCode\"\n                placeholder=\"e.g., SBIN0001234\"\n                value={value.bankDetails.ifscCode}\n                onChange={(e) => handleInputChange('bankDetails.ifscCode', e.target.value.toUpperCase())}\n                className={errors?.bankDetails?.ifscCode ? 'border-red-500' : ''}\n                maxLength={11}\n              />\n              {errors?.bankDetails?.ifscCode && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.bankDetails.ifscCode?.[0]}\n                </p>\n              )}\n              {value.bankDetails.ifscCode && !validateIFSC(value.bankDetails.ifscCode) && value.bankDetails.ifscCode.length === 11 && (\n                <p className=\"text-sm text-amber-600\">\n                  Invalid IFSC code format\n                </p>\n              )}\n              {value.bankDetails.ifscCode && validateIFSC(value.bankDetails.ifscCode) && (\n                <div className=\"flex items-center gap-1\">\n                  <Badge variant=\"secondary\" className=\"text-green-600\">\n                    Valid IFSC Format\n                  </Badge>\n                </div>\n              )}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;AAuEA,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS,oBAAoB,EAC1C,KAAK,EACL,QAAQ,EACR,MAAM,EACN,qBAAqB,IAAI,EACA;IACzB,MAAM,oBAAoB,CAAC,OAAe;QACxC,MAAM,OAAO,MAAM,KAAK,CAAC;QACzB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,MAAM,EAAE;YACX;QACF,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;YAC5B,MAAM,CAAC,QAAQ,MAAM,GAAG;YACxB,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,OAAiB,EAAE;oBAClB,GAAI,KAAK,CAAC,OAA6B;oBACvC,CAAC,MAAgB,EAAE;gBACrB;YACF;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,cAAc,CAAC;QACnB,MAAM,WAAW;QACjB,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,6BAA6B;IAC7B,MAAM,cAAc,CAAC;QACnB,MAAM,WAAW;QACjB,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,4BAA4B;IAC5B,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY;QAClB,OAAO,UAAU,IAAI,CAAC;IACxB;IAEA,qBACE,6WAAC;QAAI,WAAU;;YAEZ,oCACC,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;;0CACT,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,gSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGnC,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAEjD,6WAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,aAAY;wDACZ,OAAO,MAAM,YAAY,EAAE,WAAW;wDACtC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wDACzE,WAAW,QAAQ,cAAc,UAAU,mBAAmB;;;;;;kEAEhE,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;;4CAErB,QAAQ,cAAc,yBACrB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,YAAY,CAAC,OAAO,EAAE,CAAC,EAAE;;;;;;;;;;;;;kDAKvC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6WAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,MAAM,YAAY,EAAE,gBAAgB;gDAC3C,eAAe,CAAC,MAAQ,kBAAkB,6BAA6B;;kEAEvE,6WAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAW,QAAQ,cAAc,eAAe,mBAAmB;kEAChF,cAAA,6WAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6WAAC,kIAAA,CAAA,gBAAa;kEACX,cAAc,GAAG,CAAC,CAAC,qBAClB,6WAAC,kIAAA,CAAA,aAAU;gEAAY,OAAO;0EAC3B;+DADc;;;;;;;;;;;;;;;;4CAMtB,QAAQ,cAAc,8BACrB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,YAAY,CAAC,YAAY,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;0CAM9C,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,6WAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8EACN,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKX,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,YAAY,EAAE,aAAa;gDACxC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;gDACvF,WAAW,QAAQ,cAAc,YAAY,mBAAmB;gDAChE,WAAW;;;;;;4CAEZ,QAAQ,cAAc,2BACrB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE;;;;;;;4CAGtC,MAAM,YAAY,EAAE,aAAa,CAAC,YAAY,MAAM,YAAY,CAAC,SAAS,KAAK,MAAM,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,oBACtH,6WAAC;gDAAE,WAAU;0DAAyB;;;;;;4CAIvC,MAAM,YAAY,EAAE,aAAa,YAAY,MAAM,YAAY,CAAC,SAAS,mBACxE,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAiB;;;;;;;;;;;;;;;;;kDAO5D,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAY;;;;;;kEAC3B,6WAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8EACN,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKX,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,YAAY,EAAE,aAAa;gDACxC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;gDACvF,WAAW,QAAQ,cAAc,YAAY,mBAAmB;gDAChE,WAAW;;;;;;4CAEZ,QAAQ,cAAc,2BACrB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE;;;;;;;4CAGtC,MAAM,YAAY,EAAE,aAAa,CAAC,YAAY,MAAM,YAAY,CAAC,SAAS,KAAK,MAAM,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,oBACtH,6WAAC;gDAAE,WAAU;0DAAyB;;;;;;4CAIvC,MAAM,YAAY,EAAE,aAAa,YAAY,MAAM,YAAY,CAAC,SAAS,mBACxE,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpE,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;;0CACT,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGpC,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAEjD,6WAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAoB;;;;;;0DACnC,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,WAAW,CAAC,iBAAiB;gDAC1C,UAAU,CAAC,IAAM,kBAAkB,iCAAiC,EAAE,MAAM,CAAC,KAAK;gDAClF,WAAW,QAAQ,aAAa,oBAAoB,mBAAmB;;;;;;4CAExE,QAAQ,aAAa,mCACpB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,iBAAiB,EAAE,CAAC,EAAE;;;;;;;;;;;;;kDAKhD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,WAAW,CAAC,QAAQ;gDACjC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;gDACzE,WAAW,QAAQ,aAAa,WAAW,mBAAmB;;;;;;4CAE/D,QAAQ,aAAa,0BACpB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;0CAMzC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,WAAW,CAAC,aAAa;gDACtC,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,KAAK;gDAC9E,WAAW,QAAQ,aAAa,gBAAgB,mBAAmB;;;;;;4CAEpE,QAAQ,aAAa,+BACpB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,aAAa,EAAE,CAAC,EAAE;;;;;;;;;;;;;kDAK5C,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,6WAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8EACN,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6WAAC,mIAAA,CAAA,iBAAc;8EACb,cAAA,6WAAC;kFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAKX,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACZ,OAAO,MAAM,WAAW,CAAC,QAAQ;gDACjC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;gDACrF,WAAW,QAAQ,aAAa,WAAW,mBAAmB;gDAC9D,WAAW;;;;;;4CAEZ,QAAQ,aAAa,0BACpB,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE;;;;;;;4CAGpC,MAAM,WAAW,CAAC,QAAQ,IAAI,CAAC,aAAa,MAAM,WAAW,CAAC,QAAQ,KAAK,MAAM,WAAW,CAAC,QAAQ,CAAC,MAAM,KAAK,oBAChH,6WAAC;gDAAE,WAAU;0DAAyB;;;;;;4CAIvC,MAAM,WAAW,CAAC,QAAQ,IAAI,aAAa,MAAM,WAAW,CAAC,QAAQ,mBACpE,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxE", "debugId": null}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/LocationPicker.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { MapPin, Navigation, Loader2, CheckCircle, AlertCircle } from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface LocationData {\n  address: string\n  street?: string // Optional for backward compatibility\n  city: string\n  state: string\n  country: string\n  pincode: string\n  coordinates?: {\n    latitude: number\n    longitude: number\n  }\n}\n\ninterface LocationPickerProps {\n  value: LocationData\n  onChange: (location: LocationData) => void\n  errors?: {\n    address?: string[]\n    street?: string[]\n    city?: string[]\n    state?: string[]\n    country?: string[]\n    pincode?: string[]\n    coordinates?: string[]\n  }\n}\n\nexport default function LocationPicker({ value, onChange, errors }: LocationPickerProps) {\n  const [isDetectingLocation, setIsDetectingLocation] = useState(false)\n  const [locationError, setLocationError] = useState<string | null>(null)\n\n  const handleInputChange = (field: keyof LocationData, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  const detectCurrentLocation = async () => {\n    if (!navigator.geolocation) {\n      setLocationError('Geolocation is not supported by this browser')\n      toast.error('Geolocation not supported')\n      return\n    }\n\n    setIsDetectingLocation(true)\n    setLocationError(null)\n\n    navigator.geolocation.getCurrentPosition(\n      async (position) => {\n        try {\n          const { latitude, longitude } = position.coords\n\n          // First update coordinates\n          onChange({\n            ...value,\n            coordinates: { latitude, longitude }\n          })\n\n          // Perform reverse geocoding to get complete address details\n          await reverseGeocode(latitude, longitude)\n\n        } catch (error) {\n          console.error('Error getting address:', error)\n          toast.error('Could not get address from location')\n        } finally {\n          setIsDetectingLocation(false)\n        }\n      },\n      (error) => {\n        setIsDetectingLocation(false)\n        let errorMessage = 'Failed to get location'\n\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user'\n            break\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable'\n            break\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out'\n            break\n        }\n\n        setLocationError(errorMessage)\n        toast.error(errorMessage)\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 60000\n      }\n    )\n  }\n\n  // Reverse geocoding to get complete address from coordinates\n  const reverseGeocode = async (lat: number, lng: number) => {\n    try {\n      toast.info('Getting address details...', { duration: 2000 })\n\n      // Using multiple geocoding services for better accuracy\n      const geocodingServices = [\n        // Primary: BigDataCloud (free, no API key required)\n        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`,\n        // Fallback: OpenCage (requires API key but more accurate)\n        // `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lng}&key=YOUR_API_KEY&language=en&pretty=1`\n      ]\n\n      let addressData = null\n\n      for (const serviceUrl of geocodingServices) {\n        try {\n          const response = await fetch(serviceUrl)\n          if (response.ok) {\n            addressData = await response.json()\n            break\n          }\n        } catch (err) {\n          console.warn('Geocoding service failed:', err)\n          continue\n        }\n      }\n\n      if (addressData) {\n        // Extract address components from BigDataCloud response\n        const extractedAddress = extractAddressComponents(addressData, lat, lng)\n\n        // Update the form with complete address details\n        onChange(extractedAddress)\n\n        toast.success('📍 Address details auto-filled successfully!')\n      } else {\n        // Fallback: Just update coordinates\n        onChange({\n          ...value,\n          coordinates: { latitude: lat, longitude: lng }\n        })\n        toast.success('📍 Location detected! Please fill address details manually.')\n      }\n    } catch (error) {\n      console.error('Reverse geocoding error:', error)\n      // Fallback: Just fill coordinates\n      onChange({\n        ...value,\n        coordinates: { latitude: lat, longitude: lng }\n      })\n      toast.success('📍 Location coordinates detected! Please fill address details manually.')\n    }\n  }\n\n  // Extract and format address components from geocoding response\n  const extractAddressComponents = (data: any, lat: number, lng: number): LocationData => {\n    // Handle BigDataCloud response format\n    const address = [\n      data.locality,\n      data.localityInfo?.administrative?.[3]?.name,\n      data.localityInfo?.administrative?.[2]?.name,\n      data.localityInfo?.administrative?.[1]?.name\n    ].filter(Boolean).join(', ') ||\n    [data.city, data.principalSubdivision].filter(Boolean).join(', ') ||\n    data.countryName ||\n    value.address\n\n    const city = data.city ||\n                 data.locality ||\n                 data.localityInfo?.administrative?.[2]?.name ||\n                 data.localityInfo?.administrative?.[1]?.name ||\n                 value.city\n\n    const state = data.principalSubdivision ||\n                  data.principalSubdivisionCode ||\n                  data.localityInfo?.administrative?.[1]?.name ||\n                  value.state\n\n    // Enhanced pincode extraction with multiple fallbacks\n    const pincode = data.postcode ||\n                    data.postalCode ||\n                    data.localityInfo?.postcode ||\n                    data.localityInfo?.postalCode ||\n                    value.pincode\n\n    // Country detection from coordinates\n    const country = data.countryName ||\n                    data.country ||\n                    data.localityInfo?.administrative?.[0]?.name ||\n                    value.country\n\n    return {\n      address: address || value.address,\n      street: address || value.street || value.address, // Keep for backward compatibility\n      city: city || value.city,\n      state: state || value.state,\n      country: country || value.country,\n      pincode: pincode || value.pincode,\n      coordinates: { latitude: lat, longitude: lng }\n    }\n  }\n\n  const validatePincode = async (pincode: string) => {\n    if (pincode.length === 6) {\n      // Here you could integrate with a pincode API to get city/state\n      // For now, we'll just validate the format\n      const isValid = /^[0-9]{6}$/.test(pincode)\n      if (!isValid) {\n        toast.error('Invalid pincode format')\n      }\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <MapPin className=\"h-5 w-5\" />\n          Property Location\n        </CardTitle>\n        <CardDescription>\n          Enter the complete address details for the property\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Address Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"address\">Complete Address *</Label>\n          <Input\n            id=\"address\"\n            placeholder=\"Enter complete address with landmarks\"\n            value={value.address || ''}\n            onChange={(e) => handleInputChange('address', e.target.value)}\n            className={errors?.address ? 'border-red-500' : ''}\n          />\n          {errors?.address && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.address[0]}\n            </p>\n          )}\n        </div>\n\n        {/* City and State Row */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"city\">City *</Label>\n            <Input\n              id=\"city\"\n              placeholder=\"Enter city\"\n              value={value.city || ''}\n              onChange={(e) => handleInputChange('city', e.target.value)}\n              className={errors?.city ? 'border-red-500' : ''}\n            />\n            {errors?.city && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.city[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"state\">State *</Label>\n            <Input\n              id=\"state\"\n              placeholder=\"Enter state\"\n              value={value.state || ''}\n              onChange={(e) => handleInputChange('state', e.target.value)}\n              className={errors?.state ? 'border-red-500' : ''}\n            />\n            {errors?.state && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.state[0]}\n              </p>\n            )}\n          </div>\n\n          {/* Country Field */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"country\">Country *</Label>\n            <Input\n              id=\"country\"\n              placeholder=\"Enter country\"\n              value={value.country || ''}\n              onChange={(e) => handleInputChange('country', e.target.value)}\n              className={errors?.country ? 'border-red-500' : ''}\n            />\n            {errors?.country && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.country[0]}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Pincode Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"pincode\">Pincode *</Label>\n          <Input\n            id=\"pincode\"\n            placeholder=\"Enter 6-digit pincode\"\n            value={value.pincode || ''}\n            onChange={(e) => {\n              handleInputChange('pincode', e.target.value)\n              if (e.target.value.length === 6) {\n                validatePincode(e.target.value)\n              }\n            }}\n            maxLength={6}\n            className={errors?.pincode ? 'border-red-500' : ''}\n          />\n          {errors?.pincode && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.pincode[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Location Detection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label>GPS Coordinates & Auto-Fill</Label>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={detectCurrentLocation}\n              disabled={isDetectingLocation}\n              className=\"flex items-center gap-2 bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700\"\n            >\n              {isDetectingLocation ? (\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n              ) : (\n                <Navigation className=\"h-4 w-4\" />\n              )}\n              {isDetectingLocation ? 'Auto-Filling...' : 'Auto-Detect & Fill'}\n            </Button>\n          </div>\n\n          {/* Info Banner */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-emerald-50 border border-blue-200 rounded-lg p-4\">\n            <div className=\"flex items-start gap-3\">\n              <div className=\"bg-blue-500 rounded-full p-1\">\n                <Navigation className=\"h-4 w-4 text-white\" />\n              </div>\n              <div>\n                <h4 className=\"font-medium text-blue-900 mb-1\">Smart Location Detection</h4>\n                <p className=\"text-sm text-blue-700\">\n                  Click \"Auto-Detect & Fill\" to automatically detect your current location and fill all address details including:\n                </p>\n                <ul className=\"text-xs text-blue-600 mt-2 space-y-1\">\n                  <li>• Complete address with landmarks</li>\n                  <li>• City and state information</li>\n                  <li>• Postal/PIN code</li>\n                  <li>• GPS coordinates (latitude & longitude)</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {value.coordinates && (\n            <div className=\"flex items-center gap-2\">\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lat: {value.coordinates.latitude.toFixed(6)}\n              </Badge>\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lng: {value.coordinates.longitude.toFixed(6)}\n              </Badge>\n            </div>\n          )}\n\n          {locationError && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {locationError}\n            </p>\n          )}\n        </div>\n\n        {/* Manual Coordinates Input */}\n        {!value.coordinates && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"latitude\">Latitude (Optional)</Label>\n              <Input\n                id=\"latitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 28.6139\"\n                onChange={(e) => {\n                  const lat = parseFloat(e.target.value)\n                  if (!isNaN(lat)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: lat,\n                        longitude: value.coordinates?.longitude || 0\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"longitude\">Longitude (Optional)</Label>\n              <Input\n                id=\"longitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 77.2090\"\n                onChange={(e) => {\n                  const lng = parseFloat(e.target.value)\n                  if (!isNaN(lng)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: value.coordinates?.latitude || 0,\n                        longitude: lng\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAsCe,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAuB;IACrF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,oBAAoB,CAAC,OAA2B;QACpD,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,iBAAiB;QAEjB,UAAU,WAAW,CAAC,kBAAkB,CACtC,OAAO;YACL,IAAI;gBACF,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;gBAE/C,2BAA2B;gBAC3B,SAAS;oBACP,GAAG,KAAK;oBACR,aAAa;wBAAE;wBAAU;oBAAU;gBACrC;gBAEA,4DAA4D;gBAC5D,MAAM,eAAe,UAAU;YAEjC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,uBAAuB;YACzB;QACF,GACA,CAAC;YACC,uBAAuB;YACvB,IAAI,eAAe;YAEnB,OAAQ,MAAM,IAAI;gBAChB,KAAK,MAAM,iBAAiB;oBAC1B,eAAe;oBACf;gBACF,KAAK,MAAM,oBAAoB;oBAC7B,eAAe;oBACf;gBACF,KAAK,MAAM,OAAO;oBAChB,eAAe;oBACf;YACJ;YAEA,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,KAAa;QACzC,IAAI;YACF,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,8BAA8B;gBAAE,UAAU;YAAK;YAE1D,wDAAwD;YACxD,MAAM,oBAAoB;gBACxB,oDAAoD;gBACpD,CAAC,kEAAkE,EAAE,IAAI,WAAW,EAAE,IAAI,oBAAoB,CAAC;aAGhH;YAED,IAAI,cAAc;YAElB,KAAK,MAAM,cAAc,kBAAmB;gBAC1C,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM;oBAC7B,IAAI,SAAS,EAAE,EAAE;wBACf,cAAc,MAAM,SAAS,IAAI;wBACjC;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,6BAA6B;oBAC1C;gBACF;YACF;YAEA,IAAI,aAAa;gBACf,wDAAwD;gBACxD,MAAM,mBAAmB,yBAAyB,aAAa,KAAK;gBAEpE,gDAAgD;gBAChD,SAAS;gBAET,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,oCAAoC;gBACpC,SAAS;oBACP,GAAG,KAAK;oBACR,aAAa;wBAAE,UAAU;wBAAK,WAAW;oBAAI;gBAC/C;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,kCAAkC;YAClC,SAAS;gBACP,GAAG,KAAK;gBACR,aAAa;oBAAE,UAAU;oBAAK,WAAW;gBAAI;YAC/C;YACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,gEAAgE;IAChE,MAAM,2BAA2B,CAAC,MAAW,KAAa;QACxD,sCAAsC;QACtC,MAAM,UAAU;YACd,KAAK,QAAQ;YACb,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;YACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;YACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;SACzC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,SACvB;YAAC,KAAK,IAAI;YAAE,KAAK,oBAAoB;SAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,SAC5D,KAAK,WAAW,IAChB,MAAM,OAAO;QAEb,MAAM,OAAO,KAAK,IAAI,IACT,KAAK,QAAQ,IACb,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,IAAI;QAEvB,MAAM,QAAQ,KAAK,oBAAoB,IACzB,KAAK,wBAAwB,IAC7B,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,KAAK;QAEzB,sDAAsD;QACtD,MAAM,UAAU,KAAK,QAAQ,IACb,KAAK,UAAU,IACf,KAAK,YAAY,EAAE,YACnB,KAAK,YAAY,EAAE,cACnB,MAAM,OAAO;QAE7B,qCAAqC;QACrC,MAAM,UAAU,KAAK,WAAW,IAChB,KAAK,OAAO,IACZ,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,OAAO;QAE7B,OAAO;YACL,SAAS,WAAW,MAAM,OAAO;YACjC,QAAQ,WAAW,MAAM,MAAM,IAAI,MAAM,OAAO;YAChD,MAAM,QAAQ,MAAM,IAAI;YACxB,OAAO,SAAS,MAAM,KAAK;YAC3B,SAAS,WAAW,MAAM,OAAO;YACjC,SAAS,WAAW,MAAM,OAAO;YACjC,aAAa;gBAAE,UAAU;gBAAK,WAAW;YAAI;QAC/C;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,0CAA0C;YAC1C,MAAM,UAAU,aAAa,IAAI,CAAC;YAClC,IAAI,CAAC,SAAS;gBACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,8RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO,IAAI;gCACxB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,IAAI,IAAI;wCACrB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAW,QAAQ,OAAO,mBAAmB;;;;;;oCAE9C,QAAQ,sBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;0CAKrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,KAAK,IAAI;wCACtB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,QAAQ,QAAQ,mBAAmB;;;;;;oCAE/C,QAAQ,uBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;0CAMtB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;kDACzB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,OAAO,IAAI;wCACxB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAW,QAAQ,UAAU,mBAAmB;;;;;;oCAEjD,QAAQ,yBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kCAO1B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO,IAAI;gCACxB,UAAU,CAAC;oCACT,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC3C,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;wCAC/B,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAChC;gCACF;gCACA,WAAW;gCACX,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6WAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,oCACC,6WAAC,qSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAEvB,sBAAsB,oBAAoB;;;;;;;;;;;;;0CAK/C,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;8DAGrC,6WAAC;oDAAG,WAAU;;sEACZ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAMX,MAAM,WAAW,kBAChB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;kDAE3C,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;4BAK/C,+BACC,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB;;;;;;;;;;;;;oBAMN,CAAC,MAAM,WAAW,kBACjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU;wDACV,WAAW,MAAM,WAAW,EAAE,aAAa;oDAC7C;gDACF;4CACF;wCACF;;;;;;;;;;;;0CAIJ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU,MAAM,WAAW,EAAE,YAAY;wDACzC,WAAW;oDACb;gDACF;4CACF;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 4062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/s3Upload.ts"], "sourcesContent": ["// Updated S3 Upload Service - Using RTK Query API\nimport { store } from '@/store'\nimport { usersApi } from '@/store/api/usersApi'\nimport { propertiesApi } from '@/store/api/propertiesApi'\n\nexport interface UploadResult {\n  success: boolean\n  url?: string\n  key?: string\n  error?: string\n  fileKey?: string\n  publicUrl?: string\n  presignedUrl?: string\n}\n\nexport interface PresignedUrlResponse {\n  success: boolean\n  data?: {\n    presignedUrl: string\n    publicUrl: string\n    fileKey: string\n    uploadUrl: string\n    expiresIn: number\n    bucket: string\n    region: string\n    uploadInstructions?: {\n      method: string\n      headers: Record<string, string>\n      note: string\n    }\n  }\n  message?: string\n  error?: string\n}\n\n/**\n * Sanitize file name to match backend validation requirements\n */\nconst sanitizeFileName = (fileName: string): string => {\n  return fileName\n    .replace(/[<>:\"/\\\\|?*]/g, '_') // Replace dangerous characters\n    .replace(/\\s+/g, '_') // Replace spaces with underscores\n    .replace(/_{2,}/g, '_') // Replace multiple underscores with single\n    .toLowerCase() // Convert to lowercase\n}\n\n/**\n * Normalize file type to match backend expectations\n */\nconst normalizeFileType = (fileType: string): string => {\n  // Handle common MIME type variations\n  const typeMap: Record<string, string> = {\n    'image/jpg': 'image/jpeg',\n    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    'application/msword': 'application/msword'\n  }\n\n  return typeMap[fileType] || fileType\n}\n\n/**\n * Get presigned URL from server API using RTK Query\n */\nexport const getPresignedUploadUrl = async (\n  fileName: string,\n  fileType: string,\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  fileSize?: number,\n  entityId?: string\n): Promise<PresignedUrlResponse> => {\n  try {\n    // Sanitize file name to match backend validation\n    const sanitizedFileName = sanitizeFileName(fileName)\n    const normalizedFileType = normalizeFileType(fileType)\n\n    // Log upload request for debugging\n    console.log('S3 Upload Request:', {\n      fileName: sanitizedFileName,\n      fileType: normalizedFileType,\n      uploadType,\n      entityId: entityId || 'none'\n    })\n\n    let result: any\n\n    if (uploadType.startsWith('user-')) {\n      // Use user API for user-related uploads\n      const requestData: any = {\n        fileName: sanitizedFileName,\n        fileType: normalizedFileType,\n        fileSize: fileSize || 0,\n        uploadType: uploadType as 'user-document' | 'user-avatar'\n      }\n      if (entityId) {\n        requestData.userId = entityId\n      }\n\n      result = await store.dispatch(\n        usersApi.endpoints.getUserPresignedUrl.initiate(requestData)\n      ).unwrap()\n    } else {\n      // Use properties API for property-related uploads\n      const requestData: any = {\n        fileName: sanitizedFileName,\n        fileType: normalizedFileType,\n        fileSize: fileSize || 0,\n        uploadType: uploadType as 'property-image' | 'property-document'\n      }\n      if (entityId) {\n        requestData.propertyId = entityId\n      }\n\n      result = await store.dispatch(\n        propertiesApi.endpoints.getPresignedUrl.initiate(requestData)\n      ).unwrap()\n    }\n\n    return {\n      success: true,\n      data: result.data,\n      message: result.message\n    }\n  } catch (error: any) {\n    console.error('Presigned URL error:', error?.data?.message || error.message)\n    return {\n      success: false,\n      error: error?.data?.message || error.message || 'Failed to generate upload URL',\n    }\n  }\n}\n\n/**\n * Upload file to S3 using presigned URL\n */\nexport const uploadFileToS3 = async (\n  file: File,\n  presignedUrl: string\n): Promise<UploadResult> => {\n  try {\n    const response = await fetch(presignedUrl, {\n      method: 'PUT',\n      body: file,\n      headers: {\n        'Content-Type': file.type,\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Upload failed with status ${response.status}`)\n    }\n\n    return {\n      success: true,\n      url: presignedUrl,\n    }\n  } catch (error) {\n    console.error('S3 upload error:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Upload failed',\n    }\n  }\n}\n\n/**\n * Complete file upload process (get presigned URL + upload + confirm)\n */\nexport const uploadFileComplete = async (\n  file: File,\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  entityId?: string\n): Promise<UploadResult> => {\n  try {\n    // Step 1: Get presigned URL\n    const presignedResponse = await getPresignedUploadUrl(\n      file.name,\n      file.type,\n      uploadType,\n      file.size,\n      entityId\n    )\n\n    if (!presignedResponse.success || !presignedResponse.data) {\n      throw new Error(presignedResponse.error || 'Failed to get presigned URL')\n    }\n\n    // Step 2: Upload file to S3\n    const uploadResponse = await uploadFileToS3(file, presignedResponse.data.presignedUrl)\n\n    if (!uploadResponse.success) {\n      throw new Error(uploadResponse.error || 'Failed to upload file')\n    }\n\n    // Step 3: Confirm upload with backend\n    let confirmResult: any = null\n    const sanitizedFileNameForConfirm = sanitizeFileName(file.name)\n    const normalizedFileTypeForConfirm = normalizeFileType(file.type)\n\n    if (uploadType.startsWith('user-')) {\n      const confirmData: any = {\n        fileKey: presignedResponse.data.fileKey,\n        fileName: sanitizedFileNameForConfirm,\n        fileType: normalizedFileTypeForConfirm,\n        uploadType: uploadType as 'user-document' | 'user-avatar'\n      }\n      if (entityId) {\n        confirmData.userId = entityId\n      }\n\n      confirmResult = await store.dispatch(\n        usersApi.endpoints.confirmUserFileUpload.initiate(confirmData)\n      ).unwrap()\n    } else {\n      const confirmData: any = {\n        fileKey: presignedResponse.data.fileKey,\n        fileName: sanitizedFileNameForConfirm,\n        fileType: normalizedFileTypeForConfirm,\n        uploadType: uploadType as 'property-image' | 'property-document'\n      }\n      if (entityId) {\n        confirmData.propertyId = entityId\n      }\n\n      confirmResult = await store.dispatch(\n        propertiesApi.endpoints.confirmFileUpload.initiate(confirmData)\n      ).unwrap()\n    }\n\n    return {\n      success: true,\n      url: confirmResult?.data?.fileUrl || presignedResponse.data.publicUrl,\n      key: presignedResponse.data.fileKey,\n      fileKey: presignedResponse.data.fileKey,\n      publicUrl: confirmResult?.data?.fileUrl || presignedResponse.data.publicUrl,\n      presignedUrl: presignedResponse.data.presignedUrl,\n    }\n  } catch (error: any) {\n    console.error('Complete upload error:', error)\n    return {\n      success: false,\n      error: error?.data?.message || error.message || 'Upload failed',\n    }\n  }\n}\n\n/**\n * Upload multiple files to S3\n */\nexport const uploadMultipleFiles = async (\n  files: File[],\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document' = 'property-image',\n  entityId?: string\n): Promise<UploadResult[]> => {\n  const uploadPromises = files.map(file => uploadFileComplete(file, uploadType, entityId))\n  return Promise.all(uploadPromises)\n}\n\n/**\n * Upload property images with optimization\n */\nexport const uploadPropertyImages = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'property-image')\n}\n\n/**\n * Upload property documents\n */\nexport const uploadPropertyDocuments = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'property-document')\n}\n\n/**\n * Upload user avatar\n */\nexport const uploadUserAvatar = async (file: File): Promise<UploadResult> => {\n  return uploadFileComplete(file, 'user-avatar')\n}\n\n/**\n * Upload user documents\n */\nexport const uploadUserDocuments = async (files: File[]): Promise<UploadResult[]> => {\n  return uploadMultipleFiles(files, 'user-document')\n}\n\n/**\n * Check S3 service health\n */\nexport const checkS3Health = async (): Promise<{ success: boolean; data?: any; error?: string }> => {\n  try {\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'\n    const response = await fetch(`${API_BASE_URL}/s3/health`)\n\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`)\n    }\n\n    const data = await response.json()\n    return data\n  } catch (error) {\n    console.error('S3 health check error:', error)\n    return {\n      success: false,\n      error: error instanceof Error ? error.message : 'Health check failed',\n    }\n  }\n}\n\n/**\n * Get file type information for preview purposes\n */\nexport const getFileTypeInfo = (fileName: string) => {\n  const extension = fileName.split('.').pop()?.toLowerCase() || ''\n\n  const typeMap = {\n    // Images\n    jpg: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    jpeg: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    png: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n    webp: { type: 'image', icon: '🖼️', color: 'text-green-600', bgColor: 'bg-green-100' },\n\n    // Documents\n    pdf: { type: 'document', icon: '📄', color: 'text-red-600', bgColor: 'bg-red-100', label: 'PDF' },\n    doc: { type: 'document', icon: '📝', color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'DOC' },\n    docx: { type: 'document', icon: '📝', color: 'text-blue-600', bgColor: 'bg-blue-100', label: 'DOCX' },\n    xls: { type: 'document', icon: '📊', color: 'text-green-600', bgColor: 'bg-green-100', label: 'XLS' },\n    xlsx: { type: 'document', icon: '📊', color: 'text-green-600', bgColor: 'bg-green-100', label: 'XLSX' },\n\n    // Default\n    default: { type: 'file', icon: '📁', color: 'text-gray-600', bgColor: 'bg-gray-100', label: extension.toUpperCase() }\n  }\n\n  return typeMap[extension as keyof typeof typeMap] || typeMap.default\n}\n\n/**\n * Check if file type supports inline preview\n */\nexport const supportsInlinePreview = (fileName: string): boolean => {\n  const fileInfo = getFileTypeInfo(fileName)\n  return fileInfo.type === 'image'\n}\n\n/**\n * Validate file type and size\n */\nexport const validateFile = (\n  file: File,\n  allowedTypes: string[] = [],\n  maxSizeMB: number = 10\n): { valid: boolean; error?: string } => {\n  // Check file size\n  const maxSizeBytes = maxSizeMB * 1024 * 1024\n  if (file.size > maxSizeBytes) {\n    return {\n      valid: false,\n      error: `File size must be less than ${maxSizeMB}MB`,\n    }\n  }\n\n  // Check file type if specified\n  if (allowedTypes.length > 0) {\n    const fileType = file.type.toLowerCase()\n    const isValidType = allowedTypes.some(type => \n      fileType.includes(type.toLowerCase()) || \n      file.name.toLowerCase().endsWith(type.toLowerCase())\n    )\n\n    if (!isValidType) {\n      return {\n        valid: false,\n        error: `File type must be one of: ${allowedTypes.join(', ')}`,\n      }\n    }\n  }\n\n  return { valid: true }\n}\n\n/**\n * Validate image file\n */\nexport const validateImageFile = (file: File): { valid: boolean; error?: string } => {\n  return validateFile(file, ['jpg', 'jpeg', 'png', 'webp', 'gif'], 5)\n}\n\n/**\n * Validate document file\n */\nexport const validateDocumentFile = (file: File): { valid: boolean; error?: string } => {\n  return validateFile(file, ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'], 10)\n}\n\n/**\n * Get file preview URL\n */\nexport const getFilePreviewUrl = (file: File): string => {\n  return URL.createObjectURL(file)\n}\n\n/**\n * Format file size for display\n */\nexport const formatFileSize = (bytes: number): string => {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * Get file type icon\n */\nexport const getFileTypeIcon = (fileName: string): string => {\n  const extension = fileName.split('.').pop()?.toLowerCase()\n  \n  switch (extension) {\n    case 'pdf':\n      return '📄'\n    case 'doc':\n    case 'docx':\n      return '📝'\n    case 'xls':\n    case 'xlsx':\n      return '📊'\n    case 'jpg':\n    case 'jpeg':\n    case 'png':\n    case 'gif':\n    case 'webp':\n      return '🖼️'\n    default:\n      return '📎'\n  }\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;;;;;;;;;;;;;;;AAClD;AACA;AACA;;;;AAgCA;;CAEC,GACD,MAAM,mBAAmB,CAAC;IACxB,OAAO,SACJ,OAAO,CAAC,iBAAiB,KAAK,+BAA+B;KAC7D,OAAO,CAAC,QAAQ,KAAK,kCAAkC;KACvD,OAAO,CAAC,UAAU,KAAK,2CAA2C;KAClE,WAAW,GAAG,uBAAuB;;AAC1C;AAEA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACzB,qCAAqC;IACrC,MAAM,UAAkC;QACtC,aAAa;QACb,2EAA2E;QAC3E,sBAAsB;IACxB;IAEA,OAAO,OAAO,CAAC,SAAS,IAAI;AAC9B;AAKO,MAAM,wBAAwB,OACnC,UACA,UACA,aAAuF,gBAAgB,EACvG,UACA;IAEA,IAAI;QACF,iDAAiD;QACjD,MAAM,oBAAoB,iBAAiB;QAC3C,MAAM,qBAAqB,kBAAkB;QAE7C,mCAAmC;QACnC,QAAQ,GAAG,CAAC,sBAAsB;YAChC,UAAU;YACV,UAAU;YACV;YACA,UAAU,YAAY;QACxB;QAEA,IAAI;QAEJ,IAAI,WAAW,UAAU,CAAC,UAAU;YAClC,wCAAwC;YACxC,MAAM,cAAmB;gBACvB,UAAU;gBACV,UAAU;gBACV,UAAU,YAAY;gBACtB,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,MAAM,GAAG;YACvB;YAEA,SAAS,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAC3B,+HAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,CAAC,cAChD,MAAM;QACV,OAAO;YACL,kDAAkD;YAClD,MAAM,cAAmB;gBACvB,UAAU;gBACV,UAAU;gBACV,UAAU,YAAY;gBACtB,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,UAAU,GAAG;YAC3B;YAEA,SAAS,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAC3B,oIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,cACjD,MAAM;QACV;QAEA,OAAO;YACL,SAAS;YACT,MAAM,OAAO,IAAI;YACjB,SAAS,OAAO,OAAO;QACzB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wBAAwB,OAAO,MAAM,WAAW,MAAM,OAAO;QAC3E,OAAO;YACL,SAAS;YACT,OAAO,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI;QAClD;IACF;AACF;AAKO,MAAM,iBAAiB,OAC5B,MACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,cAAc;YACzC,QAAQ;YACR,MAAM;YACN,SAAS;gBACP,gBAAgB,KAAK,IAAI;YAC3B;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,MAAM,EAAE;QAChE;QAEA,OAAO;YACL,SAAS;YACT,KAAK;QACP;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAKO,MAAM,qBAAqB,OAChC,MACA,aAAuF,gBAAgB,EACvG;IAEA,IAAI;QACF,4BAA4B;QAC5B,MAAM,oBAAoB,MAAM,sBAC9B,KAAK,IAAI,EACT,KAAK,IAAI,EACT,YACA,KAAK,IAAI,EACT;QAGF,IAAI,CAAC,kBAAkB,OAAO,IAAI,CAAC,kBAAkB,IAAI,EAAE;YACzD,MAAM,IAAI,MAAM,kBAAkB,KAAK,IAAI;QAC7C;QAEA,4BAA4B;QAC5B,MAAM,iBAAiB,MAAM,eAAe,MAAM,kBAAkB,IAAI,CAAC,YAAY;QAErF,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,MAAM,IAAI,MAAM,eAAe,KAAK,IAAI;QAC1C;QAEA,sCAAsC;QACtC,IAAI,gBAAqB;QACzB,MAAM,8BAA8B,iBAAiB,KAAK,IAAI;QAC9D,MAAM,+BAA+B,kBAAkB,KAAK,IAAI;QAEhE,IAAI,WAAW,UAAU,CAAC,UAAU;YAClC,MAAM,cAAmB;gBACvB,SAAS,kBAAkB,IAAI,CAAC,OAAO;gBACvC,UAAU;gBACV,UAAU;gBACV,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,MAAM,GAAG;YACvB;YAEA,gBAAgB,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAClC,+HAAA,CAAA,WAAQ,CAAC,SAAS,CAAC,qBAAqB,CAAC,QAAQ,CAAC,cAClD,MAAM;QACV,OAAO;YACL,MAAM,cAAmB;gBACvB,SAAS,kBAAkB,IAAI,CAAC,OAAO;gBACvC,UAAU;gBACV,UAAU;gBACV,YAAY;YACd;YACA,IAAI,UAAU;gBACZ,YAAY,UAAU,GAAG;YAC3B;YAEA,gBAAgB,MAAM,qHAAA,CAAA,QAAK,CAAC,QAAQ,CAClC,oIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,iBAAiB,CAAC,QAAQ,CAAC,cACnD,MAAM;QACV;QAEA,OAAO;YACL,SAAS;YACT,KAAK,eAAe,MAAM,WAAW,kBAAkB,IAAI,CAAC,SAAS;YACrE,KAAK,kBAAkB,IAAI,CAAC,OAAO;YACnC,SAAS,kBAAkB,IAAI,CAAC,OAAO;YACvC,WAAW,eAAe,MAAM,WAAW,kBAAkB,IAAI,CAAC,SAAS;YAC3E,cAAc,kBAAkB,IAAI,CAAC,YAAY;QACnD;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,OAAO,MAAM,WAAW,MAAM,OAAO,IAAI;QAClD;IACF;AACF;AAKO,MAAM,sBAAsB,OACjC,OACA,aAAuF,gBAAgB,EACvG;IAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,mBAAmB,MAAM,YAAY;IAC9E,OAAO,QAAQ,GAAG,CAAC;AACrB;AAKO,MAAM,uBAAuB,OAAO;IACzC,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,0BAA0B,OAAO;IAC5C,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,mBAAmB,OAAO;IACrC,OAAO,mBAAmB,MAAM;AAClC;AAKO,MAAM,sBAAsB,OAAO;IACxC,OAAO,oBAAoB,OAAO;AACpC;AAKO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,eAAe,iEAAmC;QACxD,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,CAAC;QAExD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IAE9D,MAAM,UAAU;QACd,SAAS;QACT,KAAK;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACpF,MAAM;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACrF,KAAK;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QACpF,MAAM;YAAE,MAAM;YAAS,MAAM;YAAO,OAAO;YAAkB,SAAS;QAAe;QAErF,YAAY;QACZ,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAgB,SAAS;YAAc,OAAO;QAAM;QAChG,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO;QAAM;QAClG,MAAM;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO;QAAO;QACpG,KAAK;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAkB,SAAS;YAAgB,OAAO;QAAM;QACpG,MAAM;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;YAAkB,SAAS;YAAgB,OAAO;QAAO;QAEtG,UAAU;QACV,SAAS;YAAE,MAAM;YAAQ,MAAM;YAAM,OAAO;YAAiB,SAAS;YAAe,OAAO,UAAU,WAAW;QAAG;IACtH;IAEA,OAAO,OAAO,CAAC,UAAkC,IAAI,QAAQ,OAAO;AACtE;AAKO,MAAM,wBAAwB,CAAC;IACpC,MAAM,WAAW,gBAAgB;IACjC,OAAO,SAAS,IAAI,KAAK;AAC3B;AAKO,MAAM,eAAe,CAC1B,MACA,eAAyB,EAAE,EAC3B,YAAoB,EAAE;IAEtB,kBAAkB;IAClB,MAAM,eAAe,YAAY,OAAO;IACxC,IAAI,KAAK,IAAI,GAAG,cAAc;QAC5B,OAAO;YACL,OAAO;YACP,OAAO,CAAC,4BAA4B,EAAE,UAAU,EAAE,CAAC;QACrD;IACF;IAEA,+BAA+B;IAC/B,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW;QACtC,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,OACpC,SAAS,QAAQ,CAAC,KAAK,WAAW,OAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,WAAW;QAGnD,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,OAAO;gBACP,OAAO,CAAC,0BAA0B,EAAE,aAAa,IAAI,CAAC,OAAO;YAC/D;QACF;IACF;IAEA,OAAO;QAAE,OAAO;IAAK;AACvB;AAKO,MAAM,oBAAoB,CAAC;IAChC,OAAO,aAAa,MAAM;QAAC;QAAO;QAAQ;QAAO;QAAQ;KAAM,EAAE;AACnE;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,aAAa,MAAM;QAAC;QAAO;QAAO;QAAQ;QAAO;QAAQ;QAAO;QAAQ;KAAM,EAAE;AACzF;AAKO,MAAM,oBAAoB,CAAC;IAChC,OAAO,IAAI,eAAe,CAAC;AAC7B;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAKO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAE7C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 4476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/FileUpload/EnhancedS3Upload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { toast } from 'sonner'\nimport { Upload, X, FileText, Image, Video, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  uploadFileComplete, \n  uploadMultipleFiles, \n  validateImageFile, \n  validateDocumentFile,\n  formatFileSize,\n  getFileTypeIcon \n} from '@/lib/s3Upload'\n\ninterface EnhancedS3UploadProps {\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document'\n  entityId?: string // propertyId or userId depending on uploadType\n  onUploadComplete?: (results: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => void\n  onUploadError?: (error: string) => void\n  maxFiles?: number\n  disabled?: boolean\n  className?: string\n  showPreview?: boolean\n  allowMultiple?: boolean\n}\n\ninterface UploadingFile {\n  file: File\n  progress: number\n  status: 'uploading' | 'success' | 'error'\n  fileKey?: string\n  publicUrl?: string\n  error?: string\n  id: string\n}\n\nconst EnhancedS3Upload: React.FC<EnhancedS3UploadProps> = ({\n  uploadType,\n  entityId,\n  onUploadComplete,\n  onUploadError,\n  maxFiles = 5,\n  disabled = false,\n  className = '',\n  showPreview = true,\n  allowMultiple = true,\n}) => {\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])\n  const [isUploading, setIsUploading] = useState(false)\n\n  const getFileTypeValidation = () => {\n    const validations: Record<string, { accept: Record<string, string[]>; maxSize: number }> = {\n      'property-image': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 10 * 1024 * 1024, // 10MB\n      },\n      'property-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'application/msword': ['.doc', '.docx'],\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'image/*': ['.jpg', '.jpeg', '.png'] \n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-avatar': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 2 * 1024 * 1024, // 2MB\n      },\n    }\n    return validations[uploadType] || validations['property-image']\n  }\n\n  const validateFile = (file: File): { valid: boolean; error?: string } => {\n    if (uploadType.includes('image') || uploadType === 'user-avatar') {\n      return validateImageFile(file)\n    } else {\n      return validateDocumentFile(file)\n    }\n  }\n\n  const uploadFileWithProgress = async (file: File, fileId: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Simulate progress for presigned URL upload\n      let progress = 0\n      const progressInterval = setInterval(() => {\n        progress += Math.random() * 30\n        if (progress > 90) progress = 90\n        \n        setUploadingFiles(prev =>\n          prev.map(f => f.id === fileId ? { ...f, progress: Math.round(progress) } : f)\n        )\n      }, 200)\n\n      uploadFileComplete(file, uploadType, entityId)\n        .then(result => {\n          clearInterval(progressInterval)\n          \n          if (result.success) {\n            setUploadingFiles(prev =>\n              prev.map(f => f.id === fileId ? {\n                ...f,\n                progress: 100,\n                status: 'success' as const,\n                ...(result.fileKey && { fileKey: result.fileKey }),\n                ...(result.publicUrl && { publicUrl: result.publicUrl }),\n              } : f)\n            )\n            resolve()\n          } else {\n            throw new Error(result.error || 'Upload failed')\n          }\n        })\n        .catch(error => {\n          clearInterval(progressInterval)\n          setUploadingFiles(prev =>\n            prev.map(f => f.id === fileId ? {\n              ...f,\n              status: 'error' as const,\n              error: error.message,\n            } : f)\n          )\n          reject(error)\n        })\n    })\n  }\n\n  const handleFileUpload = async (files: File[]) => {\n    if (disabled || isUploading) return\n\n    setIsUploading(true)\n    const newFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading' as const,\n      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n    }))\n\n    // Validate files\n    const validFiles: UploadingFile[] = []\n    const invalidFiles: string[] = []\n\n    newFiles.forEach(fileData => {\n      const validation = validateFile(fileData.file)\n      if (validation.valid) {\n        validFiles.push(fileData)\n      } else {\n        invalidFiles.push(`${fileData.file.name}: ${validation.error}`)\n      }\n    })\n\n    if (invalidFiles.length > 0) {\n      toast.error(`Invalid files: ${invalidFiles.join(', ')}`)\n      setIsUploading(false)\n      return\n    }\n\n    // Add files to uploading list\n    setUploadingFiles(prev => [...prev, ...validFiles])\n\n    try {\n      // Upload files\n      await Promise.all(\n        validFiles.map(fileData => \n          uploadFileWithProgress(fileData.file, fileData.id)\n        )\n      )\n\n      // Get successful uploads\n      const successfulUploads = uploadingFiles\n        .concat(validFiles)\n        .filter(f => f.status === 'success' && f.fileKey && f.publicUrl)\n        .map(f => ({\n          fileKey: f.fileKey!,\n          publicUrl: f.publicUrl!,\n          fileName: f.file.name,\n        }))\n\n      if (successfulUploads.length > 0) {\n        onUploadComplete?.(successfulUploads)\n        toast.success(`${successfulUploads.length} file(s) uploaded successfully`)\n      }\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Upload failed'\n      onUploadError?.(errorMessage)\n      toast.error(`Upload failed: ${errorMessage}`)\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const filesToUpload = allowMultiple ? acceptedFiles : acceptedFiles.slice(0, 1)\n    handleFileUpload(filesToUpload)\n  }, [uploadType, allowMultiple, disabled, isUploading])\n\n  const validation = getFileTypeValidation()\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: validation?.accept || {},\n    maxSize: validation?.maxSize || 10 * 1024 * 1024,\n    maxFiles: allowMultiple ? maxFiles : 1,\n    disabled: disabled || isUploading,\n    multiple: allowMultiple,\n  })\n\n  const removeFile = (fileId: string) => {\n    setUploadingFiles(prev => prev.filter(f => f.id !== fileId))\n  }\n\n  const getFileIcon = (file: File) => {\n    if (file.type.startsWith('image/')) return <Image className=\"w-4 h-4\" />\n    if (file.type.startsWith('video/')) return <Video className=\"w-4 h-4\" />\n    return <FileText className=\"w-4 h-4\" />\n  }\n\n  const getStatusIcon = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Loader2 className=\"w-4 h-4 animate-spin text-blue-500\" />\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      default:\n        return null\n    }\n  }\n\n  const getStatusBadge = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Badge variant=\"secondary\">Uploading</Badge>\n      case 'success':\n        return <Badge variant=\"default\" className=\"bg-green-500\">Success</Badge>\n      case 'error':\n        return <Badge variant=\"destructive\">Error</Badge>\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Dropzone */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-all duration-200\n          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n          ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}\n          min-h-[100px] max-h-[150px] flex flex-col justify-center items-center\n        `}\n      >\n        <input {...getInputProps()} />\n        <Upload className={`w-6 h-6 mx-auto mb-1 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} />\n        <p className=\"text-xs text-gray-600 font-medium\">\n          {isDragActive\n            ? 'Drop files here...'\n            : `Drag & drop ${allowMultiple ? 'files' : 'a file'} here, or click to select`\n          }\n        </p>\n        <p className=\"text-xs text-gray-500 mt-1\">\n          Max size: {validation ? Math.round(validation.maxSize / (1024 * 1024)) : 10}MB\n          {allowMultiple && ` • Max files: ${maxFiles}`}\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">\n          Supported: {validation ? Object.values(validation.accept).flat().join(', ') : 'All files'}\n        </p>\n      </div>\n\n      {/* Uploading files list */}\n      {uploadingFiles.length > 0 && (\n        <div className=\"space-y-2 max-h-[200px] overflow-y-auto\">\n          <h4 className=\"text-xs font-medium text-gray-700\">\n            Upload Progress ({uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''})\n          </h4>\n          {uploadingFiles.map((uploadingFile) => (\n            <div key={uploadingFile.id} className=\"flex items-center space-x-2 p-2 bg-gray-50 rounded border\">\n              {getFileIcon(uploadingFile.file)}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"text-xs font-medium text-gray-900 truncate\">\n                    {uploadingFile.file.name}\n                  </p>\n                  <div className=\"flex items-center space-x-1\">\n                    {getStatusIcon(uploadingFile.status)}\n                    {getStatusBadge(uploadingFile.status)}\n                  </div>\n                </div>\n                <p className=\"text-xs text-gray-500\">\n                  {formatFileSize(uploadingFile.file.size)}\n                </p>\n                {uploadingFile.status === 'uploading' && (\n                  <div className=\"flex items-center space-x-2 mt-1\">\n                    <Progress value={uploadingFile.progress} className=\"flex-1 h-1\" />\n                    <span className=\"text-xs text-gray-500 min-w-[2rem]\">\n                      {uploadingFile.progress}%\n                    </span>\n                  </div>\n                )}\n                {uploadingFile.status === 'success' && uploadingFile.publicUrl && (\n                  <div className=\"mt-2 space-y-2\">\n                    {/* Preview for images */}\n                    {uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg overflow-hidden bg-gray-100 border\">\n                          <img\n                            src={uploadingFile.publicUrl}\n                            alt={uploadingFile.file.name}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              const target = e.target as HTMLImageElement;\n                              target.style.display = 'none';\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            View full image\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            Image uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Preview for documents */}\n                    {!uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg bg-gray-100 border flex items-center justify-center\">\n                          {uploadingFile.file.type === 'application/pdf' ? (\n                            <FileText className=\"w-8 h-8 text-red-500\" />\n                          ) : uploadingFile.file.type.includes('word') ? (\n                            <FileText className=\"w-8 h-8 text-blue-500\" />\n                          ) : (\n                            <FileText className=\"w-8 h-8 text-gray-500\" />\n                          )}\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            Download document\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            {uploadingFile.file.type === 'application/pdf' ? 'PDF Document' :\n                             uploadingFile.file.type.includes('word') ? 'Word Document' :\n                             'Document'} uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n                {uploadingFile.status === 'error' && uploadingFile.error && (\n                  <p className=\"text-xs text-red-600 mt-1 bg-red-50 p-2 rounded\">\n                    {uploadingFile.error}\n                  </p>\n                )}\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => removeFile(uploadingFile.id)}\n                className=\"text-gray-400 hover:text-gray-600\"\n                disabled={uploadingFile.status === 'uploading'}\n              >\n                <X className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default EnhancedS3Upload\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAwCA,MAAM,mBAAoD,CAAC,EACzD,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACb,WAAW,CAAC,EACZ,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACrB;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,MAAM,cAAqF;YACzF,kBAAkB;gBAChB,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,KAAK,OAAO;YACvB;YACA,qBAAqB;gBACnB,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,sBAAsB;wBAAC;wBAAQ;qBAAQ;oBACvC,2EAA2E;wBAAC;qBAAQ;gBACtF;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,iBAAiB;gBACf,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,WAAW;wBAAC;wBAAQ;wBAAS;qBAAO;gBACtC;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,eAAe;gBACb,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,IAAI,OAAO;YACtB;QACF;QACA,OAAO,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,iBAAiB;IACjE;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,WAAW,QAAQ,CAAC,YAAY,eAAe,eAAe;YAChE,OAAO,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,OAAO;YACL,OAAO,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC9B;IACF;IAEA,MAAM,yBAAyB,OAAO,MAAY;QAChD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,6CAA6C;YAC7C,IAAI,WAAW;YACf,MAAM,mBAAmB,YAAY;gBACnC,YAAY,KAAK,MAAM,KAAK;gBAC5B,IAAI,WAAW,IAAI,WAAW;gBAE9B,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAAE,GAAG,CAAC;4BAAE,UAAU,KAAK,KAAK,CAAC;wBAAU,IAAI;YAE/E,GAAG;YAEH,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,YAAY,UAClC,IAAI,CAAC,CAAA;gBACJ,cAAc;gBAEd,IAAI,OAAO,OAAO,EAAE;oBAClB,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;gCAC9B,GAAG,CAAC;gCACJ,UAAU;gCACV,QAAQ;gCACR,GAAI,OAAO,OAAO,IAAI;oCAAE,SAAS,OAAO,OAAO;gCAAC,CAAC;gCACjD,GAAI,OAAO,SAAS,IAAI;oCAAE,WAAW,OAAO,SAAS;gCAAC,CAAC;4BACzD,IAAI;oBAEN;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;gBAClC;YACF,GACC,KAAK,CAAC,CAAA;gBACL,cAAc;gBACd,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAC9B,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO,MAAM,OAAO;wBACtB,IAAI;gBAEN,OAAO;YACT;QACJ;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,YAAY,aAAa;QAE7B,eAAe;QACf,MAAM,WAA4B,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACnD;gBACA,UAAU;gBACV,QAAQ;gBACR,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAChE,CAAC;QAED,iBAAiB;QACjB,MAAM,aAA8B,EAAE;QACtC,MAAM,eAAyB,EAAE;QAEjC,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,aAAa,aAAa,SAAS,IAAI;YAC7C,IAAI,WAAW,KAAK,EAAE;gBACpB,WAAW,IAAI,CAAC;YAClB,OAAO;gBACL,aAAa,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;YAChE;QACF;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,aAAa,IAAI,CAAC,OAAO;YACvD,eAAe;YACf;QACF;QAEA,8BAA8B;QAC9B,kBAAkB,CAAA,OAAQ;mBAAI;mBAAS;aAAW;QAElD,IAAI;YACF,eAAe;YACf,MAAM,QAAQ,GAAG,CACf,WAAW,GAAG,CAAC,CAAA,WACb,uBAAuB,SAAS,IAAI,EAAE,SAAS,EAAE;YAIrD,yBAAyB;YACzB,MAAM,oBAAoB,eACvB,MAAM,CAAC,YACP,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,OAAO,IAAI,EAAE,SAAS,EAC9D,GAAG,CAAC,CAAA,IAAK,CAAC;oBACT,SAAS,EAAE,OAAO;oBAClB,WAAW,EAAE,SAAS;oBACtB,UAAU,EAAE,IAAI,CAAC,IAAI;gBACvB,CAAC;YAEH,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,mBAAmB;gBACnB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,kBAAkB,MAAM,CAAC,8BAA8B,CAAC;YAC3E;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,gBAAgB;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,cAAc;QAC9C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,gBAAgB,gBAAgB,gBAAgB,cAAc,KAAK,CAAC,GAAG;QAC7E,iBAAiB;IACnB,GAAG;QAAC;QAAY;QAAe;QAAU;KAAY;IAErD,MAAM,aAAa;IACnB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6PAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ,YAAY,UAAU,CAAC;QAC/B,SAAS,YAAY,WAAW,KAAK,OAAO;QAC5C,UAAU,gBAAgB,WAAW;QACrC,UAAU,YAAY;QACtB,UAAU;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,qBAAO,6WAAC,kSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,qSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6WAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAe;;;;;;YAC3D,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6WAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eAAe,+BAA+B,wCAAwC;UACxF,EAAE,YAAY,cAAc,kCAAkC,GAAG;;QAEnE,CAAC;;kCAED,6WAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6WAAC,0RAAA,CAAA,SAAM;wBAAC,WAAW,CAAC,qBAAqB,EAAE,eAAe,kBAAkB,iBAAiB;;;;;;kCAC7F,6WAAC;wBAAE,WAAU;kCACV,eACG,uBACA,CAAC,YAAY,EAAE,gBAAgB,UAAU,SAAS,yBAAyB,CAAC;;;;;;kCAGlF,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC7B,aAAa,KAAK,KAAK,CAAC,WAAW,OAAO,GAAG,CAAC,OAAO,IAAI,KAAK;4BAAG;4BAC3E,iBAAiB,CAAC,cAAc,EAAE,UAAU;;;;;;;kCAE/C,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC5B,aAAa,OAAO,MAAM,CAAC,WAAW,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ;;;;;;;;;;;;;YAKjF,eAAe,MAAM,GAAG,mBACvB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;;4BAAoC;4BAC9B,eAAe,MAAM;4BAAC;4BAAM,eAAe,MAAM,KAAK,IAAI,MAAM;4BAAG;;;;;;;oBAEtF,eAAe,GAAG,CAAC,CAAC,8BACnB,6WAAC;4BAA2B,WAAU;;gCACnC,YAAY,cAAc,IAAI;8CAC/B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DACV,cAAc,IAAI,CAAC,IAAI;;;;;;8DAE1B,6WAAC;oDAAI,WAAU;;wDACZ,cAAc,cAAc,MAAM;wDAClC,eAAe,cAAc,MAAM;;;;;;;;;;;;;sDAGxC,6WAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,IAAI,CAAC,IAAI;;;;;;wCAExC,cAAc,MAAM,KAAK,6BACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO,cAAc,QAAQ;oDAAE,WAAU;;;;;;8DACnD,6WAAC;oDAAK,WAAU;;wDACb,cAAc,QAAQ;wDAAC;;;;;;;;;;;;;wCAI7B,cAAc,MAAM,KAAK,aAAa,cAAc,SAAS,kBAC5D,6WAAC;4CAAI,WAAU;;gDAEZ,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BAClC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEACC,KAAK,cAAc,SAAS;gEAC5B,KAAK,cAAc,IAAI,CAAC,IAAI;gEAC5B,WAAU;gEACV,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gEACzB;;;;;;;;;;;sEAGJ,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;gDAQ/C,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BACnC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACZ,cAAc,IAAI,CAAC,IAAI,KAAK,kCAC3B,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;uEAClB,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,wBACnC,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;qFAEpB,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAGxB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;;wEACV,cAAc,IAAI,CAAC,IAAI,KAAK,oBAAoB,iBAChD,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,kBAC3C;wEAAW;;;;;;;;;;;;;;;;;;;;;;;;;wCAOvB,cAAc,MAAM,KAAK,WAAW,cAAc,KAAK,kBACtD,6WAAC;4CAAE,WAAU;sDACV,cAAc,KAAK;;;;;;;;;;;;8CAI1B,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,cAAc,EAAE;oCAC1C,WAAU;oCACV,UAAU,cAAc,MAAM,KAAK;8CAEnC,cAAA,6WAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BAnGP,cAAc,EAAE;;;;;;;;;;;;;;;;;AA2GtC;uCAEe", "debugId": null}}, {"offset": {"line": 5113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/owners/add/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport { useCreatePropertyOwnerMutation } from '@/store/api/propertyOwnersApi'\nimport PersonalDetails from '@/components/forms/owner/PersonalDetails'\nimport BusinessInformation from '@/components/forms/owner/BusinessInformation'\nimport LocationPicker from '@/components/forms/property/LocationPicker'\nimport EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'\nimport { OwnerFormSchema, type OwnerFormData } from '@/lib/validations/owner'\nimport { PersonalDetailsErrors, DocumentData, BusinessInfo, AddressData } from '@/types/shared'\nimport {\n  ArrowLeft,\n  Save,\n  User,\n  Building,\n  MapPin,\n  FileText,\n  CheckCircle,\n  AlertCircle,\n  <PERSON>rkles\n} from 'lucide-react'\n\nexport default function AddOwnerPage() {\n  const router = useRouter()\n  const [createOwner, { isLoading: isCreating }] = useCreatePropertyOwnerMutation()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [errors, setErrors] = useState<Record<string, string[]>>({})\n\n  const [formData, setFormData] = useState<OwnerFormData>({\n    // Personal Information\n    firstName: '',\n    lastName: '',\n    email: '',\n    phone: '',\n    \n    // Address Information\n    address: {\n      street: '',\n      city: '',\n      state: '',\n      country: '', // Don't auto-fill, detect from coordinates\n      pincode: '',\n      coordinates: {\n        latitude: 0,\n        longitude: 0\n      }\n    },\n    \n    // Role Information\n    roles: {\n      isOwner: true,\n      isDeveloper: false\n    },\n    \n    // Business Information\n    businessInfo: {\n      company: '',\n      businessType: '',\n      gstNumber: '',\n      panNumber: '',\n      incorporationDate: '',\n      businessAddress: ''\n    },\n    \n    // Bank Details\n    bankDetails: {\n      accountNumber: '',\n      ifscCode: '',\n      bankName: '',\n      accountHolderName: '',\n      accountType: 'savings',\n      branchName: ''\n    },\n    \n    // Developer Specific Details - simplified\n    developerDetails: {\n      companyName: '',\n      experience: 0,\n      licenseNumber: '',\n      specialization: []\n    },\n    \n    // Documents\n    documents: {\n      otherDocuments: []\n    },\n    \n    // Administrative\n    notes: '',\n    status: 'active',\n    verificationStatus: 'pending'\n  })\n\n  const steps = [\n    { id: 1, title: 'Personal Details & Role', icon: User, description: 'Basic personal information and role selection' },\n    { id: 2, title: 'Address & Location (Optional)', icon: MapPin, description: 'Owner/Developer address details' },\n    { id: 3, title: 'Business Details (Optional)', icon: Building, description: 'Business and bank information' },\n    { id: 4, title: 'Documents & Review', icon: FileText, description: 'Upload documents and review' }\n  ]\n\n  const handlePersonalDetailsChange = (personalData: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...personalData\n    }))\n    // Clear errors for personal fields\n    setErrors(prev => {\n      const newErrors = { ...prev }\n      Object.keys(personalData).forEach(key => {\n        delete newErrors[key]\n      })\n      return newErrors\n    })\n  }\n\n  const handleLocationChange = (location: any) => {\n    setFormData(prev => ({\n      ...prev,\n      address: location\n    }))\n    // Clear address errors\n    setErrors(prev => {\n      const newErrors = { ...prev }\n      delete newErrors.address\n      return newErrors\n    })\n  }\n\n  const handleBusinessChange = (businessData: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...businessData\n    }))\n    // Clear business errors\n    setErrors(prev => {\n      const newErrors = { ...prev }\n      Object.keys(businessData).forEach(key => {\n        delete newErrors[key]\n      })\n      return newErrors\n    })\n  }\n\n  const validateCurrentStep = () => {\n    // Simple validation for step navigation\n    setErrors({}) // Clear errors on step change\n\n    if (currentStep === 1) {\n      // Basic check for personal details\n      if (!formData.firstName.trim() || !formData.lastName.trim() ||\n          !formData.email.trim() || !formData.phone.trim()) {\n        toast.error('Please fill in all personal details')\n        return false\n      }\n    }\n    // Step 2 (address) is now optional - no validation required\n\n    return true\n  }\n\n  const handleNext = () => {\n    // For step 1 and 2, validate before proceeding\n    if (currentStep <= 2) {\n      if (!validateCurrentStep()) {\n        toast.error('Please fill in all required fields')\n        return\n      }\n    }\n\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1)\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleSubmit = async () => {\n    // Simple validation for required fields only\n    const validationErrors: any = {}\n\n    // Personal details validation\n    if (!formData.firstName.trim()) {\n      validationErrors.firstName = ['First name is required']\n    }\n    if (!formData.lastName.trim()) {\n      validationErrors.lastName = ['Last name is required']\n    }\n    if (!formData.email.trim()) {\n      validationErrors.email = ['Email is required']\n    } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n      validationErrors.email = ['Invalid email format']\n    }\n    if (!formData.phone.trim()) {\n      validationErrors.phone = ['Phone number is required']\n    } else if (!/^[0-9]{10,15}$/.test(formData.phone)) {\n      validationErrors.phone = ['Phone number must be 10-15 digits']\n    }\n\n    // Address validation - now optional, only validate if provided\n    if (formData.address && (\n        formData.address.street?.trim() || formData.address.city?.trim() ||\n        formData.address.state?.trim() || formData.address.pincode?.trim())) {\n      // If any address field is filled, validate the format\n      if (formData.address.pincode?.trim() && !/^[0-9]{6}$/.test(formData.address.pincode)) {\n        validationErrors.address = ['Pincode must be 6 digits']\n      }\n    }\n\n    // Check if there are any validation errors\n    if (Object.keys(validationErrors).length > 0) {\n      setErrors(validationErrors)\n      toast.error('Please fill in all required fields correctly')\n      return\n    }\n\n    try {\n      // Clean up empty optional fields and fix field names for backend\n      let cleanedAddress = undefined\n\n      if (formData.address) {\n        const { pincode } = formData.address\n\n        // Check if address has any meaningful data\n        const hasAddress = formData.address.street?.trim() || formData.address.city?.trim() ||\n                          formData.address.state?.trim() || formData.address.pincode?.trim()\n\n        if (hasAddress) {\n          cleanedAddress = {\n            street: formData.address.street || '',\n            city: formData.address.city || '',\n            state: formData.address.state || '',\n            country: formData.address.country || '',\n            postalCode: pincode || '' // Backend expects postalCode instead of pincode\n          }\n        }\n      }\n\n      const cleanedData: any = {\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        email: formData.email,\n        phone: formData.phone,\n        roles: formData.roles,\n        ...(cleanedAddress && { address: cleanedAddress }),\n        ...(formData.businessInfo?.company && { businessInfo: formData.businessInfo }),\n        ...(formData.bankDetails?.accountNumber && { bankDetails: formData.bankDetails }),\n        ...(formData.roles.isDeveloper && formData.developerDetails?.companyName && { developerDetails: formData.developerDetails }),\n        ...(formData.notes && { notes: formData.notes })\n      }\n\n      await createOwner(cleanedData).unwrap()\n      toast.success('Owner/Developer created successfully!')\n      router.push('/properties/owners')\n    } catch (error: any) {\n      console.error('Error creating owner:', error)\n      toast.error(error?.data?.message || 'Failed to create owner/developer')\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <PersonalDetails\n            value={{\n              firstName: formData.firstName,\n              lastName: formData.lastName,\n              email: formData.email,\n              phone: formData.phone,\n              roles: formData.roles\n            }}\n            onChange={handlePersonalDetailsChange}\n            errors={Object.fromEntries(\n              Object.entries({\n                firstName: errors.firstName,\n                lastName: errors.lastName,\n                email: errors.email,\n                phone: errors.phone,\n                roles: errors.roles\n              }).filter(([_, value]) => value !== undefined)\n            )}\n          />\n        )\n\n      case 2:\n        return (\n          <LocationPicker\n            value={{\n              address: formData.address?.street || '',\n              city: formData.address?.city || '',\n              state: formData.address?.state || '',\n              country: formData.address?.country || '',\n              pincode: formData.address?.pincode || '',\n              ...(formData.address?.coordinates && { coordinates: formData.address.coordinates })\n            }}\n            onChange={handleLocationChange}\n            {...(errors.address && { errors: { address: errors.address } })}\n          />\n        )\n\n      case 3:\n        return (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Building className=\"h-5 w-5\" />\n                Business Information (Optional)\n              </CardTitle>\n              <CardDescription>\n                Add business and bank details. You can skip this step if not applicable.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {/* Skip Option */}\n              <div className=\"p-4 bg-amber-50 border border-amber-200 rounded-lg\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"font-medium text-amber-900\">Skip Business Details</h4>\n                    <p className=\"text-sm text-amber-700\">\n                      You can skip company and bank details for now and add them later from the profile section.\n                    </p>\n                  </div>\n                  <Button\n                    variant=\"outline\"\n                    onClick={() => setCurrentStep(4)}\n                    className=\"border-amber-300 text-amber-700 hover:bg-amber-100\"\n                  >\n                    Skip & Continue\n                  </Button>\n                </div>\n              </div>\n\n              {/* Business Information (Optional) */}\n              <BusinessInformation\n                value={{\n                  ...(formData.businessInfo && {\n                    businessInfo: {\n                      company: formData.businessInfo.company || '',\n                      businessType: formData.businessInfo.businessType || '',\n                      gstNumber: formData.businessInfo.gstNumber || '',\n                      panNumber: formData.businessInfo.panNumber || '',\n                      incorporationDate: formData.businessInfo.incorporationDate || '',\n                      businessAddress: formData.businessInfo.businessAddress || ''\n                    }\n                  }),\n                  bankDetails: {\n                    accountNumber: formData.bankDetails?.accountNumber || '',\n                    ifscCode: formData.bankDetails?.ifscCode || '',\n                    bankName: formData.bankDetails?.bankName || '',\n                    accountHolderName: formData.bankDetails?.accountHolderName || '',\n                    accountType: formData.bankDetails?.accountType || 'savings',\n                    branchName: formData.bankDetails?.branchName || ''\n                  },\n                  ...(formData.developerDetails && {\n                    developerDetails: {\n                      companyName: formData.developerDetails.companyName || '',\n                      experience: formData.developerDetails.experience || 0,\n                      licenseNumber: formData.developerDetails.licenseNumber || '',\n                      specialization: formData.developerDetails.specialization || []\n                    }\n                  }),\n                  roles: { ...formData.roles, isInvestor: false }\n                }}\n                onChange={handleBusinessChange}\n\n                showBusinessFields={true}\n              />\n            </CardContent>\n          </Card>\n        )\n\n      case 4:\n        return (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <FileText className=\"h-5 w-5\" />\n                Review & Submit\n              </CardTitle>\n              <CardDescription>\n                Review all information before submitting\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {/* Document Upload Section */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-lg\">Documents (Optional)</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">Identity Proof</label>\n                    <EnhancedS3Upload\n                      uploadType=\"user-document\"\n                      maxFiles={1}\n                      allowMultiple={false}\n                      onUploadComplete={(results) => {\n                        if (results.length > 0) {\n                          setFormData(prev => ({\n                            ...prev,\n                            documents: { ...prev.documents, identityProof: results[0]?.fileKey }\n                          }))\n                          toast.success('Identity proof uploaded successfully')\n                        }\n                      }}\n                      onUploadError={(error) => toast.error(`Upload failed: ${error}`)}\n                      className=\"h-32\"\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <label className=\"text-sm font-medium\">Address Proof</label>\n                    <EnhancedS3Upload\n                      uploadType=\"user-document\"\n                      maxFiles={1}\n                      allowMultiple={false}\n                      onUploadComplete={(results) => {\n                        if (results.length > 0) {\n                          setFormData(prev => ({\n                            ...prev,\n                            documents: { ...prev.documents, addressProof: results[0]?.fileKey }\n                          }))\n                          toast.success('Address proof uploaded successfully')\n                        }\n                      }}\n                      onUploadError={(error) => toast.error(`Upload failed: ${error}`)}\n                      className=\"h-32\"\n                    />\n                  </div>\n\n                  {formData.businessInfo?.company && (\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Business Registration</label>\n                      <EnhancedS3Upload\n                        uploadType=\"user-document\"\n                        maxFiles={1}\n                        allowMultiple={false}\n                        onUploadComplete={(results) => {\n                          if (results.length > 0) {\n                            setFormData(prev => ({\n                              ...prev,\n                              documents: { ...prev.documents, businessRegistration: results[0]?.fileKey }\n                            }))\n                            toast.success('Business registration uploaded successfully')\n                          }\n                        }}\n                        onUploadError={(error) => toast.error(`Upload failed: ${error}`)}\n                        className=\"h-32\"\n                      />\n                    </div>\n                  )}\n\n                  {formData.roles?.isDeveloper && (\n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Developer License</label>\n                      <EnhancedS3Upload\n                        uploadType=\"user-document\"\n                        maxFiles={1}\n                        allowMultiple={false}\n                        onUploadComplete={(results) => {\n                          if (results.length > 0) {\n                            setFormData(prev => ({\n                              ...prev,\n                              documents: { ...prev.documents, developerLicense: results[0]?.fileKey }\n                            }))\n                            toast.success('Developer license uploaded successfully')\n                          }\n                        }}\n                        onUploadError={(error) => toast.error(`Upload failed: ${error}`)}\n                        className=\"h-32\"\n                      />\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Summary Cards */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n                  <h4 className=\"font-medium text-blue-900 mb-2\">Personal Information</h4>\n                  <div className=\"space-y-1 text-sm text-blue-700\">\n                    <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>\n                    <p><strong>Email:</strong> {formData.email}</p>\n                    <p><strong>Phone:</strong> {formData.phone}</p>\n                    <div className=\"flex gap-2 mt-2\">\n                      {formData.roles?.isOwner && <Badge variant=\"secondary\">Owner</Badge>}\n                      {formData.roles?.isDeveloper && <Badge variant=\"secondary\">Developer</Badge>}\n                    </div>\n                  </div>\n                </div>\n\n                {(formData.address?.street || formData.address?.city || formData.address?.state || formData.address?.pincode) && (\n                  <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg\">\n                    <h4 className=\"font-medium text-green-900 mb-2\">Address</h4>\n                    <div className=\"space-y-1 text-sm text-green-700\">\n                      {formData.address?.street && <p>{formData.address.street}</p>}\n                      {(formData.address?.city || formData.address?.state) && (\n                        <p>{formData.address?.city}{formData.address?.city && formData.address?.state ? ', ' : ''}{formData.address?.state}</p>\n                      )}\n                      {(formData.address?.country || formData.address?.pincode) && (\n                        <p>{formData.address?.country}{formData.address?.country && formData.address?.pincode ? ' - ' : ''}{formData.address?.pincode}</p>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {formData.businessInfo?.company && (\n                  <div className=\"p-4 bg-purple-50 border border-purple-200 rounded-lg\">\n                    <h4 className=\"font-medium text-purple-900 mb-2\">Business Information</h4>\n                    <div className=\"space-y-1 text-sm text-purple-700\">\n                      <p><strong>Company:</strong> {formData.businessInfo?.company}</p>\n                      <p><strong>Type:</strong> {formData.businessInfo?.businessType}</p>\n                      {formData.businessInfo?.gstNumber && (\n                        <p><strong>GST:</strong> {formData.businessInfo.gstNumber}</p>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {formData.roles?.isDeveloper && formData.developerDetails?.companyName && (\n                  <div className=\"p-4 bg-orange-50 border border-orange-200 rounded-lg\">\n                    <h4 className=\"font-medium text-orange-900 mb-2\">Developer Details</h4>\n                    <div className=\"space-y-1 text-sm text-orange-700\">\n                      <p><strong>Company:</strong> {formData.developerDetails?.companyName}</p>\n                      {formData.developerDetails?.experience && formData.developerDetails.experience > 0 && (\n                        <p><strong>Experience:</strong> {formData.developerDetails.experience} years</p>\n                      )}\n                      {formData.developerDetails?.licenseNumber && (\n                        <p><strong>License:</strong> {formData.developerDetails.licenseNumber}</p>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Confirmation */}\n              <div className=\"p-4 bg-muted/50 rounded-lg\">\n                <div className=\"flex items-center gap-2 text-sm\">\n                  <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                  <span>All information has been reviewed and is ready for submission</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <div className=\"bg-blue-600 text-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-4\">\n                <Button\n                  variant=\"ghost\"\n                  onClick={() => router.back()}\n                  className=\"text-white hover:bg-white/20\"\n                >\n                  <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                  Back\n                </Button>\n                <div>\n                  <h1 className=\"text-3xl font-bold flex items-center gap-2\">\n                    <Sparkles className=\"h-8 w-8\" />\n                    Add Owner/Developer\n                  </h1>\n                  <p className=\"text-emerald-100\">Create a new property owner or developer profile</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          {/* Progress Steps */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                    currentStep >= step.id\n                      ? 'bg-blue-500 border-blue-500 text-white'\n                      : 'border-gray-300 text-gray-500'\n                  }`}>\n                    {React.createElement(step.icon, { className: \"h-5 w-5\" })}\n                  </div>\n                  <div className=\"ml-3\">\n                    <p className={`text-sm font-medium ${\n                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'\n                    }`}>\n                      {step.title}\n                    </p>\n                    <p className=\"text-xs text-gray-400\">{step.description}</p>\n                  </div>\n                  {index < steps.length - 1 && (\n                    <div className={`w-16 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-blue-500' : 'bg-gray-300'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Step Content */}\n          <div className=\"mb-8\">\n            {renderStepContent()}\n          </div>\n\n          {/* Navigation */}\n          <div className=\"flex items-center justify-between\">\n            <Button\n              variant=\"outline\"\n              onClick={handlePrevious}\n              disabled={currentStep === 1}\n            >\n              Previous\n            </Button>\n            \n            <div className=\"flex items-center gap-3\">\n              {currentStep === 2 && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setCurrentStep(3)}\n                  className=\"text-gray-600\"\n                >\n                  Skip Address\n                </Button>\n              )}\n              {currentStep === 3 && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setCurrentStep(4)}\n                  className=\"text-gray-600\"\n                >\n                  Skip Business Details\n                </Button>\n              )}\n              {currentStep < steps.length ? (\n                <Button onClick={handleNext}>\n                  Next\n                </Button>\n              ) : (\n                <Button\n                  onClick={handleSubmit}\n                  disabled={isCreating}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  {isCreating ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                      Creating...\n                    </>\n                  ) : (\n                    <>\n                      <Save className=\"h-4 w-4 mr-2\" />\n                      Create Owner/Developer\n                    </>\n                  )}\n                </Button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAhBA;;;;;;;;;;;;;;;AA4Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,wIAAA,CAAA,iCAA8B,AAAD;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAEhE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,uBAAuB;QACvB,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QAEP,sBAAsB;QACtB,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;YACT,aAAa;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QAEA,mBAAmB;QACnB,OAAO;YACL,SAAS;YACT,aAAa;QACf;QAEA,uBAAuB;QACvB,cAAc;YACZ,SAAS;YACT,cAAc;YACd,WAAW;YACX,WAAW;YACX,mBAAmB;YACnB,iBAAiB;QACnB;QAEA,eAAe;QACf,aAAa;YACX,eAAe;YACf,UAAU;YACV,UAAU;YACV,mBAAmB;YACnB,aAAa;YACb,YAAY;QACd;QAEA,0CAA0C;QAC1C,kBAAkB;YAChB,aAAa;YACb,YAAY;YACZ,eAAe;YACf,gBAAgB,EAAE;QACpB;QAEA,YAAY;QACZ,WAAW;YACT,gBAAgB,EAAE;QACpB;QAEA,iBAAiB;QACjB,OAAO;QACP,QAAQ;QACR,oBAAoB;IACtB;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,OAAO;YAA2B,MAAM,sRAAA,CAAA,OAAI;YAAE,aAAa;QAAgD;QACpH;YAAE,IAAI;YAAG,OAAO;YAAiC,MAAM,8RAAA,CAAA,SAAM;YAAE,aAAa;QAAkC;QAC9G;YAAE,IAAI;YAAG,OAAO;YAA+B,MAAM,8RAAA,CAAA,WAAQ;YAAE,aAAa;QAAgC;QAC5G;YAAE,IAAI;YAAG,OAAO;YAAsB,MAAM,kSAAA,CAAA,WAAQ;YAAE,aAAa;QAA8B;KAClG;IAED,MAAM,8BAA8B,CAAC;QACnC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,YAAY;YACjB,CAAC;QACD,mCAAmC;QACnC,UAAU,CAAA;YACR,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;gBAChC,OAAO,SAAS,CAAC,IAAI;YACvB;YACA,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,SAAS;YACX,CAAC;QACD,uBAAuB;QACvB,UAAU,CAAA;YACR,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,UAAU,OAAO;YACxB,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,YAAY;YACjB,CAAC;QACD,wBAAwB;QACxB,UAAU,CAAA;YACR,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;gBAChC,OAAO,SAAS,CAAC,IAAI;YACvB;YACA,OAAO;QACT;IACF;IAEA,MAAM,sBAAsB;QAC1B,wCAAwC;QACxC,UAAU,CAAC,GAAG,8BAA8B;;QAE5C,IAAI,gBAAgB,GAAG;YACrB,mCAAmC;YACnC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,IAAI,MACrD,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QACA,4DAA4D;QAE5D,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,+CAA+C;QAC/C,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,uBAAuB;gBAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;QACF;QAEA,IAAI,cAAc,MAAM,MAAM,EAAE;YAC9B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,eAAe;QACnB,6CAA6C;QAC7C,MAAM,mBAAwB,CAAC;QAE/B,8BAA8B;QAC9B,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,iBAAiB,SAAS,GAAG;gBAAC;aAAyB;QACzD;QACA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,iBAAiB,QAAQ,GAAG;gBAAC;aAAwB;QACvD;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,iBAAiB,KAAK,GAAG;gBAAC;aAAoB;QAChD,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,iBAAiB,KAAK,GAAG;gBAAC;aAAuB;QACnD;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,iBAAiB,KAAK,GAAG;gBAAC;aAA2B;QACvD,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,SAAS,KAAK,GAAG;YACjD,iBAAiB,KAAK,GAAG;gBAAC;aAAoC;QAChE;QAEA,+DAA+D;QAC/D,IAAI,SAAS,OAAO,IAAI,CACpB,SAAS,OAAO,CAAC,MAAM,EAAE,UAAU,SAAS,OAAO,CAAC,IAAI,EAAE,UAC1D,SAAS,OAAO,CAAC,KAAK,EAAE,UAAU,SAAS,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG;YACvE,sDAAsD;YACtD,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,aAAa,IAAI,CAAC,SAAS,OAAO,CAAC,OAAO,GAAG;gBACpF,iBAAiB,OAAO,GAAG;oBAAC;iBAA2B;YACzD;QACF;QAEA,2CAA2C;QAC3C,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;YAC5C,UAAU;YACV,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iEAAiE;YACjE,IAAI,iBAAiB;YAErB,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,OAAO;gBAEpC,2CAA2C;gBAC3C,MAAM,aAAa,SAAS,OAAO,CAAC,MAAM,EAAE,UAAU,SAAS,OAAO,CAAC,IAAI,EAAE,UAC3D,SAAS,OAAO,CAAC,KAAK,EAAE,UAAU,SAAS,OAAO,CAAC,OAAO,EAAE;gBAE9E,IAAI,YAAY;oBACd,iBAAiB;wBACf,QAAQ,SAAS,OAAO,CAAC,MAAM,IAAI;wBACnC,MAAM,SAAS,OAAO,CAAC,IAAI,IAAI;wBAC/B,OAAO,SAAS,OAAO,CAAC,KAAK,IAAI;wBACjC,SAAS,SAAS,OAAO,CAAC,OAAO,IAAI;wBACrC,YAAY,WAAW,GAAG,gDAAgD;oBAC5E;gBACF;YACF;YAEA,MAAM,cAAmB;gBACvB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK;gBACrB,GAAI,kBAAkB;oBAAE,SAAS;gBAAe,CAAC;gBACjD,GAAI,SAAS,YAAY,EAAE,WAAW;oBAAE,cAAc,SAAS,YAAY;gBAAC,CAAC;gBAC7E,GAAI,SAAS,WAAW,EAAE,iBAAiB;oBAAE,aAAa,SAAS,WAAW;gBAAC,CAAC;gBAChF,GAAI,SAAS,KAAK,CAAC,WAAW,IAAI,SAAS,gBAAgB,EAAE,eAAe;oBAAE,kBAAkB,SAAS,gBAAgB;gBAAC,CAAC;gBAC3H,GAAI,SAAS,KAAK,IAAI;oBAAE,OAAO,SAAS,KAAK;gBAAC,CAAC;YACjD;YAEA,MAAM,YAAY,aAAa,MAAM;YACrC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,MAAM,WAAW;QACtC;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6WAAC,uJAAA,CAAA,UAAe;oBACd,OAAO;wBACL,WAAW,SAAS,SAAS;wBAC7B,UAAU,SAAS,QAAQ;wBAC3B,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;oBACvB;oBACA,UAAU;oBACV,QAAQ,OAAO,WAAW,CACxB,OAAO,OAAO,CAAC;wBACb,WAAW,OAAO,SAAS;wBAC3B,UAAU,OAAO,QAAQ;wBACzB,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;oBACrB,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,UAAU;;;;;;YAK5C,KAAK;gBACH,qBACE,6WAAC,yJAAA,CAAA,UAAc;oBACb,OAAO;wBACL,SAAS,SAAS,OAAO,EAAE,UAAU;wBACrC,MAAM,SAAS,OAAO,EAAE,QAAQ;wBAChC,OAAO,SAAS,OAAO,EAAE,SAAS;wBAClC,SAAS,SAAS,OAAO,EAAE,WAAW;wBACtC,SAAS,SAAS,OAAO,EAAE,WAAW;wBACtC,GAAI,SAAS,OAAO,EAAE,eAAe;4BAAE,aAAa,SAAS,OAAO,CAAC,WAAW;wBAAC,CAAC;oBACpF;oBACA,UAAU;oBACT,GAAI,OAAO,OAAO,IAAI;wBAAE,QAAQ;4BAAE,SAAS,OAAO,OAAO;wBAAC;oBAAE,CAAC;;;;;;YAIpE,KAAK;gBACH,qBACE,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;;8CACT,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6WAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6WAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;;kEACC,6WAAC;wDAAG,WAAU;kEAA6B;;;;;;kEAC3C,6WAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;0DAIxC,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,6WAAC,2JAAA,CAAA,UAAmB;oCAClB,OAAO;wCACL,GAAI,SAAS,YAAY,IAAI;4CAC3B,cAAc;gDACZ,SAAS,SAAS,YAAY,CAAC,OAAO,IAAI;gDAC1C,cAAc,SAAS,YAAY,CAAC,YAAY,IAAI;gDACpD,WAAW,SAAS,YAAY,CAAC,SAAS,IAAI;gDAC9C,WAAW,SAAS,YAAY,CAAC,SAAS,IAAI;gDAC9C,mBAAmB,SAAS,YAAY,CAAC,iBAAiB,IAAI;gDAC9D,iBAAiB,SAAS,YAAY,CAAC,eAAe,IAAI;4CAC5D;wCACF,CAAC;wCACD,aAAa;4CACX,eAAe,SAAS,WAAW,EAAE,iBAAiB;4CACtD,UAAU,SAAS,WAAW,EAAE,YAAY;4CAC5C,UAAU,SAAS,WAAW,EAAE,YAAY;4CAC5C,mBAAmB,SAAS,WAAW,EAAE,qBAAqB;4CAC9D,aAAa,SAAS,WAAW,EAAE,eAAe;4CAClD,YAAY,SAAS,WAAW,EAAE,cAAc;wCAClD;wCACA,GAAI,SAAS,gBAAgB,IAAI;4CAC/B,kBAAkB;gDAChB,aAAa,SAAS,gBAAgB,CAAC,WAAW,IAAI;gDACtD,YAAY,SAAS,gBAAgB,CAAC,UAAU,IAAI;gDACpD,eAAe,SAAS,gBAAgB,CAAC,aAAa,IAAI;gDAC1D,gBAAgB,SAAS,gBAAgB,CAAC,cAAc,IAAI,EAAE;4CAChE;wCACF,CAAC;wCACD,OAAO;4CAAE,GAAG,SAAS,KAAK;4CAAE,YAAY;wCAAM;oCAChD;oCACA,UAAU;oCAEV,oBAAoB;;;;;;;;;;;;;;;;;;YAM9B,KAAK;gBACH,qBACE,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;;8CACT,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,kSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6WAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6WAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CAErB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,6WAAC,oJAAA,CAAA,UAAgB;4DACf,YAAW;4DACX,UAAU;4DACV,eAAe;4DACf,kBAAkB,CAAC;gEACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;oEACtB,YAAY,CAAA,OAAQ,CAAC;4EACnB,GAAG,IAAI;4EACP,WAAW;gFAAE,GAAG,KAAK,SAAS;gFAAE,eAAe,OAAO,CAAC,EAAE,EAAE;4EAAQ;wEACrE,CAAC;oEACD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gEAChB;4DACF;4DACA,eAAe,CAAC,QAAU,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;4DAC/D,WAAU;;;;;;;;;;;;8DAId,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,6WAAC,oJAAA,CAAA,UAAgB;4DACf,YAAW;4DACX,UAAU;4DACV,eAAe;4DACf,kBAAkB,CAAC;gEACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;oEACtB,YAAY,CAAA,OAAQ,CAAC;4EACnB,GAAG,IAAI;4EACP,WAAW;gFAAE,GAAG,KAAK,SAAS;gFAAE,cAAc,OAAO,CAAC,EAAE,EAAE;4EAAQ;wEACpE,CAAC;oEACD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gEAChB;4DACF;4DACA,eAAe,CAAC,QAAU,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;4DAC/D,WAAU;;;;;;;;;;;;gDAIb,SAAS,YAAY,EAAE,yBACtB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,6WAAC,oJAAA,CAAA,UAAgB;4DACf,YAAW;4DACX,UAAU;4DACV,eAAe;4DACf,kBAAkB,CAAC;gEACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;oEACtB,YAAY,CAAA,OAAQ,CAAC;4EACnB,GAAG,IAAI;4EACP,WAAW;gFAAE,GAAG,KAAK,SAAS;gFAAE,sBAAsB,OAAO,CAAC,EAAE,EAAE;4EAAQ;wEAC5E,CAAC;oEACD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gEAChB;4DACF;4DACA,eAAe,CAAC,QAAU,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;4DAC/D,WAAU;;;;;;;;;;;;gDAKf,SAAS,KAAK,EAAE,6BACf,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,6WAAC,oJAAA,CAAA,UAAgB;4DACf,YAAW;4DACX,UAAU;4DACV,eAAe;4DACf,kBAAkB,CAAC;gEACjB,IAAI,QAAQ,MAAM,GAAG,GAAG;oEACtB,YAAY,CAAA,OAAQ,CAAC;4EACnB,GAAG,IAAI;4EACP,WAAW;gFAAE,GAAG,KAAK,SAAS;gFAAE,kBAAkB,OAAO,CAAC,EAAE,EAAE;4EAAQ;wEACxE,CAAC;oEACD,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gEAChB;4DACF;4DACA,eAAe,CAAC,QAAU,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;4DAC/D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAQpB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAc;gEAAE,SAAS,SAAS;gEAAC;gEAAE,SAAS,QAAQ;;;;;;;sEACjE,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,6WAAC;4DAAI,WAAU;;gEACZ,SAAS,KAAK,EAAE,yBAAW,6WAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAY;;;;;;gEACtD,SAAS,KAAK,EAAE,6BAAe,6WAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAY;;;;;;;;;;;;;;;;;;;;;;;;wCAKhE,CAAC,SAAS,OAAO,EAAE,UAAU,SAAS,OAAO,EAAE,QAAQ,SAAS,OAAO,EAAE,SAAS,SAAS,OAAO,EAAE,OAAO,mBAC1G,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,6WAAC;oDAAI,WAAU;;wDACZ,SAAS,OAAO,EAAE,wBAAU,6WAAC;sEAAG,SAAS,OAAO,CAAC,MAAM;;;;;;wDACvD,CAAC,SAAS,OAAO,EAAE,QAAQ,SAAS,OAAO,EAAE,KAAK,mBACjD,6WAAC;;gEAAG,SAAS,OAAO,EAAE;gEAAM,SAAS,OAAO,EAAE,QAAQ,SAAS,OAAO,EAAE,QAAQ,OAAO;gEAAI,SAAS,OAAO,EAAE;;;;;;;wDAE9G,CAAC,SAAS,OAAO,EAAE,WAAW,SAAS,OAAO,EAAE,OAAO,mBACtD,6WAAC;;gEAAG,SAAS,OAAO,EAAE;gEAAS,SAAS,OAAO,EAAE,WAAW,SAAS,OAAO,EAAE,UAAU,QAAQ;gEAAI,SAAS,OAAO,EAAE;;;;;;;;;;;;;;;;;;;wCAM7H,SAAS,YAAY,EAAE,yBACtB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAiB;gEAAE,SAAS,YAAY,EAAE;;;;;;;sEACrD,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAc;gEAAE,SAAS,YAAY,EAAE;;;;;;;wDACjD,SAAS,YAAY,EAAE,2BACtB,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAa;gEAAE,SAAS,YAAY,CAAC,SAAS;;;;;;;;;;;;;;;;;;;wCAMhE,SAAS,KAAK,EAAE,eAAe,SAAS,gBAAgB,EAAE,6BACzD,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAiB;gEAAE,SAAS,gBAAgB,EAAE;;;;;;;wDACxD,SAAS,gBAAgB,EAAE,cAAc,SAAS,gBAAgB,CAAC,UAAU,GAAG,mBAC/E,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAoB;gEAAE,SAAS,gBAAgB,CAAC,UAAU;gEAAC;;;;;;;wDAEvE,SAAS,gBAAgB,EAAE,+BAC1B,6WAAC;;8EAAE,6WAAC;8EAAO;;;;;;gEAAiB;gEAAE,SAAS,gBAAgB,CAAC,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,6WAAC;oCAAI,WAAU;8CACb,cAAA,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6WAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOlB;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC,+IAAA,CAAA,UAAe;kBACd,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6WAAC;;0DACC,6WAAC;gDAAG,WAAU;;kEACZ,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6WAAC;gDAAE,WAAU;0DAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO1C,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6WAAC;wCAAkB,WAAU;;0DAC3B,6WAAC;gDAAI,WAAW,CAAC,iEAAiE,EAChF,eAAe,KAAK,EAAE,GAClB,2CACA,iCACJ;0DACC,cAAA,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;oDAAE,WAAW;gDAAU;;;;;;0DAEzD,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAE,WAAW,CAAC,oBAAoB,EACjC,eAAe,KAAK,EAAE,GAAG,kBAAkB,iBAC3C;kEACC,KAAK,KAAK;;;;;;kEAEb,6WAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;4CAEvD,QAAQ,MAAM,MAAM,GAAG,mBACtB,6WAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,cAAc,KAAK,EAAE,GAAG,gBAAgB,eACxC;;;;;;;uCAnBI,KAAK,EAAE;;;;;;;;;;;;;;;sCA2BvB,6WAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIH,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,gBAAgB;8CAC3B;;;;;;8CAID,6WAAC;oCAAI,WAAU;;wCACZ,gBAAgB,mBACf,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;wCAIF,gBAAgB,mBACf,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDACX;;;;;;wCAIF,cAAc,MAAM,MAAM,iBACzB,6WAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;sDAAY;;;;;iEAI7B,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,2BACC;;kEACE,6WAAC;wDAAI,WAAU;;;;;;oDAAuE;;6EAIxF;;kEACE,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD", "debugId": null}}]}