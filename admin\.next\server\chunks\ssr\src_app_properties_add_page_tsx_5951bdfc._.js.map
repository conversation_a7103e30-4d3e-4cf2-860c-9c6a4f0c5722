{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/add/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRout<PERSON> } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport { useCreatePropertyMutation } from '@/store/api/propertiesApi'\nimport LocationPicker from '@/components/forms/property/LocationPicker'\nimport FinancialDetails from '@/components/forms/property/FinancialDetails'\nimport ConstructionTimeline from '@/components/forms/property/ConstructionTimeline'\nimport OwnerDeveloperSelector from '@/components/forms/property/OwnerDeveloperSelector'\nimport EnhancedFeaturesAmenities from '@/components/forms/property/EnhancedFeaturesAmenities'\nimport LegalDocumentsUpload from '@/components/forms/property/LegalDocumentsUpload'\nimport PropertyImagesUpload from '@/components/forms/property/PropertyImagesUpload'\nimport BookingForm from '@/components/forms/property/BookingForm'\nimport { PropertyImageFormData, LocationData, ConstructionData } from '@/types/shared'\n// Simple form data interface\ninterface PropertyFormData {\n  name: string\n  description: string\n  propertyType: string\n  location: LocationData\n  expectedReturns: number\n  maturityPeriodMonths: number\n  totalStocks: number\n  pricePerStock: number\n  constructionStatus: string\n  launchDate: string\n  expectedCompletion: string\n  actualCompletion?: string\n  constructionTimeline?: string\n  ownerId?: string\n  developerId?: string\n  images: PropertyImageFormData[]\n  documents?: any[]\n  videos?: any[]\n  legalDocuments?: any[]\n  features?: string[]\n  amenities?: string[]\n  specifications?: any\n  status?: string\n  featured?: boolean\n  priorityOrder?: number\n  referralCommissionRate?: number\n  salesCommissionRate?: number\n  referralCommissionPerStock?: number\n  salesCommissionPerStock?: number\n  commissionType?: string\n  [key: string]: any\n}\nimport {\n  ArrowLeft,\n  ArrowRight,\n  Save,\n  Building,\n  MapPin,\n  DollarSign,\n  Calendar,\n  User,\n  Upload,\n  AlertCircle,\n  FileText,\n  CreditCard\n} from 'lucide-react'\n\nconst propertyTypes = [\n  { value: 'residential', label: 'Residential', icon: '🏠' },\n  { value: 'commercial', label: 'Commercial', icon: '🏢' },\n  { value: 'industrial', label: 'Industrial', icon: '🏭' },\n  { value: 'land', label: 'Land', icon: '🌍' },\n  { value: 'mixed', label: 'Mixed Use', icon: '🏘️' },\n  { value: 'luxury', label: 'Luxury', icon: '✨' },\n  { value: 'eco_friendly', label: 'Eco-Friendly', icon: '🌱' }\n]\n\nconst amenitiesList = [\n  // Basic Amenities\n  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',\n  'Club House', 'Power Backup', 'Elevator', 'CCTV', 'Intercom', 'Water Supply',\n\n  // Premium Amenities\n  'Spa & Wellness Center', 'Rooftop Terrace', 'Business Center', 'Conference Room',\n  'Library', 'Meditation Room', 'Yoga Studio', 'Indoor Games Room',\n  'Outdoor Sports Court', 'Jogging Track', 'Cycling Track', 'Pet Park',\n\n  // Safety & Security\n  '24/7 Security', 'Gated Community', 'Fire Safety', 'Emergency Exit',\n  'Smoke Detectors', 'Sprinkler System', 'Security Cameras', 'Access Control',\n\n  // Convenience\n  'Concierge Service', 'Housekeeping', 'Laundry Service', 'Valet Parking',\n  'Car Wash', 'ATM', 'Shopping Center', 'Food Court',\n\n  // Utilities\n  'High Speed Internet', 'Cable TV', 'Intercom System', 'Video Door Phone',\n  'Centralized Gas', 'Solar Power', 'Rainwater Harvesting', 'Waste Management',\n\n  // Outdoor Features\n  'Landscaped Gardens', 'Water Features', 'BBQ Area', 'Outdoor Seating',\n  'Kids Play Area', 'Senior Citizen Area', 'Amphitheater', 'Gazebo'\n]\n\nconst featuresList = [\n  // Structural Features\n  'Modular Kitchen', 'Walk-in Closet', 'Master Bedroom', 'Guest Room',\n  'Study Room', 'Prayer Room', 'Servant Room', 'Store Room',\n  'Balcony', 'Terrace', 'Basement', 'Attic',\n\n  // Interior Features\n  'Wooden Flooring', 'Marble Flooring', 'Vitrified Tiles', 'False Ceiling',\n  'Designer Lighting', 'Built-in Wardrobes', 'Kitchen Cabinets', 'Granite Countertops',\n  'Modular Switches', 'Premium Fittings', 'Designer Bathroom', 'Jacuzzi',\n\n  // Technology Features\n  'Smart Home Automation', 'Video Door Bell', 'Smart Locks', 'Motion Sensors',\n  'Smart Lighting', 'Climate Control', 'Home Theater Setup', 'Sound System',\n\n  // Energy Efficiency\n  'Solar Panels', 'Energy Efficient Windows', 'LED Lighting', 'Insulation',\n  'Double Glazed Windows', 'Ventilation System', 'Natural Lighting', 'Cross Ventilation',\n\n  // Luxury Features\n  'Private Pool', 'Private Garden', 'Private Elevator', 'Butler Service',\n  'Wine Cellar', 'Home Office', 'Guest House', 'Driver Room',\n\n  // Accessibility\n  'Wheelchair Accessible', 'Ramp Access', 'Wide Doorways', 'Accessible Bathroom',\n  'Emergency Call System', 'Braille Signage', 'Audio Visual Alerts', 'Grab Bars'\n]\n\n\n\nexport default function AddEnhancedPropertyPage() {\n  const router = useRouter()\n  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()\n  const [currentStep, setCurrentStep] = useState(1)\n  const [errors, setErrors] = useState<any>({})\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  const [formData, setFormData] = useState<PropertyFormData>({\n    // Basic Information\n    name: '',\n    description: '',\n    propertyType: 'residential',\n    \n    // Location\n    location: {\n      address: '',\n      city: '',\n      state: '',\n      country: 'India',\n      pincode: '',\n      coordinates: {\n        latitude: 0,\n        longitude: 0\n      }\n    },\n    \n    // Financial Details\n    expectedReturns: 0,\n    maturityPeriodMonths: 12,\n    totalStocks: 100,\n    pricePerStock: 1000,\n    availableStocks: 100,\n    stockPrefix: 'PROP',\n    stockStartNumber: 1,\n    referralCommissionRate: 2,\n    salesCommissionRate: 1,\n    referralCommissionPerStock: 20,\n    salesCommissionPerStock: 10,\n    commissionType: 'percentage',\n    \n    // Construction Details\n    constructionStatus: 'planning',\n    launchDate: '',\n    expectedCompletion: '',\n    actualCompletion: '',\n    constructionTimeline: '',\n    \n    // Owner and Developer\n    ownerId: '',\n    developerId: '',\n    \n    // Property Features\n    amenities: [],\n    features: [],\n    specifications: {},\n    \n    // Media Files\n    images: [] as PropertyImageFormData[],\n    documents: [],\n    videos: [],\n    legalDocuments: [],\n    \n    // Administrative\n    status: 'active',\n    featured: false,\n    priorityOrder: 0\n  })\n\n  const steps = [\n    { id: 1, title: 'Basic Information', shortTitle: 'Basic Info', icon: Building },\n    { id: 2, title: 'Location Details', shortTitle: 'Location', icon: MapPin },\n    { id: 3, title: 'Owner & Developer', shortTitle: 'Owner/Dev', icon: User },\n    { id: 4, title: 'Financial Details', shortTitle: 'Financial', icon: DollarSign },\n    { id: 5, title: 'Construction Timeline', shortTitle: 'Timeline', icon: Calendar },\n    { id: 6, title: 'Property Images', shortTitle: 'Images', icon: Upload },\n    { id: 7, title: 'Features & Amenities', shortTitle: 'Features', icon: Upload },\n    { id: 8, title: 'Document Upload', shortTitle: 'Documents', icon: Upload },\n    { id: 9, title: 'Booking Form', shortTitle: 'Booking', icon: CreditCard }\n  ]\n\n  const handleInputChange = (field: string, value: any) => {\n    if (field.includes('.')) {\n      const keys = field.split('.')\n      setFormData(prev => {\n        const updated = { ...prev }\n        let current = updated as any\n        for (let i = 0; i < keys.length - 1; i++) {\n          const key = keys[i]\n          if (key !== undefined) {\n            current = current[key]\n          }\n        }\n        const lastKey = keys[keys.length - 1]\n        if (lastKey !== undefined) {\n          current[lastKey] = value\n        }\n        return updated\n      })\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }))\n    }\n    \n    // Clear error for this field\n    if (errors[field]) {\n      setErrors((prev: any) => ({\n        ...prev,\n        [field]: undefined\n      }))\n    }\n  }\n\n  const handleLocationChange = (location: any) => {\n    setFormData(prev => ({\n      ...prev,\n      location\n    }))\n  }\n\n  const handleFinancialChange = (financial: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...financial\n    }))\n  }\n\n  const handleConstructionChange = (construction: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...construction\n    }))\n  }\n\n  const handleOwnerDeveloperChange = (ownerDeveloper: any) => {\n    setFormData(prev => ({\n      ...prev,\n      ...ownerDeveloper\n    }))\n  }\n\n\n\n  const validateCurrentStep = () => {\n    // Step-specific validation only\n    switch (currentStep) {\n      case 1: // Basic Information\n        if (!formData.name?.trim()) {\n          toast.error('Property name is required')\n          return false\n        }\n        if (!formData.description?.trim()) {\n          toast.error('Property description is required')\n          return false\n        }\n        if (!formData.propertyType) {\n          toast.error('Property type is required')\n          return false\n        }\n        break\n\n      case 2: // Location Details\n        if (!formData.location?.address?.trim()) {\n          toast.error('Property address is required (minimum 3 characters)')\n          return false\n        }\n        if (formData.location.address.trim().length < 3) {\n          toast.error('Property address must be at least 3 characters long')\n          return false\n        }\n        if (!formData.location?.city?.trim()) {\n          toast.error('City is required (minimum 2 characters)')\n          return false\n        }\n        if (formData.location.city.trim().length < 2) {\n          toast.error('City must be at least 2 characters long')\n          return false\n        }\n        if (!formData.location?.state?.trim()) {\n          toast.error('State is required (minimum 2 characters)')\n          return false\n        }\n        if (formData.location.state.trim().length < 2) {\n          toast.error('State must be at least 2 characters long')\n          return false\n        }\n        // Pincode is optional in backend validation\n        break\n\n      case 3: // Owner & Developer\n        if (!formData.ownerId && !formData.developerId) {\n          toast.error('Please select either a property owner or developer')\n          return false\n        }\n        break\n\n      case 4: // Financial Details\n        if (!formData.expectedReturns || formData.expectedReturns <= 0) {\n          toast.error('Expected returns is required and must be greater than 0')\n          return false\n        }\n        if (formData.expectedReturns > 100) {\n          toast.error('Expected returns cannot exceed 100%')\n          return false\n        }\n        if (!formData.totalStocks || formData.totalStocks <= 0) {\n          toast.error('Total stocks is required and must be greater than 0')\n          return false\n        }\n        if (!formData.pricePerStock || formData.pricePerStock <= 0) {\n          toast.error('Price per stock is required and must be greater than 0')\n          return false\n        }\n        if (!formData.maturityPeriodMonths || formData.maturityPeriodMonths <= 0) {\n          toast.error('Maturity period is required and must be at least 1 month')\n          return false\n        }\n        break\n\n      case 5: // Construction Timeline\n        if (!formData.constructionStatus) {\n          toast.error('Construction status is required')\n          return false\n        }\n        if (!formData.launchDate) {\n          toast.error('Launch date is required')\n          return false\n        }\n        if (!formData.expectedCompletion) {\n          toast.error('Expected completion date is required')\n          return false\n        }\n        break\n\n      case 6: // Property Images\n        if (!formData.images || formData.images.length === 0) {\n          toast.error('At least 1 property image is required')\n          return false\n        }\n        if (formData.images.length > 10) {\n          toast.error('Maximum 10 images allowed')\n          return false\n        }\n        break\n\n      case 7: // Features & Amenities (optional)\n        // No validation required - features and amenities are optional\n        break\n\n      case 8: // Document Upload (optional)\n        // No validation required - legal documents are optional\n        break\n\n      case 9: // Booking Form (optional)\n        // No validation required - booking form is optional demonstration\n        break\n\n      default:\n        break\n    }\n\n    return true\n  }\n\n  const handleNext = () => {\n    if (validateCurrentStep()) {\n      if (currentStep < 9) {\n        setCurrentStep(currentStep + 1)\n      } else {\n        handleSubmit()\n      }\n    }\n  }\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1)\n    }\n  }\n\n  const handleSubmit = async () => {\n    if (!validateCurrentStep()) {\n      toast.error('Please fix the validation errors before submitting')\n      return\n    }\n\n    // Validate required fields\n    if (!formData.ownerId?.trim() && !formData.developerId?.trim()) {\n      toast.error('Please select at least one owner or developer')\n      return\n    }\n\n    if (formData.images.length === 0) {\n      toast.error('Please upload at least one property image')\n      return\n    }\n\n    try {\n      setIsSubmitting(true)\n      // Transform data for API compatibility - Backend model expects location as object\n      const apiFormData = {\n        ...formData,\n        // Backend model expects location as object with address, city, state, etc.\n        location: {\n          address: formData.location?.address?.trim() || '',\n          city: formData.location?.city?.trim() || extractCityFromAddress(formData.location?.address) || '',\n          state: formData.location?.state?.trim() || extractStateFromAddress(formData.location?.address) || '',\n          country: formData.location?.country?.trim() || 'India',\n          pincode: formData.location?.pincode?.trim() || '',\n          // Add coordinates if available\n          ...(formData.location?.coordinates?.latitude && formData.location?.coordinates?.longitude && {\n            coordinates: {\n              latitude: formData.location.coordinates.latitude,\n              longitude: formData.location.coordinates.longitude\n            }\n          })\n        },\n        // Also add flat city and state for backward compatibility\n        city: formData.location?.city?.trim() || extractCityFromAddress(formData.location?.address) || '',\n        state: formData.location?.state?.trim() || extractStateFromAddress(formData.location?.address) || '',\n        // Stock configuration data\n        totalStocks: formData.totalStocks || 1000,\n        stockPrice: formData.pricePerStock || 1000,\n        minimumPurchase: 1,\n        referralCommissionRate: formData.referralCommissionRate || 2,\n        salesCommissionRate: formData.salesCommissionRate || 1,\n        referralCommissionPerStock: formData.referralCommissionPerStock || 20,\n        salesCommissionPerStock: formData.salesCommissionPerStock || 10,\n        commissionType: formData.commissionType || 'percentage',\n        // Fix construction status mapping\n        constructionStatus: mapConstructionStatus(formData.constructionStatus),\n        // Fix images format - send as array of objects with url and name\n        images: formData.images.map((img, index) => {\n          if (typeof img === 'string') {\n            return {\n              url: img,\n              name: `Property Image ${index + 1}`,\n              type: 'image/jpeg',\n              key: `image-${index + 1}`\n            }\n          } else {\n            return {\n              url: img.url || img,\n              name: img.name || `Property Image ${index + 1}`,\n              type: img.type || 'image/jpeg',\n              key: img.key || `image-${index + 1}`\n            }\n          }\n        }),\n        // Fix legal documents format - send as object\n        legalDocuments: Array.isArray(formData.legalDocuments) && formData.legalDocuments.length > 0\n          ? { approvals: formData.legalDocuments, certificates: [], agreements: [] }\n          : { approvals: [], certificates: [], agreements: [] },\n        // Set owner/developer IDs (only include if not empty)\n        ...(formData.ownerId?.trim() && { ownerId: formData.ownerId.trim() }),\n        ...(formData.developerId?.trim() && { developerId: formData.developerId.trim() }),\n        // Set completion dates\n        actualCompletion: formData.actualCompletion,\n        // Transform documents array - send as array of objects\n        documents: formData.documents?.map((doc, index) => {\n          if (typeof doc === 'string') {\n            return {\n              url: doc,\n              name: `Document ${index + 1}`,\n              type: 'application/pdf',\n              key: `doc-${index + 1}`\n            }\n          } else {\n            return {\n              url: doc.url || doc,\n              name: doc.name || `Document ${index + 1}`,\n              type: doc.type || 'application/pdf',\n              key: doc.key || `doc-${index + 1}`\n            }\n          }\n        }),\n        videos: formData.videos?.map(vid => typeof vid === 'string' ? vid : vid.url) || [],\n        status: formData.status as any\n      }\n\n      // Helper functions\n      function extractCityFromAddress(address: string): string {\n        if (!address) return ''\n        const parts = address.split(',').map(p => p.trim())\n        return parts[0] || ''\n      }\n\n      function extractStateFromAddress(address: string): string {\n        if (!address) return ''\n        const parts = address.split(',').map(p => p.trim())\n        // Look for state in the address parts\n        for (const part of parts) {\n          if (part.toLowerCase().includes('uttar pradesh') || part.toLowerCase().includes('up')) return 'Uttar Pradesh'\n          if (part.toLowerCase().includes('delhi')) return 'Delhi'\n          if (part.toLowerCase().includes('haryana')) return 'Haryana'\n          if (part.toLowerCase().includes('rajasthan')) return 'Rajasthan'\n          // Add more states as needed\n        }\n        return parts[parts.length - 2] || ''\n      }\n\n      function mapConstructionStatus(status: string): string {\n        const statusMap: Record<string, string> = {\n          'structure': 'under_construction',\n          'planning': 'planning',\n          'foundation': 'under_construction',\n          'completed': 'completed',\n          'ready_to_move': 'ready_to_move'\n        }\n        return statusMap[status] || 'planning'\n      }\n\n      // Remove empty fields\n      const cleanedData: any = { ...apiFormData }\n      if (!cleanedData.ownerId || cleanedData.ownerId.trim() === '') {\n        delete cleanedData.ownerId\n      }\n      if (!cleanedData.developerId || cleanedData.developerId.trim() === '') {\n        delete cleanedData.developerId\n      }\n      if (!cleanedData.actualCompletion || cleanedData.actualCompletion.trim() === '') {\n        delete cleanedData.actualCompletion\n      }\n\n      console.log('=== PROPERTY CREATION DEBUG ===')\n      console.log('Form data location:', formData.location)\n      console.log('API data location:', cleanedData.location)\n      console.log('Location structure:', {\n        address: cleanedData.location?.address,\n        city: cleanedData.location?.city,\n        state: cleanedData.location?.state,\n        country: cleanedData.location?.country,\n        pincode: cleanedData.location?.pincode\n      })\n      console.log('Required fields check:', {\n        name: cleanedData.name,\n        propertyType: cleanedData.propertyType,\n        totalStocks: cleanedData.totalStocks,\n        stockPrice: cleanedData.stockPrice,\n        expectedReturns: cleanedData.expectedReturns,\n        maturityPeriodMonths: cleanedData.maturityPeriodMonths,\n        imagesCount: cleanedData.images?.length\n      })\n      console.log('Full API payload:', JSON.stringify(cleanedData, null, 2))\n\n      // Backend expects flat structure, no need to restructure location\n      const finalData = {\n        ...cleanedData\n      }\n\n      // Remove any undefined or null values\n      Object.keys(finalData).forEach(key => {\n        if (finalData[key] === undefined || finalData[key] === null || finalData[key] === '') {\n          delete finalData[key]\n        }\n      })\n\n      console.log('Final data location:', finalData.location)\n      await createProperty(finalData).unwrap()\n      toast.success('Property created successfully!')\n      router.push('/properties')\n    } catch (error: any) {\n      console.error('Property creation failed:', error)\n      console.error('Error details:', JSON.stringify(error, null, 2))\n\n      if (error?.data?.details) {\n        // Show validation errors with field names\n        const validationErrors = error.data.details.map((err: any) =>\n          `${err.path || err.param || 'Field'}: ${err.msg}`\n        ).join('\\n')\n        toast.error(`Validation Errors:\\n${validationErrors}`, { duration: 8000 })\n      } else if (error?.data?.message) {\n        toast.error(`Error: ${error.data.message}`)\n      } else if (error?.message) {\n        toast.error(`Error: ${error.message}`)\n      } else {\n        toast.error('Failed to create property. Please check all required fields and try again.')\n      }\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Building className=\"h-5 w-5\" />\n                Basic Information\n              </CardTitle>\n              <CardDescription>\n                Enter the property's basic details and description\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"name\">Property Name *</Label>\n                  <Input\n                    id=\"name\"\n                    placeholder=\"Enter property name\"\n                    value={formData.name}\n                    onChange={(e) => handleInputChange('name', e.target.value)}\n                    className={errors.name ? 'border-red-500' : ''}\n                  />\n                  {errors.name && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.name?.[0]}\n                    </p>\n                  )}\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"propertyType\">Property Type *</Label>\n                  <Select\n                    value={formData.propertyType}\n                    onValueChange={(value) => handleInputChange('propertyType', value)}\n                  >\n                    <SelectTrigger className={errors.propertyType ? 'border-red-500' : ''}>\n                      <SelectValue placeholder=\"Select property type\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {propertyTypes.map((type) => (\n                        <SelectItem key={type.value} value={type.value}>\n                          <div className=\"flex items-center gap-2\">\n                            <span>{type.icon}</span>\n                            {type.label}\n                          </div>\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  {errors.propertyType && (\n                    <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                      <AlertCircle className=\"h-4 w-4\" />\n                      {errors.propertyType?.[0]}\n                    </p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\">Description *</Label>\n                <Textarea\n                  id=\"description\"\n                  placeholder=\"Enter detailed property description\"\n                  value={formData.description}\n                  onChange={(e) => handleInputChange('description', e.target.value)}\n                  className={errors.description ? 'border-red-500' : ''}\n                  rows={4}\n                />\n                {errors.description && (\n                  <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    {errors.description?.[0]}\n                  </p>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        )\n\n      case 2:\n        return (\n          <LocationPicker\n            value={formData.location}\n            onChange={handleLocationChange}\n            errors={errors.location}\n          />\n        )\n\n      case 3:\n        return (\n          <OwnerDeveloperSelector\n            value={{\n              ...(formData.ownerId && { ownerId: formData.ownerId }),\n              ...(formData.developerId && { developerId: formData.developerId })\n            }}\n            onChange={handleOwnerDeveloperChange}\n            errors={{\n              ownerId: errors.ownerId,\n              developerId: errors.developerId\n            }}\n          />\n        )\n\n      case 4:\n        return (\n          <FinancialDetails\n            value={{\n              expectedReturns: formData.expectedReturns,\n              maturityPeriodMonths: formData.maturityPeriodMonths,\n              totalStocks: formData.totalStocks,\n              pricePerStock: formData.pricePerStock,\n              availableStocks: formData.availableStocks,\n              stockPrefix: formData.stockPrefix,\n              stockStartNumber: formData.stockStartNumber,\n              referralCommissionRate: formData.referralCommissionRate || 0,\n              salesCommissionRate: formData.salesCommissionRate || 0,\n              referralCommissionPerStock: formData.referralCommissionPerStock || 0,\n              salesCommissionPerStock: formData.salesCommissionPerStock || 0,\n              commissionType: (formData.commissionType as 'percentage' | 'fixed') || 'percentage'\n            }}\n            onChange={handleFinancialChange}\n            errors={errors}\n          />\n        )\n\n      case 5:\n        return (\n          <ConstructionTimeline\n            value={{\n              constructionStatus: formData.constructionStatus,\n              launchDate: formData.launchDate,\n              expectedCompletion: formData.expectedCompletion,\n              ...(formData.actualCompletion && { actualCompletion: formData.actualCompletion }),\n              ...(formData.constructionTimeline && { constructionTimeline: formData.constructionTimeline })\n            }}\n            onChange={handleConstructionChange}\n            errors={{\n              constructionStatus: errors.constructionStatus,\n              launchDate: errors.launchDate,\n              expectedCompletion: errors.expectedCompletion,\n              actualCompletion: errors.actualCompletion,\n              constructionTimeline: errors.constructionTimeline\n            }}\n          />\n        )\n\n      case 6:\n        return (\n          <PropertyImagesUpload\n            images={formData.images}\n            onImagesChange={(images) => handleInputChange('images', images)}\n          />\n        )\n\n      case 7:\n        return (\n          <EnhancedFeaturesAmenities\n            features={formData.features || []}\n            amenities={formData.amenities || []}\n            onFeaturesChange={(features) => handleInputChange('features', features)}\n            onAmenitiesChange={(amenities) => handleInputChange('amenities', amenities)}\n            featuresList={featuresList}\n            amenitiesList={amenitiesList}\n          />\n        )\n\n      case 8:\n        return (\n          <LegalDocumentsUpload\n            legalDocuments={formData.legalDocuments || []}\n            onLegalDocumentsChange={(documents) => handleInputChange('legalDocuments', documents)}\n          />\n        )\n\n      case 9:\n        return (\n          <BookingForm\n            propertyData={{\n              name: formData.name,\n              location: {\n                address: formData.location.address,\n                city: formData.location.city,\n                state: formData.location.state,\n                pincode: formData.location.pincode\n              },\n              pricePerStock: formData.pricePerStock,\n              totalStocks: formData.totalStocks,\n              expectedReturns: formData.expectedReturns,\n              maturityPeriodMonths: formData.maturityPeriodMonths,\n              propertyType: formData.propertyType\n            }}\n            onBookingComplete={(bookingData) => {\n              console.log('Booking completed:', bookingData)\n              toast.success('Booking form completed successfully!')\n            }}\n          />\n        )\n\n      default:\n        return null\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6 space-y-6\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-6 text-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div className=\"flex items-start gap-4 flex-1\">\n              <Button\n                variant=\"ghost\"\n                onClick={() => router.back()}\n                className=\"flex-shrink-0 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:text-white transition-all duration-200\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                <span className=\"hidden sm:inline\">Back</span>\n              </Button>\n              <div className=\"min-w-0 flex-1\">\n                <h1 className=\"text-2xl sm:text-3xl font-bold text-white mb-2 flex items-center gap-3\">\n                  <div className=\"p-2 bg-white/20 backdrop-blur-sm rounded-xl flex-shrink-0\">\n                    <Building className=\"h-5 w-5 sm:h-6 sm:w-6 text-white\" />\n                  </div>\n                  <span className=\"leading-tight\">Add New Property</span>\n                </h1>\n                <p className=\"text-green-100 text-sm sm:text-base max-w-2xl leading-relaxed\">\n                  Create a comprehensive property listing with images, features, amenities, and legal documents\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center gap-3 flex-shrink-0\">\n              <Badge className=\"bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30 transition-all duration-200\">\n                Step {currentStep} of 8\n              </Badge>\n            </div>\n          </div>\n        </div>\n\n        {/* Progress Steps */}\n          <div className=\"mb-8\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6\">\n              {/* Desktop Progress Steps */}\n              <div className=\"hidden lg:flex items-center justify-between\">\n                {steps.map((step, index) => (\n                  <div key={step.id} className=\"flex items-center flex-1\">\n                    <div className=\"flex items-center\">\n                      <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${\n                        currentStep >= step.id\n                          ? 'bg-green-500 border-green-500 text-white shadow-lg'\n                          : currentStep === step.id - 1\n                          ? 'border-green-300 text-green-500 bg-green-50'\n                          : 'border-gray-300 text-gray-400 bg-gray-50'\n                      }`}>\n                        {React.createElement(step.icon, { className: \"h-6 w-6\" })}\n                      </div>\n                      <div className=\"ml-4\">\n                        <p className={`text-sm font-semibold transition-colors ${\n                          currentStep >= step.id ? 'text-green-600' : 'text-gray-500'\n                        }`}>\n                          Step {step.id}\n                        </p>\n                        <p className={`text-xs transition-colors ${\n                          currentStep >= step.id ? 'text-gray-700' : 'text-gray-400'\n                        }`}>\n                          <span className=\"hidden xl:inline\">{step.title}</span>\n                          <span className=\"xl:hidden\">{step.shortTitle}</span>\n                        </p>\n                      </div>\n                    </div>\n                    {index < steps.length - 1 && (\n                      <div className={`flex-1 h-0.5 mx-6 transition-all duration-300 ${\n                        currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'\n                      }`} />\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              {/* Mobile Progress Steps */}\n              <div className=\"lg:hidden\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-green-500 text-white shadow-lg\">\n                      {steps[currentStep - 1] && React.createElement(steps[currentStep - 1]!.icon, { className: \"h-5 w-5\" })}\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-semibold text-green-600\">\n                        Step {currentStep} of {steps.length}\n                      </p>\n                      <p className=\"text-xs text-gray-600\">\n                        <span className=\"hidden sm:inline\">{steps[currentStep - 1]?.title}</span>\n                        <span className=\"sm:hidden\">{steps[currentStep - 1]?.shortTitle}</span>\n                      </p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Progress Bar */}\n                <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div\n                    className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${(currentStep / steps.length) * 100}%` }}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Step Content */}\n          <div className=\"mb-6 sm:mb-8\">\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 lg:p-8\">\n              {renderStepContent()}\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6\">\n            <div className=\"flex flex-col sm:flex-row items-center justify-between gap-4\">\n              <Button\n                variant=\"outline\"\n                onClick={handlePrevious}\n                disabled={currentStep === 1}\n                className=\"w-full sm:w-auto border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Previous\n              </Button>\n\n              <div className=\"flex items-center gap-3 w-full sm:w-auto\">\n                {currentStep < steps.length ? (\n                  <Button\n                    onClick={handleNext}\n                    className=\"w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white shadow-lg transition-all duration-200\"\n                  >\n                    Next\n                    <ArrowRight className=\"h-4 w-4 ml-2\" />\n                  </Button>\n                ) : (\n                  <Button\n                    onClick={handleSubmit}\n                    disabled={isSubmitting || isCreating}\n                    className=\"w-full sm:w-auto bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg transition-all duration-200 disabled:opacity-50\"\n                  >\n                    {isSubmitting || isCreating ? (\n                      <>\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                        Creating Property...\n                      </>\n                    ) : (\n                      <>\n                        <Save className=\"h-4 w-4 mr-2\" />\n                        Create Property\n                      </>\n                    )}\n                  </Button>\n                )}\n              </div>\n            </div>\n          </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAoCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAzDA;;;;;;;;;;;;;;;;;;;;;;;AAwEA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAe,OAAO;QAAe,MAAM;IAAK;IACzD;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAc,OAAO;QAAc,MAAM;IAAK;IACvD;QAAE,OAAO;QAAQ,OAAO;QAAQ,MAAM;IAAK;IAC3C;QAAE,OAAO;QAAS,OAAO;QAAa,MAAM;IAAM;IAClD;QAAE,OAAO;QAAU,OAAO;QAAU,MAAM;IAAI;IAC9C;QAAE,OAAO;QAAgB,OAAO;QAAgB,MAAM;IAAK;CAC5D;AAED,MAAM,gBAAgB;IACpB,kBAAkB;IAClB;IAAiB;IAAO;IAAW;IAAY;IAAU;IACzD;IAAc;IAAgB;IAAY;IAAQ;IAAY;IAE9D,oBAAoB;IACpB;IAAyB;IAAmB;IAAmB;IAC/D;IAAW;IAAmB;IAAe;IAC7C;IAAwB;IAAiB;IAAiB;IAE1D,oBAAoB;IACpB;IAAiB;IAAmB;IAAe;IACnD;IAAmB;IAAoB;IAAoB;IAE3D,cAAc;IACd;IAAqB;IAAgB;IAAmB;IACxD;IAAY;IAAO;IAAmB;IAEtC,YAAY;IACZ;IAAuB;IAAY;IAAmB;IACtD;IAAmB;IAAe;IAAwB;IAE1D,mBAAmB;IACnB;IAAsB;IAAkB;IAAY;IACpD;IAAkB;IAAuB;IAAgB;CAC1D;AAED,MAAM,eAAe;IACnB,sBAAsB;IACtB;IAAmB;IAAkB;IAAkB;IACvD;IAAc;IAAe;IAAgB;IAC7C;IAAW;IAAW;IAAY;IAElC,oBAAoB;IACpB;IAAmB;IAAmB;IAAmB;IACzD;IAAqB;IAAsB;IAAoB;IAC/D;IAAoB;IAAoB;IAAqB;IAE7D,sBAAsB;IACtB;IAAyB;IAAmB;IAAe;IAC3D;IAAkB;IAAmB;IAAsB;IAE3D,oBAAoB;IACpB;IAAgB;IAA4B;IAAgB;IAC5D;IAAyB;IAAsB;IAAoB;IAEnE,kBAAkB;IAClB;IAAgB;IAAkB;IAAoB;IACtD;IAAe;IAAe;IAAe;IAE7C,gBAAgB;IAChB;IAAyB;IAAe;IAAiB;IACzD;IAAyB;IAAmB;IAAuB;CACpE;AAIc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,EAAE,WAAW,UAAU,EAAE,CAAC,GAAG,CAAA,GAAA,oIAAA,CAAA,4BAAyB,AAAD;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,oBAAoB;QACpB,MAAM;QACN,aAAa;QACb,cAAc;QAEd,WAAW;QACX,UAAU;YACR,SAAS;YACT,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;YACT,aAAa;gBACX,UAAU;gBACV,WAAW;YACb;QACF;QAEA,oBAAoB;QACpB,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,iBAAiB;QACjB,aAAa;QACb,kBAAkB;QAClB,wBAAwB;QACxB,qBAAqB;QACrB,4BAA4B;QAC5B,yBAAyB;QACzB,gBAAgB;QAEhB,uBAAuB;QACvB,oBAAoB;QACpB,YAAY;QACZ,oBAAoB;QACpB,kBAAkB;QAClB,sBAAsB;QAEtB,sBAAsB;QACtB,SAAS;QACT,aAAa;QAEb,oBAAoB;QACpB,WAAW,EAAE;QACb,UAAU,EAAE;QACZ,gBAAgB,CAAC;QAEjB,cAAc;QACd,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAElB,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,OAAO;YAAqB,YAAY;YAAc,MAAM,8RAAA,CAAA,WAAQ;QAAC;QAC9E;YAAE,IAAI;YAAG,OAAO;YAAoB,YAAY;YAAY,MAAM,8RAAA,CAAA,SAAM;QAAC;QACzE;YAAE,IAAI;YAAG,OAAO;YAAqB,YAAY;YAAa,MAAM,sRAAA,CAAA,OAAI;QAAC;QACzE;YAAE,IAAI;YAAG,OAAO;YAAqB,YAAY;YAAa,MAAM,sSAAA,CAAA,aAAU;QAAC;QAC/E;YAAE,IAAI;YAAG,OAAO;YAAyB,YAAY;YAAY,MAAM,8RAAA,CAAA,WAAQ;QAAC;QAChF;YAAE,IAAI;YAAG,OAAO;YAAmB,YAAY;YAAU,MAAM,0RAAA,CAAA,SAAM;QAAC;QACtE;YAAE,IAAI;YAAG,OAAO;YAAwB,YAAY;YAAY,MAAM,0RAAA,CAAA,SAAM;QAAC;QAC7E;YAAE,IAAI;YAAG,OAAO;YAAmB,YAAY;YAAa,MAAM,0RAAA,CAAA,SAAM;QAAC;QACzE;YAAE,IAAI;YAAG,OAAO;YAAgB,YAAY;YAAW,MAAM,sSAAA,CAAA,aAAU;QAAC;KACzE;IAED,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,MAAM,QAAQ,CAAC,MAAM;YACvB,MAAM,OAAO,MAAM,KAAK,CAAC;YACzB,YAAY,CAAA;gBACV,MAAM,UAAU;oBAAE,GAAG,IAAI;gBAAC;gBAC1B,IAAI,UAAU;gBACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;oBACxC,MAAM,MAAM,IAAI,CAAC,EAAE;oBACnB,IAAI,QAAQ,WAAW;wBACrB,UAAU,OAAO,CAAC,IAAI;oBACxB;gBACF;gBACA,MAAM,UAAU,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;gBACrC,IAAI,YAAY,WAAW;oBACzB,OAAO,CAAC,QAAQ,GAAG;gBACrB;gBACA,OAAO;YACT;QACF,OAAO;YACL,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;QAEA,6BAA6B;QAC7B,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;gBACX,CAAC;QACH;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP;YACF,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,SAAS;YACd,CAAC;IACH;IAEA,MAAM,2BAA2B,CAAC;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,YAAY;YACjB,CAAC;IACH;IAEA,MAAM,6BAA6B,CAAC;QAClC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,GAAG,cAAc;YACnB,CAAC;IACH;IAIA,MAAM,sBAAsB;QAC1B,gCAAgC;QAChC,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,SAAS,IAAI,EAAE,QAAQ;oBAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,WAAW,EAAE,QAAQ;oBACjC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,YAAY,EAAE;oBAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,QAAQ;oBACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,SAAS,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC/C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,QAAQ,EAAE,MAAM,QAAQ;oBACpC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,SAAS,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC5C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,QAAQ,EAAE,OAAO,QAAQ;oBACrC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;oBAC7C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBAEA;YAEF,KAAK;gBACH,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,WAAW,EAAE;oBAC9C,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,IAAI,GAAG;oBAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,SAAS,eAAe,GAAG,KAAK;oBAClC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,IAAI,GAAG;oBACtD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,IAAI,GAAG;oBAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,oBAAoB,IAAI,SAAS,oBAAoB,IAAI,GAAG;oBACxE,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,SAAS,kBAAkB,EAAE;oBAChC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,UAAU,EAAE;oBACxB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,CAAC,SAAS,kBAAkB,EAAE;oBAChC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA;YAEF,KAAK;gBACH,IAAI,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;oBACpD,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IAAI;oBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,OAAO;gBACT;gBACA;YAEF,KAAK;gBAEH;YAEF,KAAK;gBAEH;YAEF,KAAK;gBAEH;YAEF;gBACE;QACJ;QAEA,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,IAAI,uBAAuB;YACzB,IAAI,cAAc,GAAG;gBACnB,eAAe,cAAc;YAC/B,OAAO;gBACL;YACF;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,uBAAuB;YAC1B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,2BAA2B;QAC3B,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,CAAC,SAAS,WAAW,EAAE,QAAQ;YAC9D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;YAChC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,gBAAgB;YAChB,kFAAkF;YAClF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,2EAA2E;gBAC3E,UAAU;oBACR,SAAS,SAAS,QAAQ,EAAE,SAAS,UAAU;oBAC/C,MAAM,SAAS,QAAQ,EAAE,MAAM,UAAU,uBAAuB,SAAS,QAAQ,EAAE,YAAY;oBAC/F,OAAO,SAAS,QAAQ,EAAE,OAAO,UAAU,wBAAwB,SAAS,QAAQ,EAAE,YAAY;oBAClG,SAAS,SAAS,QAAQ,EAAE,SAAS,UAAU;oBAC/C,SAAS,SAAS,QAAQ,EAAE,SAAS,UAAU;oBAC/C,+BAA+B;oBAC/B,GAAI,SAAS,QAAQ,EAAE,aAAa,YAAY,SAAS,QAAQ,EAAE,aAAa,aAAa;wBAC3F,aAAa;4BACX,UAAU,SAAS,QAAQ,CAAC,WAAW,CAAC,QAAQ;4BAChD,WAAW,SAAS,QAAQ,CAAC,WAAW,CAAC,SAAS;wBACpD;oBACF,CAAC;gBACH;gBACA,0DAA0D;gBAC1D,MAAM,SAAS,QAAQ,EAAE,MAAM,UAAU,uBAAuB,SAAS,QAAQ,EAAE,YAAY;gBAC/F,OAAO,SAAS,QAAQ,EAAE,OAAO,UAAU,wBAAwB,SAAS,QAAQ,EAAE,YAAY;gBAClG,2BAA2B;gBAC3B,aAAa,SAAS,WAAW,IAAI;gBACrC,YAAY,SAAS,aAAa,IAAI;gBACtC,iBAAiB;gBACjB,wBAAwB,SAAS,sBAAsB,IAAI;gBAC3D,qBAAqB,SAAS,mBAAmB,IAAI;gBACrD,4BAA4B,SAAS,0BAA0B,IAAI;gBACnE,yBAAyB,SAAS,uBAAuB,IAAI;gBAC7D,gBAAgB,SAAS,cAAc,IAAI;gBAC3C,kCAAkC;gBAClC,oBAAoB,sBAAsB,SAAS,kBAAkB;gBACrE,iEAAiE;gBACjE,QAAQ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK;oBAChC,IAAI,OAAO,QAAQ,UAAU;wBAC3B,OAAO;4BACL,KAAK;4BACL,MAAM,CAAC,eAAe,EAAE,QAAQ,GAAG;4BACnC,MAAM;4BACN,KAAK,CAAC,MAAM,EAAE,QAAQ,GAAG;wBAC3B;oBACF,OAAO;wBACL,OAAO;4BACL,KAAK,IAAI,GAAG,IAAI;4BAChB,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE,QAAQ,GAAG;4BAC/C,MAAM,IAAI,IAAI,IAAI;4BAClB,KAAK,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;wBACtC;oBACF;gBACF;gBACA,8CAA8C;gBAC9C,gBAAgB,MAAM,OAAO,CAAC,SAAS,cAAc,KAAK,SAAS,cAAc,CAAC,MAAM,GAAG,IACvF;oBAAE,WAAW,SAAS,cAAc;oBAAE,cAAc,EAAE;oBAAE,YAAY,EAAE;gBAAC,IACvE;oBAAE,WAAW,EAAE;oBAAE,cAAc,EAAE;oBAAE,YAAY,EAAE;gBAAC;gBACtD,sDAAsD;gBACtD,GAAI,SAAS,OAAO,EAAE,UAAU;oBAAE,SAAS,SAAS,OAAO,CAAC,IAAI;gBAAG,CAAC;gBACpE,GAAI,SAAS,WAAW,EAAE,UAAU;oBAAE,aAAa,SAAS,WAAW,CAAC,IAAI;gBAAG,CAAC;gBAChF,uBAAuB;gBACvB,kBAAkB,SAAS,gBAAgB;gBAC3C,uDAAuD;gBACvD,WAAW,SAAS,SAAS,EAAE,IAAI,CAAC,KAAK;oBACvC,IAAI,OAAO,QAAQ,UAAU;wBAC3B,OAAO;4BACL,KAAK;4BACL,MAAM,CAAC,SAAS,EAAE,QAAQ,GAAG;4BAC7B,MAAM;4BACN,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO;4BACL,KAAK,IAAI,GAAG,IAAI;4BAChB,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,QAAQ,GAAG;4BACzC,MAAM,IAAI,IAAI,IAAI;4BAClB,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,GAAG;wBACpC;oBACF;gBACF;gBACA,QAAQ,SAAS,MAAM,EAAE,IAAI,CAAA,MAAO,OAAO,QAAQ,WAAW,MAAM,IAAI,GAAG,KAAK,EAAE;gBAClF,QAAQ,SAAS,MAAM;YACzB;YAEA,mBAAmB;YACnB,SAAS,uBAAuB,OAAe;gBAC7C,IAAI,CAAC,SAAS,OAAO;gBACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAChD,OAAO,KAAK,CAAC,EAAE,IAAI;YACrB;YAEA,SAAS,wBAAwB,OAAe;gBAC9C,IAAI,CAAC,SAAS,OAAO;gBACrB,MAAM,QAAQ,QAAQ,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBAChD,sCAAsC;gBACtC,KAAK,MAAM,QAAQ,MAAO;oBACxB,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,oBAAoB,KAAK,WAAW,GAAG,QAAQ,CAAC,OAAO,OAAO;oBAC9F,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,UAAU,OAAO;oBACjD,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,YAAY,OAAO;oBACnD,IAAI,KAAK,WAAW,GAAG,QAAQ,CAAC,cAAc,OAAO;gBACrD,4BAA4B;gBAC9B;gBACA,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IAAI;YACpC;YAEA,SAAS,sBAAsB,MAAc;gBAC3C,MAAM,YAAoC;oBACxC,aAAa;oBACb,YAAY;oBACZ,cAAc;oBACd,aAAa;oBACb,iBAAiB;gBACnB;gBACA,OAAO,SAAS,CAAC,OAAO,IAAI;YAC9B;YAEA,sBAAsB;YACtB,MAAM,cAAmB;gBAAE,GAAG,WAAW;YAAC;YAC1C,IAAI,CAAC,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI;gBAC7D,OAAO,YAAY,OAAO;YAC5B;YACA,IAAI,CAAC,YAAY,WAAW,IAAI,YAAY,WAAW,CAAC,IAAI,OAAO,IAAI;gBACrE,OAAO,YAAY,WAAW;YAChC;YACA,IAAI,CAAC,YAAY,gBAAgB,IAAI,YAAY,gBAAgB,CAAC,IAAI,OAAO,IAAI;gBAC/E,OAAO,YAAY,gBAAgB;YACrC;YAEA,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uBAAuB,SAAS,QAAQ;YACpD,QAAQ,GAAG,CAAC,sBAAsB,YAAY,QAAQ;YACtD,QAAQ,GAAG,CAAC,uBAAuB;gBACjC,SAAS,YAAY,QAAQ,EAAE;gBAC/B,MAAM,YAAY,QAAQ,EAAE;gBAC5B,OAAO,YAAY,QAAQ,EAAE;gBAC7B,SAAS,YAAY,QAAQ,EAAE;gBAC/B,SAAS,YAAY,QAAQ,EAAE;YACjC;YACA,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,MAAM,YAAY,IAAI;gBACtB,cAAc,YAAY,YAAY;gBACtC,aAAa,YAAY,WAAW;gBACpC,YAAY,YAAY,UAAU;gBAClC,iBAAiB,YAAY,eAAe;gBAC5C,sBAAsB,YAAY,oBAAoB;gBACtD,aAAa,YAAY,MAAM,EAAE;YACnC;YACA,QAAQ,GAAG,CAAC,qBAAqB,KAAK,SAAS,CAAC,aAAa,MAAM;YAEnE,kEAAkE;YAClE,MAAM,YAAY;gBAChB,GAAG,WAAW;YAChB;YAEA,sCAAsC;YACtC,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAA;gBAC7B,IAAI,SAAS,CAAC,IAAI,KAAK,aAAa,SAAS,CAAC,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI,KAAK,IAAI;oBACpF,OAAO,SAAS,CAAC,IAAI;gBACvB;YACF;YAEA,QAAQ,GAAG,CAAC,wBAAwB,UAAU,QAAQ;YACtD,MAAM,eAAe,WAAW,MAAM;YACtC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAkB,KAAK,SAAS,CAAC,OAAO,MAAM;YAE5D,IAAI,OAAO,MAAM,SAAS;gBACxB,0CAA0C;gBAC1C,MAAM,mBAAmB,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAC/C,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,QAAQ,EAAE,EAAE,IAAI,GAAG,EAAE,EACjD,IAAI,CAAC;gBACP,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,kBAAkB,EAAE;oBAAE,UAAU;gBAAK;YAC1E,OAAO,IAAI,OAAO,MAAM,SAAS;gBAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;YAC5C,OAAO,IAAI,OAAO,SAAS;gBACzB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;YACvC,OAAO;gBACL,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAQ;YACN,KAAK;gBACH,qBACE,6WAAC,gIAAA,CAAA,OAAI;;sCACH,6WAAC,gIAAA,CAAA,aAAU;;8CACT,6WAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6WAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6WAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6WAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAO;;;;;;8DACtB,6WAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,aAAY;oDACZ,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACzD,WAAW,OAAO,IAAI,GAAG,mBAAmB;;;;;;gDAE7C,OAAO,IAAI,kBACV,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,IAAI,EAAE,CAAC,EAAE;;;;;;;;;;;;;sDAKvB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAe;;;;;;8DAC9B,6WAAC,kIAAA,CAAA,SAAM;oDACL,OAAO,SAAS,YAAY;oDAC5B,eAAe,CAAC,QAAU,kBAAkB,gBAAgB;;sEAE5D,6WAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAW,OAAO,YAAY,GAAG,mBAAmB;sEACjE,cAAA,6WAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6WAAC,kIAAA,CAAA,gBAAa;sEACX,cAAc,GAAG,CAAC,CAAC,qBAClB,6WAAC,kIAAA,CAAA,aAAU;oEAAkB,OAAO,KAAK,KAAK;8EAC5C,cAAA,6WAAC;wEAAI,WAAU;;0FACb,6WAAC;0FAAM,KAAK,IAAI;;;;;;4EACf,KAAK,KAAK;;;;;;;mEAHE,KAAK,KAAK;;;;;;;;;;;;;;;;gDAShC,OAAO,YAAY,kBAClB,6WAAC;oDAAE,WAAU;;sEACX,6WAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,OAAO,YAAY,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;8CAMjC,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6WAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,WAAW,OAAO,WAAW,GAAG,mBAAmB;4CACnD,MAAM;;;;;;wCAEP,OAAO,WAAW,kBACjB,6WAAC;4CAAE,WAAU;;8DACX,6WAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,OAAO,WAAW,EAAE,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAQtC,KAAK;gBACH,qBACE,6WAAC,yJAAA,CAAA,UAAc;oBACb,OAAO,SAAS,QAAQ;oBACxB,UAAU;oBACV,QAAQ,OAAO,QAAQ;;;;;;YAI7B,KAAK;gBACH,qBACE,6WAAC,iKAAA,CAAA,UAAsB;oBACrB,OAAO;wBACL,GAAI,SAAS,OAAO,IAAI;4BAAE,SAAS,SAAS,OAAO;wBAAC,CAAC;wBACrD,GAAI,SAAS,WAAW,IAAI;4BAAE,aAAa,SAAS,WAAW;wBAAC,CAAC;oBACnE;oBACA,UAAU;oBACV,QAAQ;wBACN,SAAS,OAAO,OAAO;wBACvB,aAAa,OAAO,WAAW;oBACjC;;;;;;YAIN,KAAK;gBACH,qBACE,6WAAC,2JAAA,CAAA,UAAgB;oBACf,OAAO;wBACL,iBAAiB,SAAS,eAAe;wBACzC,sBAAsB,SAAS,oBAAoB;wBACnD,aAAa,SAAS,WAAW;wBACjC,eAAe,SAAS,aAAa;wBACrC,iBAAiB,SAAS,eAAe;wBACzC,aAAa,SAAS,WAAW;wBACjC,kBAAkB,SAAS,gBAAgB;wBAC3C,wBAAwB,SAAS,sBAAsB,IAAI;wBAC3D,qBAAqB,SAAS,mBAAmB,IAAI;wBACrD,4BAA4B,SAAS,0BAA0B,IAAI;wBACnE,yBAAyB,SAAS,uBAAuB,IAAI;wBAC7D,gBAAgB,AAAC,SAAS,cAAc,IAA+B;oBACzE;oBACA,UAAU;oBACV,QAAQ;;;;;;YAId,KAAK;gBACH,qBACE,6WAAC,+JAAA,CAAA,UAAoB;oBACnB,OAAO;wBACL,oBAAoB,SAAS,kBAAkB;wBAC/C,YAAY,SAAS,UAAU;wBAC/B,oBAAoB,SAAS,kBAAkB;wBAC/C,GAAI,SAAS,gBAAgB,IAAI;4BAAE,kBAAkB,SAAS,gBAAgB;wBAAC,CAAC;wBAChF,GAAI,SAAS,oBAAoB,IAAI;4BAAE,sBAAsB,SAAS,oBAAoB;wBAAC,CAAC;oBAC9F;oBACA,UAAU;oBACV,QAAQ;wBACN,oBAAoB,OAAO,kBAAkB;wBAC7C,YAAY,OAAO,UAAU;wBAC7B,oBAAoB,OAAO,kBAAkB;wBAC7C,kBAAkB,OAAO,gBAAgB;wBACzC,sBAAsB,OAAO,oBAAoB;oBACnD;;;;;;YAIN,KAAK;gBACH,qBACE,6WAAC,+JAAA,CAAA,UAAoB;oBACnB,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,CAAC,SAAW,kBAAkB,UAAU;;;;;;YAI9D,KAAK;gBACH,qBACE,6WAAC,oKAAA,CAAA,UAAyB;oBACxB,UAAU,SAAS,QAAQ,IAAI,EAAE;oBACjC,WAAW,SAAS,SAAS,IAAI,EAAE;oBACnC,kBAAkB,CAAC,WAAa,kBAAkB,YAAY;oBAC9D,mBAAmB,CAAC,YAAc,kBAAkB,aAAa;oBACjE,cAAc;oBACd,eAAe;;;;;;YAIrB,KAAK;gBACH,qBACE,6WAAC,+JAAA,CAAA,UAAoB;oBACnB,gBAAgB,SAAS,cAAc,IAAI,EAAE;oBAC7C,wBAAwB,CAAC,YAAc,kBAAkB,kBAAkB;;;;;;YAIjF,KAAK;gBACH,qBACE,6WAAC,sJAAA,CAAA,UAAW;oBACV,cAAc;wBACZ,MAAM,SAAS,IAAI;wBACnB,UAAU;4BACR,SAAS,SAAS,QAAQ,CAAC,OAAO;4BAClC,MAAM,SAAS,QAAQ,CAAC,IAAI;4BAC5B,OAAO,SAAS,QAAQ,CAAC,KAAK;4BAC9B,SAAS,SAAS,QAAQ,CAAC,OAAO;wBACpC;wBACA,eAAe,SAAS,aAAa;wBACrC,aAAa,SAAS,WAAW;wBACjC,iBAAiB,SAAS,eAAe;wBACzC,sBAAsB,SAAS,oBAAoB;wBACnD,cAAc,SAAS,YAAY;oBACrC;oBACA,mBAAmB,CAAC;wBAClB,QAAQ,GAAG,CAAC,sBAAsB;wBAClC,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;;;;;;YAIN;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC,+IAAA,CAAA,UAAe;kBACd,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,6WAAC,oSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6WAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAErC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAG,WAAU;;kEACZ,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC,8RAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,6WAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAElC,6WAAC;gDAAE,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;0CAKjF,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;wCAAwG;wCACjH;wCAAY;;;;;;;;;;;;;;;;;;;;;;;8BAOxB,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6WAAC;wCAAkB,WAAU;;0DAC3B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAW,CAAC,6FAA6F,EAC5G,eAAe,KAAK,EAAE,GAClB,uDACA,gBAAgB,KAAK,EAAE,GAAG,IAC1B,gDACA,4CACJ;kEACC,cAAA,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE;4DAAE,WAAW;wDAAU;;;;;;kEAEzD,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAE,WAAW,CAAC,wCAAwC,EACrD,eAAe,KAAK,EAAE,GAAG,mBAAmB,iBAC5C;;oEAAE;oEACI,KAAK,EAAE;;;;;;;0EAEf,6WAAC;gEAAE,WAAW,CAAC,0BAA0B,EACvC,eAAe,KAAK,EAAE,GAAG,kBAAkB,iBAC3C;;kFACA,6WAAC;wEAAK,WAAU;kFAAoB,KAAK,KAAK;;;;;;kFAC9C,6WAAC;wEAAK,WAAU;kFAAa,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;4CAIjD,QAAQ,MAAM,MAAM,GAAG,mBACtB,6WAAC;gDAAI,WAAW,CAAC,8CAA8C,EAC7D,cAAc,KAAK,EAAE,GAAG,iBAAiB,eACzC;;;;;;;uCA5BI,KAAK,EAAE;;;;;;;;;;0CAmCrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAI,WAAU;8DACZ,KAAK,CAAC,cAAc,EAAE,kBAAI,oUAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,CAAC,cAAc,EAAE,CAAE,IAAI,EAAE;wDAAE,WAAW;oDAAU;;;;;;8DAEtG,6WAAC;;sEACC,6WAAC;4DAAE,WAAU;;gEAAuC;gEAC5C;gEAAY;gEAAK,MAAM,MAAM;;;;;;;sEAErC,6WAAC;4DAAE,WAAU;;8EACX,6WAAC;oEAAK,WAAU;8EAAoB,KAAK,CAAC,cAAc,EAAE,EAAE;;;;;;8EAC5D,6WAAC;oEAAK,WAAU;8EAAa,KAAK,CAAC,cAAc,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO7D,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,AAAC,cAAc,MAAM,MAAM,GAAI,IAAI,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQnE,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;8BAKL,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,gBAAgB;gCAC1B,WAAU;;kDAEV,6WAAC,oSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIxC,6WAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,MAAM,iBACzB,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;;wCACX;sDAEC,6WAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;yDAGxB,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB;oCAC1B,WAAU;8CAET,gBAAgB,2BACf;;0DACE,6WAAC;gDAAI,WAAU;;;;;;4CAAuE;;qEAIxF;;0DACE,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD", "debugId": null}}]}