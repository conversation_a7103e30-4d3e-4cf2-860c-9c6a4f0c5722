{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6WAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,8QAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6WAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,+QAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,+QAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6WAAC,+QAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,oSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,+QAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,wSAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,+QAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6WAAC,+QAAA,CAAA,SAAsB;kBACrB,cAAA,6WAAC,+QAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6WAAC;;;;;8BACD,6WAAC,+QAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6WAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,+QAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,+QAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6WAAC,+QAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6WAAC;gBAAK,WAAU;0BACd,cAAA,6WAAC,+QAAA,CAAA,gBAA6B;8BAC5B,cAAA,6WAAC,wRAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6WAAC,+QAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+QAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,+QAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,+QAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,6WAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,6WAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,6WAAC;;;;;IACV;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,6WAAC;;;;;0BACD,6WAAC;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,6QAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,6QAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6QAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6WAAC,6QAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,6QAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nconst AvatarInitials = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { name: string }\n>(({ className, name, ...props }, ref) => {\n  const initials = name\n    .split(' ')\n    .map(word => word.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"flex h-full w-full items-center justify-center rounded-full bg-sky-500 text-white text-sm font-medium\",\n        className\n      )}\n      {...props}\n    >\n      {initials}\n    </div>\n  )\n})\nAvatarInitials.displayName = \"AvatarInitials\"\n\nexport { Avatar, AvatarImage, AvatarFallback, AvatarInitials }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAChC,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,6QAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,6QAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,6QAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,6QAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 830, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6WAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-white\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,oUAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC,8QAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8QACA;QAED,GAAG,KAAK;kBAET,cAAA,6WAAC,8QAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6WAAC,wRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,8QAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard,\n  Heart\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"user-wishlists\",\n          label: \"User Wishlists\",\n          icon: Heart,\n          href: \"/wishlists\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Owners & Developers\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-owner\",\n          label: \"Add Owner/Developer\",\n          icon: UserPlus,\n          href: \"/properties/owners/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;AAyCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;IAC3E,MAAM,WAAW,CAAA,GAAA,iQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,QAAU,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,oSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,gSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+SAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,0SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,4RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,wRAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,8RAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,qHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,sSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,qHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,qHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,gIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;0CAAE;;;;;;0CACH,6WAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,6WAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,6WAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,6WAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6WAAC,2RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,6WAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,6WAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,6WAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,6WAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,mIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC,uIAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,6WAAC;oBAAI,WAAU;;sCAEb,6WAAC;4BAAO,WAAU;sCAChB,cAAA,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDAEb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,0RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6WAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,6WAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,6WAAC;4CAAI,WAAU;;8DAEb,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,6WAAC,kTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,6WAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,6WAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6WAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAI,WAAU;sFACb,cAAA,6WAAC;gFAAI,WAAU;0FACb,cAAA,6WAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,6WAAC,wSAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,6WAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,sRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,6WAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,6WAAC,8RAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,6WAAC;oEAAI,WAAU;8EACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,6WAAC,8RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,6WAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/FileUpload/EnhancedS3Upload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { useDropzone } from 'react-dropzone'\nimport { toast } from 'sonner'\nimport { Upload, X, FileText, Image, Video, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Progress } from '@/components/ui/progress'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  uploadFileComplete, \n  uploadMultipleFiles, \n  validateImageFile, \n  validateDocumentFile,\n  formatFileSize,\n  getFileTypeIcon \n} from '@/lib/s3Upload'\n\ninterface EnhancedS3UploadProps {\n  uploadType: 'property-image' | 'property-document' | 'user-avatar' | 'user-document'\n  entityId?: string // propertyId or userId depending on uploadType\n  onUploadComplete?: (results: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => void\n  onUploadError?: (error: string) => void\n  maxFiles?: number\n  disabled?: boolean\n  className?: string\n  showPreview?: boolean\n  allowMultiple?: boolean\n}\n\ninterface UploadingFile {\n  file: File\n  progress: number\n  status: 'uploading' | 'success' | 'error'\n  fileKey?: string\n  publicUrl?: string\n  error?: string\n  id: string\n}\n\nconst EnhancedS3Upload: React.FC<EnhancedS3UploadProps> = ({\n  uploadType,\n  entityId,\n  onUploadComplete,\n  onUploadError,\n  maxFiles = 5,\n  disabled = false,\n  className = '',\n  showPreview = true,\n  allowMultiple = true,\n}) => {\n  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([])\n  const [isUploading, setIsUploading] = useState(false)\n\n  const getFileTypeValidation = () => {\n    const validations: Record<string, { accept: Record<string, string[]>; maxSize: number }> = {\n      'property-image': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 10 * 1024 * 1024, // 10MB\n      },\n      'property-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'application/msword': ['.doc', '.docx'],\n          'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']\n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-document': {\n        accept: { \n          'application/pdf': ['.pdf'], \n          'image/*': ['.jpg', '.jpeg', '.png'] \n        },\n        maxSize: 5 * 1024 * 1024, // 5MB\n      },\n      'user-avatar': {\n        accept: { 'image/*': ['.jpg', '.jpeg', '.png', '.webp'] },\n        maxSize: 2 * 1024 * 1024, // 2MB\n      },\n    }\n    return validations[uploadType] || validations['property-image']\n  }\n\n  const validateFile = (file: File): { valid: boolean; error?: string } => {\n    if (uploadType.includes('image') || uploadType === 'user-avatar') {\n      return validateImageFile(file)\n    } else {\n      return validateDocumentFile(file)\n    }\n  }\n\n  const uploadFileWithProgress = async (file: File, fileId: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Simulate progress for presigned URL upload\n      let progress = 0\n      const progressInterval = setInterval(() => {\n        progress += Math.random() * 30\n        if (progress > 90) progress = 90\n        \n        setUploadingFiles(prev =>\n          prev.map(f => f.id === fileId ? { ...f, progress: Math.round(progress) } : f)\n        )\n      }, 200)\n\n      uploadFileComplete(file, uploadType, entityId)\n        .then(result => {\n          clearInterval(progressInterval)\n          \n          if (result.success) {\n            setUploadingFiles(prev =>\n              prev.map(f => f.id === fileId ? {\n                ...f,\n                progress: 100,\n                status: 'success' as const,\n                ...(result.fileKey && { fileKey: result.fileKey }),\n                ...(result.publicUrl && { publicUrl: result.publicUrl }),\n              } : f)\n            )\n            resolve()\n          } else {\n            throw new Error(result.error || 'Upload failed')\n          }\n        })\n        .catch(error => {\n          clearInterval(progressInterval)\n          setUploadingFiles(prev =>\n            prev.map(f => f.id === fileId ? {\n              ...f,\n              status: 'error' as const,\n              error: error.message,\n            } : f)\n          )\n          reject(error)\n        })\n    })\n  }\n\n  const handleFileUpload = async (files: File[]) => {\n    if (disabled || isUploading) return\n\n    setIsUploading(true)\n    const newFiles: UploadingFile[] = files.map(file => ({\n      file,\n      progress: 0,\n      status: 'uploading' as const,\n      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n    }))\n\n    // Validate files\n    const validFiles: UploadingFile[] = []\n    const invalidFiles: string[] = []\n\n    newFiles.forEach(fileData => {\n      const validation = validateFile(fileData.file)\n      if (validation.valid) {\n        validFiles.push(fileData)\n      } else {\n        invalidFiles.push(`${fileData.file.name}: ${validation.error}`)\n      }\n    })\n\n    if (invalidFiles.length > 0) {\n      toast.error(`Invalid files: ${invalidFiles.join(', ')}`)\n      setIsUploading(false)\n      return\n    }\n\n    // Add files to uploading list\n    setUploadingFiles(prev => [...prev, ...validFiles])\n\n    try {\n      // Upload files\n      await Promise.all(\n        validFiles.map(fileData => \n          uploadFileWithProgress(fileData.file, fileData.id)\n        )\n      )\n\n      // Get successful uploads\n      const successfulUploads = uploadingFiles\n        .concat(validFiles)\n        .filter(f => f.status === 'success' && f.fileKey && f.publicUrl)\n        .map(f => ({\n          fileKey: f.fileKey!,\n          publicUrl: f.publicUrl!,\n          fileName: f.file.name,\n        }))\n\n      if (successfulUploads.length > 0) {\n        onUploadComplete?.(successfulUploads)\n        toast.success(`${successfulUploads.length} file(s) uploaded successfully`)\n      }\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Upload failed'\n      onUploadError?.(errorMessage)\n      toast.error(`Upload failed: ${errorMessage}`)\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const filesToUpload = allowMultiple ? acceptedFiles : acceptedFiles.slice(0, 1)\n    handleFileUpload(filesToUpload)\n  }, [uploadType, allowMultiple, disabled, isUploading])\n\n  const validation = getFileTypeValidation()\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: validation?.accept || {},\n    maxSize: validation?.maxSize || 10 * 1024 * 1024,\n    maxFiles: allowMultiple ? maxFiles : 1,\n    disabled: disabled || isUploading,\n    multiple: allowMultiple,\n  })\n\n  const removeFile = (fileId: string) => {\n    setUploadingFiles(prev => prev.filter(f => f.id !== fileId))\n  }\n\n  const getFileIcon = (file: File) => {\n    if (file.type.startsWith('image/')) return <Image className=\"w-4 h-4\" />\n    if (file.type.startsWith('video/')) return <Video className=\"w-4 h-4\" />\n    return <FileText className=\"w-4 h-4\" />\n  }\n\n  const getStatusIcon = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Loader2 className=\"w-4 h-4 animate-spin text-blue-500\" />\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      default:\n        return null\n    }\n  }\n\n  const getStatusBadge = (status: UploadingFile['status']) => {\n    switch (status) {\n      case 'uploading':\n        return <Badge variant=\"secondary\">Uploading</Badge>\n      case 'success':\n        return <Badge variant=\"default\" className=\"bg-green-500\">Success</Badge>\n      case 'error':\n        return <Badge variant=\"destructive\">Error</Badge>\n      default:\n        return null\n    }\n  }\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Dropzone */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-all duration-200\n          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n          ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : ''}\n          min-h-[100px] max-h-[150px] flex flex-col justify-center items-center\n        `}\n      >\n        <input {...getInputProps()} />\n        <Upload className={`w-6 h-6 mx-auto mb-1 ${isDragActive ? 'text-blue-500' : 'text-gray-400'}`} />\n        <p className=\"text-xs text-gray-600 font-medium\">\n          {isDragActive\n            ? 'Drop files here...'\n            : `Drag & drop ${allowMultiple ? 'files' : 'a file'} here, or click to select`\n          }\n        </p>\n        <p className=\"text-xs text-gray-500 mt-1\">\n          Max size: {validation ? Math.round(validation.maxSize / (1024 * 1024)) : 10}MB\n          {allowMultiple && ` • Max files: ${maxFiles}`}\n        </p>\n        <p className=\"text-xs text-gray-400 mt-1\">\n          Supported: {validation ? Object.values(validation.accept).flat().join(', ') : 'All files'}\n        </p>\n      </div>\n\n      {/* Uploading files list */}\n      {uploadingFiles.length > 0 && (\n        <div className=\"space-y-2 max-h-[200px] overflow-y-auto\">\n          <h4 className=\"text-xs font-medium text-gray-700\">\n            Upload Progress ({uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''})\n          </h4>\n          {uploadingFiles.map((uploadingFile) => (\n            <div key={uploadingFile.id} className=\"flex items-center space-x-2 p-2 bg-gray-50 rounded border\">\n              {getFileIcon(uploadingFile.file)}\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"text-xs font-medium text-gray-900 truncate\">\n                    {uploadingFile.file.name}\n                  </p>\n                  <div className=\"flex items-center space-x-1\">\n                    {getStatusIcon(uploadingFile.status)}\n                    {getStatusBadge(uploadingFile.status)}\n                  </div>\n                </div>\n                <p className=\"text-xs text-gray-500\">\n                  {formatFileSize(uploadingFile.file.size)}\n                </p>\n                {uploadingFile.status === 'uploading' && (\n                  <div className=\"flex items-center space-x-2 mt-1\">\n                    <Progress value={uploadingFile.progress} className=\"flex-1 h-1\" />\n                    <span className=\"text-xs text-gray-500 min-w-[2rem]\">\n                      {uploadingFile.progress}%\n                    </span>\n                  </div>\n                )}\n                {uploadingFile.status === 'success' && uploadingFile.publicUrl && (\n                  <div className=\"mt-2 space-y-2\">\n                    {/* Preview for images */}\n                    {uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg overflow-hidden bg-gray-100 border\">\n                          <img\n                            src={uploadingFile.publicUrl}\n                            alt={uploadingFile.file.name}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              const target = e.target as HTMLImageElement;\n                              target.style.display = 'none';\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            View full image\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            Image uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Preview for documents */}\n                    {!uploadingFile.file.type.startsWith('image/') && (\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg bg-gray-100 border flex items-center justify-center\">\n                          {uploadingFile.file.type === 'application/pdf' ? (\n                            <FileText className=\"w-8 h-8 text-red-500\" />\n                          ) : uploadingFile.file.type.includes('word') ? (\n                            <FileText className=\"w-8 h-8 text-blue-500\" />\n                          ) : (\n                            <FileText className=\"w-8 h-8 text-gray-500\" />\n                          )}\n                        </div>\n                        <div className=\"flex-1\">\n                          <a\n                            href={uploadingFile.publicUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-xs text-blue-600 hover:text-blue-800 underline block\"\n                          >\n                            Download document\n                          </a>\n                          <p className=\"text-xs text-gray-500 mt-1\">\n                            {uploadingFile.file.type === 'application/pdf' ? 'PDF Document' :\n                             uploadingFile.file.type.includes('word') ? 'Word Document' :\n                             'Document'} uploaded successfully\n                          </p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )}\n                {uploadingFile.status === 'error' && uploadingFile.error && (\n                  <p className=\"text-xs text-red-600 mt-1 bg-red-50 p-2 rounded\">\n                    {uploadingFile.error}\n                  </p>\n                )}\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => removeFile(uploadingFile.id)}\n                className=\"text-gray-400 hover:text-gray-600\"\n                disabled={uploadingFile.status === 'uploading'}\n              >\n                <X className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default EnhancedS3Upload\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAwCA,MAAM,mBAAoD,CAAC,EACzD,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,aAAa,EACb,WAAW,CAAC,EACZ,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACrB;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,MAAM,cAAqF;YACzF,kBAAkB;gBAChB,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,KAAK,OAAO;YACvB;YACA,qBAAqB;gBACnB,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,sBAAsB;wBAAC;wBAAQ;qBAAQ;oBACvC,2EAA2E;wBAAC;qBAAQ;gBACtF;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,iBAAiB;gBACf,QAAQ;oBACN,mBAAmB;wBAAC;qBAAO;oBAC3B,WAAW;wBAAC;wBAAQ;wBAAS;qBAAO;gBACtC;gBACA,SAAS,IAAI,OAAO;YACtB;YACA,eAAe;gBACb,QAAQ;oBAAE,WAAW;wBAAC;wBAAQ;wBAAS;wBAAQ;qBAAQ;gBAAC;gBACxD,SAAS,IAAI,OAAO;YACtB;QACF;QACA,OAAO,WAAW,CAAC,WAAW,IAAI,WAAW,CAAC,iBAAiB;IACjE;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,WAAW,QAAQ,CAAC,YAAY,eAAe,eAAe;YAChE,OAAO,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B,OAAO;YACL,OAAO,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;QAC9B;IACF;IAEA,MAAM,yBAAyB,OAAO,MAAY;QAChD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,6CAA6C;YAC7C,IAAI,WAAW;YACf,MAAM,mBAAmB,YAAY;gBACnC,YAAY,KAAK,MAAM,KAAK;gBAC5B,IAAI,WAAW,IAAI,WAAW;gBAE9B,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAAE,GAAG,CAAC;4BAAE,UAAU,KAAK,KAAK,CAAC;wBAAU,IAAI;YAE/E,GAAG;YAEH,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,YAAY,UAClC,IAAI,CAAC,CAAA;gBACJ,cAAc;gBAEd,IAAI,OAAO,OAAO,EAAE;oBAClB,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;gCAC9B,GAAG,CAAC;gCACJ,UAAU;gCACV,QAAQ;gCACR,GAAI,OAAO,OAAO,IAAI;oCAAE,SAAS,OAAO,OAAO;gCAAC,CAAC;gCACjD,GAAI,OAAO,SAAS,IAAI;oCAAE,WAAW,OAAO,SAAS;gCAAC,CAAC;4BACzD,IAAI;oBAEN;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;gBAClC;YACF,GACC,KAAK,CAAC,CAAA;gBACL,cAAc;gBACd,kBAAkB,CAAA,OAChB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS;4BAC9B,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO,MAAM,OAAO;wBACtB,IAAI;gBAEN,OAAO;YACT;QACJ;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,YAAY,aAAa;QAE7B,eAAe;QACf,MAAM,WAA4B,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACnD;gBACA,UAAU;gBACV,QAAQ;gBACR,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAChE,CAAC;QAED,iBAAiB;QACjB,MAAM,aAA8B,EAAE;QACtC,MAAM,eAAyB,EAAE;QAEjC,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,aAAa,aAAa,SAAS,IAAI;YAC7C,IAAI,WAAW,KAAK,EAAE;gBACpB,WAAW,IAAI,CAAC;YAClB,OAAO;gBACL,aAAa,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,KAAK,EAAE;YAChE;QACF;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,aAAa,IAAI,CAAC,OAAO;YACvD,eAAe;YACf;QACF;QAEA,8BAA8B;QAC9B,kBAAkB,CAAA,OAAQ;mBAAI;mBAAS;aAAW;QAElD,IAAI;YACF,eAAe;YACf,MAAM,QAAQ,GAAG,CACf,WAAW,GAAG,CAAC,CAAA,WACb,uBAAuB,SAAS,IAAI,EAAE,SAAS,EAAE;YAIrD,yBAAyB;YACzB,MAAM,oBAAoB,eACvB,MAAM,CAAC,YACP,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,OAAO,IAAI,EAAE,SAAS,EAC9D,GAAG,CAAC,CAAA,IAAK,CAAC;oBACT,SAAS,EAAE,OAAO;oBAClB,WAAW,EAAE,SAAS;oBACtB,UAAU,EAAE,IAAI,CAAC,IAAI;gBACvB,CAAC;YAEH,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,mBAAmB;gBACnB,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,kBAAkB,MAAM,CAAC,8BAA8B,CAAC;YAC3E;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,gBAAgB;YAChB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,cAAc;QAC9C,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,SAAS,CAAA,GAAA,oUAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1B,MAAM,gBAAgB,gBAAgB,gBAAgB,cAAc,KAAK,CAAC,GAAG;QAC7E,iBAAiB;IACnB,GAAG;QAAC;QAAY;QAAe;QAAU;KAAY;IAErD,MAAM,aAAa;IACnB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,6PAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ,YAAY,UAAU,CAAC;QAC/B,SAAS,YAAY,WAAW,KAAK,OAAO;QAC5C,UAAU,gBAAgB,WAAW;QACrC,UAAU,YAAY;QACtB,UAAU;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,kBAAkB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtD;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW,qBAAO,6WAAC,wRAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC5D,qBAAO,6WAAC,kSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,qSAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6WAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6WAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;YACpC,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,WAAU;8BAAe;;;;;;YAC3D,KAAK;gBACH,qBAAO,6WAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6WAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6WAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eAAe,+BAA+B,wCAAwC;UACxF,EAAE,YAAY,cAAc,kCAAkC,GAAG;;QAEnE,CAAC;;kCAED,6WAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6WAAC,0RAAA,CAAA,SAAM;wBAAC,WAAW,CAAC,qBAAqB,EAAE,eAAe,kBAAkB,iBAAiB;;;;;;kCAC7F,6WAAC;wBAAE,WAAU;kCACV,eACG,uBACA,CAAC,YAAY,EAAE,gBAAgB,UAAU,SAAS,yBAAyB,CAAC;;;;;;kCAGlF,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC7B,aAAa,KAAK,KAAK,CAAC,WAAW,OAAO,GAAG,CAAC,OAAO,IAAI,KAAK;4BAAG;4BAC3E,iBAAiB,CAAC,cAAc,EAAE,UAAU;;;;;;;kCAE/C,6WAAC;wBAAE,WAAU;;4BAA6B;4BAC5B,aAAa,OAAO,MAAM,CAAC,WAAW,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ;;;;;;;;;;;;;YAKjF,eAAe,MAAM,GAAG,mBACvB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;;4BAAoC;4BAC9B,eAAe,MAAM;4BAAC;4BAAM,eAAe,MAAM,KAAK,IAAI,MAAM;4BAAG;;;;;;;oBAEtF,eAAe,GAAG,CAAC,CAAC,8BACnB,6WAAC;4BAA2B,WAAU;;gCACnC,YAAY,cAAc,IAAI;8CAC/B,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;oDAAE,WAAU;8DACV,cAAc,IAAI,CAAC,IAAI;;;;;;8DAE1B,6WAAC;oDAAI,WAAU;;wDACZ,cAAc,cAAc,MAAM;wDAClC,eAAe,cAAc,MAAM;;;;;;;;;;;;;sDAGxC,6WAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,IAAI,CAAC,IAAI;;;;;;wCAExC,cAAc,MAAM,KAAK,6BACxB,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,oIAAA,CAAA,WAAQ;oDAAC,OAAO,cAAc,QAAQ;oDAAE,WAAU;;;;;;8DACnD,6WAAC;oDAAK,WAAU;;wDACb,cAAc,QAAQ;wDAAC;;;;;;;;;;;;;wCAI7B,cAAc,MAAM,KAAK,aAAa,cAAc,SAAS,kBAC5D,6WAAC;4CAAI,WAAU;;gDAEZ,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BAClC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACb,cAAA,6WAAC;gEACC,KAAK,cAAc,SAAS;gEAC5B,KAAK,cAAc,IAAI,CAAC,IAAI;gEAC5B,WAAU;gEACV,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gEACzB;;;;;;;;;;;sEAGJ,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;gDAQ/C,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,2BACnC,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;sEACZ,cAAc,IAAI,CAAC,IAAI,KAAK,kCAC3B,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;uEAClB,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,wBACnC,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;qFAEpB,6WAAC,kSAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAGxB,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEACC,MAAM,cAAc,SAAS;oEAC7B,QAAO;oEACP,KAAI;oEACJ,WAAU;8EACX;;;;;;8EAGD,6WAAC;oEAAE,WAAU;;wEACV,cAAc,IAAI,CAAC,IAAI,KAAK,oBAAoB,iBAChD,cAAc,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,kBAC3C;wEAAW;;;;;;;;;;;;;;;;;;;;;;;;;wCAOvB,cAAc,MAAM,KAAK,WAAW,cAAc,KAAK,kBACtD,6WAAC;4CAAE,WAAU;sDACV,cAAc,KAAK;;;;;;;;;;;;8CAI1B,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,cAAc,EAAE;oCAC1C,WAAU;oCACV,UAAU,cAAc,MAAM,KAAK;8CAEnC,cAAA,6WAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BAnGP,cAAc,EAAE;;;;;;;;;;;;;;;;;AA2GtC;uCAEe", "debugId": null}}]}