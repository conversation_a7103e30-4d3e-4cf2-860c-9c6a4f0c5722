{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/LocationPicker.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { MapPin, Navigation, Loader2, CheckCircle, AlertCircle } from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface LocationData {\n  address: string\n  street?: string // Optional for backward compatibility\n  city: string\n  state: string\n  country: string\n  pincode: string\n  coordinates?: {\n    latitude: number\n    longitude: number\n  }\n}\n\ninterface LocationPickerProps {\n  value: LocationData\n  onChange: (location: LocationData) => void\n  errors?: {\n    address?: string[]\n    street?: string[]\n    city?: string[]\n    state?: string[]\n    country?: string[]\n    pincode?: string[]\n    coordinates?: string[]\n  }\n}\n\nexport default function LocationPicker({ value, onChange, errors }: LocationPickerProps) {\n  const [isDetectingLocation, setIsDetectingLocation] = useState(false)\n  const [locationError, setLocationError] = useState<string | null>(null)\n\n  const handleInputChange = (field: keyof LocationData, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  const detectCurrentLocation = async () => {\n    if (!navigator.geolocation) {\n      setLocationError('Geolocation is not supported by this browser')\n      toast.error('Geolocation not supported')\n      return\n    }\n\n    setIsDetectingLocation(true)\n    setLocationError(null)\n\n    navigator.geolocation.getCurrentPosition(\n      async (position) => {\n        try {\n          const { latitude, longitude } = position.coords\n\n          // First update coordinates\n          onChange({\n            ...value,\n            coordinates: { latitude, longitude }\n          })\n\n          // Perform reverse geocoding to get complete address details\n          await reverseGeocode(latitude, longitude)\n\n        } catch (error) {\n          console.error('Error getting address:', error)\n          toast.error('Could not get address from location')\n        } finally {\n          setIsDetectingLocation(false)\n        }\n      },\n      (error) => {\n        setIsDetectingLocation(false)\n        let errorMessage = 'Failed to get location'\n\n        switch (error.code) {\n          case error.PERMISSION_DENIED:\n            errorMessage = 'Location access denied by user'\n            break\n          case error.POSITION_UNAVAILABLE:\n            errorMessage = 'Location information unavailable'\n            break\n          case error.TIMEOUT:\n            errorMessage = 'Location request timed out'\n            break\n        }\n\n        setLocationError(errorMessage)\n        toast.error(errorMessage)\n      },\n      {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 60000\n      }\n    )\n  }\n\n  // Reverse geocoding to get complete address from coordinates\n  const reverseGeocode = async (lat: number, lng: number) => {\n    try {\n      toast.info('Getting address details...', { duration: 2000 })\n\n      // Using multiple geocoding services for better accuracy\n      const geocodingServices = [\n        // Primary: BigDataCloud (free, no API key required)\n        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`,\n        // Fallback: OpenCage (requires API key but more accurate)\n        // `https://api.opencagedata.com/geocode/v1/json?q=${lat}+${lng}&key=YOUR_API_KEY&language=en&pretty=1`\n      ]\n\n      let addressData = null\n\n      for (const serviceUrl of geocodingServices) {\n        try {\n          const response = await fetch(serviceUrl)\n          if (response.ok) {\n            addressData = await response.json()\n            break\n          }\n        } catch (err) {\n          console.warn('Geocoding service failed:', err)\n          continue\n        }\n      }\n\n      if (addressData) {\n        // Extract address components from BigDataCloud response\n        const extractedAddress = extractAddressComponents(addressData, lat, lng)\n\n        // Update the form with complete address details\n        onChange(extractedAddress)\n\n        toast.success('📍 Address details auto-filled successfully!')\n      } else {\n        // Fallback: Just update coordinates\n        onChange({\n          ...value,\n          coordinates: { latitude: lat, longitude: lng }\n        })\n        toast.success('📍 Location detected! Please fill address details manually.')\n      }\n    } catch (error) {\n      console.error('Reverse geocoding error:', error)\n      // Fallback: Just fill coordinates\n      onChange({\n        ...value,\n        coordinates: { latitude: lat, longitude: lng }\n      })\n      toast.success('📍 Location coordinates detected! Please fill address details manually.')\n    }\n  }\n\n  // Extract and format address components from geocoding response\n  const extractAddressComponents = (data: any, lat: number, lng: number): LocationData => {\n    // Handle BigDataCloud response format\n    const address = [\n      data.locality,\n      data.localityInfo?.administrative?.[3]?.name,\n      data.localityInfo?.administrative?.[2]?.name,\n      data.localityInfo?.administrative?.[1]?.name\n    ].filter(Boolean).join(', ') ||\n    [data.city, data.principalSubdivision].filter(Boolean).join(', ') ||\n    data.countryName ||\n    value.address\n\n    const city = data.city ||\n                 data.locality ||\n                 data.localityInfo?.administrative?.[2]?.name ||\n                 data.localityInfo?.administrative?.[1]?.name ||\n                 value.city\n\n    const state = data.principalSubdivision ||\n                  data.principalSubdivisionCode ||\n                  data.localityInfo?.administrative?.[1]?.name ||\n                  value.state\n\n    // Enhanced pincode extraction with multiple fallbacks\n    const pincode = data.postcode ||\n                    data.postalCode ||\n                    data.localityInfo?.postcode ||\n                    data.localityInfo?.postalCode ||\n                    value.pincode\n\n    // Country detection from coordinates\n    const country = data.countryName ||\n                    data.country ||\n                    data.localityInfo?.administrative?.[0]?.name ||\n                    value.country\n\n    return {\n      address: address || value.address,\n      street: address || value.street || value.address, // Keep for backward compatibility\n      city: city || value.city,\n      state: state || value.state,\n      country: country || value.country,\n      pincode: pincode || value.pincode,\n      coordinates: { latitude: lat, longitude: lng }\n    }\n  }\n\n  const validatePincode = async (pincode: string) => {\n    if (pincode.length === 6) {\n      // Here you could integrate with a pincode API to get city/state\n      // For now, we'll just validate the format\n      const isValid = /^[0-9]{6}$/.test(pincode)\n      if (!isValid) {\n        toast.error('Invalid pincode format')\n      }\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <MapPin className=\"h-5 w-5\" />\n          Property Location\n        </CardTitle>\n        <CardDescription>\n          Enter the complete address details for the property\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Address Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"address\">Complete Address *</Label>\n          <Input\n            id=\"address\"\n            placeholder=\"Enter complete address with landmarks\"\n            value={value.address || ''}\n            onChange={(e) => handleInputChange('address', e.target.value)}\n            className={errors?.address ? 'border-red-500' : ''}\n          />\n          {errors?.address && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.address[0]}\n            </p>\n          )}\n        </div>\n\n        {/* City and State Row */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"city\">City *</Label>\n            <Input\n              id=\"city\"\n              placeholder=\"Enter city\"\n              value={value.city || ''}\n              onChange={(e) => handleInputChange('city', e.target.value)}\n              className={errors?.city ? 'border-red-500' : ''}\n            />\n            {errors?.city && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.city[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"state\">State *</Label>\n            <Input\n              id=\"state\"\n              placeholder=\"Enter state\"\n              value={value.state || ''}\n              onChange={(e) => handleInputChange('state', e.target.value)}\n              className={errors?.state ? 'border-red-500' : ''}\n            />\n            {errors?.state && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.state[0]}\n              </p>\n            )}\n          </div>\n\n          {/* Country Field */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"country\">Country *</Label>\n            <Input\n              id=\"country\"\n              placeholder=\"Enter country\"\n              value={value.country || ''}\n              onChange={(e) => handleInputChange('country', e.target.value)}\n              className={errors?.country ? 'border-red-500' : ''}\n            />\n            {errors?.country && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.country[0]}\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Pincode Field */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"pincode\">Pincode *</Label>\n          <Input\n            id=\"pincode\"\n            placeholder=\"Enter 6-digit pincode\"\n            value={value.pincode || ''}\n            onChange={(e) => {\n              handleInputChange('pincode', e.target.value)\n              if (e.target.value.length === 6) {\n                validatePincode(e.target.value)\n              }\n            }}\n            maxLength={6}\n            className={errors?.pincode ? 'border-red-500' : ''}\n          />\n          {errors?.pincode && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.pincode[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Location Detection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label>GPS Coordinates & Auto-Fill</Label>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={detectCurrentLocation}\n              disabled={isDetectingLocation}\n              className=\"flex items-center gap-2 bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700\"\n            >\n              {isDetectingLocation ? (\n                <Loader2 className=\"h-4 w-4 animate-spin\" />\n              ) : (\n                <Navigation className=\"h-4 w-4\" />\n              )}\n              {isDetectingLocation ? 'Auto-Filling...' : 'Auto-Detect & Fill'}\n            </Button>\n          </div>\n\n          {/* Info Banner */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-emerald-50 border border-blue-200 rounded-lg p-4\">\n            <div className=\"flex items-start gap-3\">\n              <div className=\"bg-blue-500 rounded-full p-1\">\n                <Navigation className=\"h-4 w-4 text-white\" />\n              </div>\n              <div>\n                <h4 className=\"font-medium text-blue-900 mb-1\">Smart Location Detection</h4>\n                <p className=\"text-sm text-blue-700\">\n                  Click \"Auto-Detect & Fill\" to automatically detect your current location and fill all address details including:\n                </p>\n                <ul className=\"text-xs text-blue-600 mt-2 space-y-1\">\n                  <li>• Complete address with landmarks</li>\n                  <li>• City and state information</li>\n                  <li>• Postal/PIN code</li>\n                  <li>• GPS coordinates (latitude & longitude)</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {value.coordinates && (\n            <div className=\"flex items-center gap-2\">\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lat: {value.coordinates.latitude.toFixed(6)}\n              </Badge>\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n                <CheckCircle className=\"h-3 w-3\" />\n                Lng: {value.coordinates.longitude.toFixed(6)}\n              </Badge>\n            </div>\n          )}\n\n          {locationError && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {locationError}\n            </p>\n          )}\n        </div>\n\n        {/* Manual Coordinates Input */}\n        {!value.coordinates && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"latitude\">Latitude (Optional)</Label>\n              <Input\n                id=\"latitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 28.6139\"\n                onChange={(e) => {\n                  const lat = parseFloat(e.target.value)\n                  if (!isNaN(lat)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: lat,\n                        longitude: value.coordinates?.longitude || 0\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"longitude\">Longitude (Optional)</Label>\n              <Input\n                id=\"longitude\"\n                type=\"number\"\n                step=\"any\"\n                placeholder=\"e.g., 77.2090\"\n                onChange={(e) => {\n                  const lng = parseFloat(e.target.value)\n                  if (!isNaN(lng)) {\n                    onChange({\n                      ...value,\n                      coordinates: {\n                        latitude: value.coordinates?.latitude || 0,\n                        longitude: lng\n                      }\n                    })\n                  }\n                }}\n              />\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAsCe,SAAS,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAuB;IACrF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,oBAAoB,CAAC,OAA2B;QACpD,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,uBAAuB;QACvB,iBAAiB;QAEjB,UAAU,WAAW,CAAC,kBAAkB,CACtC,OAAO;YACL,IAAI;gBACF,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,SAAS,MAAM;gBAE/C,2BAA2B;gBAC3B,SAAS;oBACP,GAAG,KAAK;oBACR,aAAa;wBAAE;wBAAU;oBAAU;gBACrC;gBAEA,4DAA4D;gBAC5D,MAAM,eAAe,UAAU;YAEjC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,uBAAuB;YACzB;QACF,GACA,CAAC;YACC,uBAAuB;YACvB,IAAI,eAAe;YAEnB,OAAQ,MAAM,IAAI;gBAChB,KAAK,MAAM,iBAAiB;oBAC1B,eAAe;oBACf;gBACF,KAAK,MAAM,oBAAoB;oBAC7B,eAAe;oBACf;gBACF,KAAK,MAAM,OAAO;oBAChB,eAAe;oBACf;YACJ;YAEA,iBAAiB;YACjB,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,GACA;YACE,oBAAoB;YACpB,SAAS;YACT,YAAY;QACd;IAEJ;IAEA,6DAA6D;IAC7D,MAAM,iBAAiB,OAAO,KAAa;QACzC,IAAI;YACF,wQAAA,CAAA,QAAK,CAAC,IAAI,CAAC,8BAA8B;gBAAE,UAAU;YAAK;YAE1D,wDAAwD;YACxD,MAAM,oBAAoB;gBACxB,oDAAoD;gBACpD,CAAC,kEAAkE,EAAE,IAAI,WAAW,EAAE,IAAI,oBAAoB,CAAC;aAGhH;YAED,IAAI,cAAc;YAElB,KAAK,MAAM,cAAc,kBAAmB;gBAC1C,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM;oBAC7B,IAAI,SAAS,EAAE,EAAE;wBACf,cAAc,MAAM,SAAS,IAAI;wBACjC;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,6BAA6B;oBAC1C;gBACF;YACF;YAEA,IAAI,aAAa;gBACf,wDAAwD;gBACxD,MAAM,mBAAmB,yBAAyB,aAAa,KAAK;gBAEpE,gDAAgD;gBAChD,SAAS;gBAET,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,oCAAoC;gBACpC,SAAS;oBACP,GAAG,KAAK;oBACR,aAAa;wBAAE,UAAU;wBAAK,WAAW;oBAAI;gBAC/C;gBACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,kCAAkC;YAClC,SAAS;gBACP,GAAG,KAAK;gBACR,aAAa;oBAAE,UAAU;oBAAK,WAAW;gBAAI;YAC/C;YACA,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,gEAAgE;IAChE,MAAM,2BAA2B,CAAC,MAAW,KAAa;QACxD,sCAAsC;QACtC,MAAM,UAAU;YACd,KAAK,QAAQ;YACb,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;YACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;YACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE;SACzC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,SACvB;YAAC,KAAK,IAAI;YAAE,KAAK,oBAAoB;SAAC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,SAC5D,KAAK,WAAW,IAChB,MAAM,OAAO;QAEb,MAAM,OAAO,KAAK,IAAI,IACT,KAAK,QAAQ,IACb,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,IAAI;QAEvB,MAAM,QAAQ,KAAK,oBAAoB,IACzB,KAAK,wBAAwB,IAC7B,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,KAAK;QAEzB,sDAAsD;QACtD,MAAM,UAAU,KAAK,QAAQ,IACb,KAAK,UAAU,IACf,KAAK,YAAY,EAAE,YACnB,KAAK,YAAY,EAAE,cACnB,MAAM,OAAO;QAE7B,qCAAqC;QACrC,MAAM,UAAU,KAAK,WAAW,IAChB,KAAK,OAAO,IACZ,KAAK,YAAY,EAAE,gBAAgB,CAAC,EAAE,EAAE,QACxC,MAAM,OAAO;QAE7B,OAAO;YACL,SAAS,WAAW,MAAM,OAAO;YACjC,QAAQ,WAAW,MAAM,MAAM,IAAI,MAAM,OAAO;YAChD,MAAM,QAAQ,MAAM,IAAI;YACxB,OAAO,SAAS,MAAM,KAAK;YAC3B,SAAS,WAAW,MAAM,OAAO;YACjC,SAAS,WAAW,MAAM,OAAO;YACjC,aAAa;gBAAE,UAAU;gBAAK,WAAW;YAAI;QAC/C;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,gEAAgE;YAChE,0CAA0C;YAC1C,MAAM,UAAU,aAAa,IAAI,CAAC;YAClC,IAAI,CAAC,SAAS;gBACZ,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;IACF;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,8RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGhC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO,IAAI;gCACxB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC5D,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,IAAI,IAAI;wCACrB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAW,QAAQ,OAAO,mBAAmB;;;;;;oCAE9C,QAAQ,sBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;0CAKrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,KAAK,IAAI;wCACtB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,QAAQ,QAAQ,mBAAmB;;;;;;oCAE/C,QAAQ,uBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;0CAMtB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;kDACzB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACZ,OAAO,MAAM,OAAO,IAAI;wCACxB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAW,QAAQ,UAAU,mBAAmB;;;;;;oCAEjD,QAAQ,yBACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;;;;;;;kCAO1B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;0CACzB,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,OAAO,IAAI;gCACxB,UAAU,CAAC;oCACT,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC3C,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;wCAC/B,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAChC;gCACF;gCACA,WAAW;gCACX,WAAW,QAAQ,UAAU,mBAAmB;;;;;;4BAEjD,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,CAAC,EAAE;;;;;;;;;;;;;kCAMxB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6WAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,oCACC,6WAAC,qSAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAEvB,sBAAsB,oBAAoB;;;;;;;;;;;;;0CAK/C,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6WAAC;oDAAE,WAAU;8DAAwB;;;;;;8DAGrC,6WAAC;oDAAG,WAAU;;sEACZ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;sEACJ,6WAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAMX,MAAM,WAAW,kBAChB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;;;;;;;kDAE3C,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6WAAC,+SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;4CAC7B,MAAM,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;4BAK/C,+BACC,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB;;;;;;;;;;;;;oBAMN,CAAC,MAAM,WAAW,kBACjB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAW;;;;;;kDAC1B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU;wDACV,WAAW,MAAM,WAAW,EAAE,aAAa;oDAC7C;gDACF;4CACF;wCACF;;;;;;;;;;;;0CAIJ,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAY;;;;;;kDAC3B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,UAAU,CAAC;4CACT,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4CACrC,IAAI,CAAC,MAAM,MAAM;gDACf,SAAS;oDACP,GAAG,KAAK;oDACR,aAAa;wDACX,UAAU,MAAM,WAAW,EAAE,YAAY;wDACzC,WAAW;oDACb;gDACF;4CACF;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/FinancialDetails.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { DollarSign, TrendingUp, Calculator, AlertCircle, Info, Users, Percent, Zap, Check } from 'lucide-react'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\n// Financial Templates\nconst FINANCIAL_TEMPLATES = [\n  {\n    id: 'residential-basic',\n    name: 'Residential Basic',\n    description: 'Standard residential property investment',\n    expectedReturns: 12,\n    maturityPeriodMonths: 36,\n    totalStocks: 1000,\n    pricePerStock: 1000,\n    referralCommissionRate: 2,\n    salesCommissionRate: 1\n  },\n  {\n    id: 'commercial-premium',\n    name: 'Commercial Premium',\n    description: 'High-value commercial property',\n    expectedReturns: 15,\n    maturityPeriodMonths: 48,\n    totalStocks: 500,\n    pricePerStock: 5000,\n    referralCommissionRate: 3,\n    salesCommissionRate: 1.5\n  },\n  {\n    id: 'luxury-villa',\n    name: 'Luxury Villa',\n    description: 'Premium luxury property investment',\n    expectedReturns: 18,\n    maturityPeriodMonths: 60,\n    totalStocks: 200,\n    pricePerStock: 10000,\n    referralCommissionRate: 4,\n    salesCommissionRate: 2\n  },\n  {\n    id: 'budget-apartment',\n    name: 'Budget Apartment',\n    description: 'Affordable housing investment',\n    expectedReturns: 10,\n    maturityPeriodMonths: 24,\n    totalStocks: 2000,\n    pricePerStock: 500,\n    referralCommissionRate: 1.5,\n    salesCommissionRate: 0.8\n  }\n]\n\ninterface FinancialData {\n  expectedReturns: number\n  maturityPeriodMonths: number\n  totalStocks: number\n  pricePerStock: number\n  availableStocks: number\n  stockPrefix: string\n  stockStartNumber: number\n  referralCommissionRate: number\n  salesCommissionRate: number\n  referralCommissionPerStock: number\n  salesCommissionPerStock: number\n  commissionType: 'percentage' | 'fixed'\n}\n\ninterface FinancialDetailsProps {\n  value: FinancialData\n  onChange: (financial: FinancialData) => void\n  errors?: {\n    expectedReturns?: string[]\n    maturityPeriodMonths?: string[]\n    totalStocks?: string[]\n    pricePerStock?: string[]\n    availableStocks?: string[]\n    stockPrefix?: string[]\n    stockStartNumber?: string[]\n    referralCommissionRate?: string[]\n    salesCommissionRate?: string[]\n    referralCommissionPerStock?: string[]\n    salesCommissionPerStock?: string[]\n    commissionType?: string[]\n  }\n}\n\nexport default function FinancialDetails({ value, onChange, errors }: FinancialDetailsProps) {\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('')\n\n  const handleInputChange = (field: keyof FinancialData, inputValue: string | number) => {\n    if (field === 'stockPrefix' || field === 'commissionType') {\n      onChange({\n        ...value,\n        [field]: inputValue\n      })\n    } else {\n      const numericValue = typeof inputValue === 'string' ? parseFloat(inputValue) || 0 : inputValue\n      onChange({\n        ...value,\n        [field]: numericValue\n      })\n    }\n  }\n\n  const applyTemplate = (templateId: string) => {\n    const template = FINANCIAL_TEMPLATES.find(t => t.id === templateId)\n    if (template) {\n      onChange({\n        ...value,\n        expectedReturns: template.expectedReturns,\n        maturityPeriodMonths: template.maturityPeriodMonths,\n        totalStocks: template.totalStocks,\n        pricePerStock: template.pricePerStock,\n        availableStocks: template.totalStocks,\n        referralCommissionRate: template.referralCommissionRate,\n        salesCommissionRate: template.salesCommissionRate,\n        commissionType: 'percentage',\n        stockPrefix: 'STK',\n        stockStartNumber: 1,\n        referralCommissionPerStock: Math.round(template.pricePerStock * template.referralCommissionRate / 100),\n        salesCommissionPerStock: Math.round(template.pricePerStock * template.salesCommissionRate / 100)\n      })\n      setSelectedTemplate(templateId)\n    }\n  }\n\n  // Calculate derived values\n  const totalPropertyValue = value.totalStocks * value.pricePerStock\n  const maturityYears = value.maturityPeriodMonths / 12\n\n  // Calculate commission amounts (percentage-based)\n  const totalReferralCommission = (value.referralCommissionRate / 100) * totalPropertyValue\n  const totalSalesCommission = (value.salesCommissionRate / 100) * totalPropertyValue\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <DollarSign className=\"h-5 w-5\" />\n          Financial Details\n        </CardTitle>\n        <CardDescription>\n          Configure investment parameters and expected returns\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Template Selection */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center gap-2\">\n            <Zap className=\"h-4 w-4 text-blue-500\" />\n            <h4 className=\"font-medium\">Quick Templates</h4>\n            <Badge variant=\"secondary\" className=\"text-xs\">Easy Setup</Badge>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\">\n            {FINANCIAL_TEMPLATES.map((template) => (\n              <Button\n                key={template.id}\n                variant={selectedTemplate === template.id ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={() => applyTemplate(template.id)}\n                className=\"h-auto p-3 flex flex-col items-start gap-1\"\n              >\n                <div className=\"font-medium text-sm\">{template.name}</div>\n                <div className=\"text-xs text-muted-foreground text-left\">\n                  {template.description}\n                </div>\n                <div className=\"text-xs font-mono\">\n                  {template.expectedReturns}% • {template.maturityPeriodMonths}m • ₹{template.pricePerStock}\n                </div>\n              </Button>\n            ))}\n          </div>\n\n          {selectedTemplate && (\n            <div className=\"flex items-center gap-2 text-sm text-green-600\">\n              <Check className=\"h-4 w-4\" />\n              Template applied! You can edit the values below.\n            </div>\n          )}\n        </div>\n\n        {/* Returns and Maturity */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"expectedReturns\">Expected Returns (% p.a.) *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Annual percentage return expected from this investment</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"expectedReturns\"\n                type=\"number\"\n                step=\"0.1\"\n                min=\"0\"\n                max=\"100\"\n                placeholder=\"Annual return percentage\"\n                value={value.expectedReturns || ''}\n                onChange={(e) => handleInputChange('expectedReturns', e.target.value)}\n                className={errors?.expectedReturns ? 'border-red-500' : ''}\n              />\n              <TrendingUp className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n            </div>\n            {errors?.expectedReturns && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.expectedReturns[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"maturityPeriodMonths\">Maturity Period (Months) *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Duration of the investment in months</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <Input\n              id=\"maturityPeriodMonths\"\n              type=\"number\"\n              min=\"1\"\n              max=\"600\"\n              placeholder=\"Investment period in months\"\n              value={value.maturityPeriodMonths || ''}\n              onChange={(e) => handleInputChange('maturityPeriodMonths', e.target.value)}\n              className={errors?.maturityPeriodMonths ? 'border-red-500' : ''}\n            />\n            {errors?.maturityPeriodMonths && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.maturityPeriodMonths[0]}\n              </p>\n            )}\n            {value.maturityPeriodMonths > 0 && (\n              <p className=\"text-sm text-muted-foreground\">\n                ≈ {maturityYears.toFixed(1)} years\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Stock Configuration */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium flex items-center gap-2\">\n            <Calculator className=\"h-4 w-4\" />\n            Stock Configuration\n          </h4>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"totalStocks\">Total Stocks *</Label>\n              <Input\n                id=\"totalStocks\"\n                type=\"number\"\n                min=\"1\"\n                placeholder=\"Total number of stocks\"\n                value={value.totalStocks || ''}\n                onChange={(e) => handleInputChange('totalStocks', e.target.value)}\n                className={errors?.totalStocks ? 'border-red-500' : ''}\n              />\n              {errors?.totalStocks && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.totalStocks[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"pricePerStock\">Price per Stock (₹) *</Label>\n              <Input\n                id=\"pricePerStock\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0.01\"\n                placeholder=\"Price per individual stock\"\n                value={value.pricePerStock || ''}\n                onChange={(e) => handleInputChange('pricePerStock', e.target.value)}\n                className={errors?.pricePerStock ? 'border-red-500' : ''}\n              />\n              {errors?.pricePerStock && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.pricePerStock[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"availableStocks\">Available Stocks *</Label>\n              <Input\n                id=\"availableStocks\"\n                type=\"number\"\n                min=\"0\"\n                max={value.totalStocks}\n                placeholder=\"Available stocks for sale\"\n                value={value.availableStocks || ''}\n                onChange={(e) => handleInputChange('availableStocks', e.target.value)}\n                className={errors?.availableStocks ? 'border-red-500' : ''}\n              />\n              {errors?.availableStocks && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.availableStocks[0]}\n                </p>\n              )}\n              {value.availableStocks > value.totalStocks && (\n                <p className=\"text-sm text-amber-600\">\n                  Available stocks cannot exceed total stocks\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Stock Preview */}\n          {value.totalStocks > 0 && (\n            <div className=\"p-3 bg-muted/50 rounded-lg\">\n              <div className=\"text-sm text-muted-foreground\">Stock Preview:</div>\n              <div className=\"font-medium\">\n                STK-0001 to STK-{String(value.totalStocks).padStart(4, '0')} ({value.totalStocks} stocks)\n              </div>\n            </div>\n          )}\n        </div>\n\n\n\n        {/* Commission Configuration */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Commission Rates (%)\n          </h4>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"referralCommissionRate\">Referral Commission (%)</Label>\n              <div className=\"relative\">\n                <Input\n                  id=\"referralCommissionRate\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  max=\"100\"\n                  placeholder=\"Referral commission rate\"\n                  value={value.referralCommissionRate || ''}\n                  onChange={(e) => handleInputChange('referralCommissionRate', e.target.value)}\n                  className={errors?.referralCommissionRate ? 'border-red-500' : ''}\n                />\n                <Percent className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n              </div>\n              {errors?.referralCommissionRate && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.referralCommissionRate[0]}\n                </p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"salesCommissionRate\">Sales Commission (%)</Label>\n              <div className=\"relative\">\n                <Input\n                  id=\"salesCommissionRate\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  min=\"0\"\n                  max=\"100\"\n                  placeholder=\"Sales commission rate\"\n                  value={value.salesCommissionRate || ''}\n                  onChange={(e) => handleInputChange('salesCommissionRate', e.target.value)}\n                  className={errors?.salesCommissionRate ? 'border-red-500' : ''}\n                />\n                <Percent className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground\" />\n              </div>\n              {errors?.salesCommissionRate && (\n                <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  {errors.salesCommissionRate[0]}\n                </p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Calculated Values */}\n        {(value.totalStocks > 0 && value.pricePerStock > 0) && (\n          <div className=\"space-y-4 p-4 bg-muted/50 rounded-lg\">\n            <h4 className=\"font-medium text-sm\">Calculated Values</h4>\n\n            {/* Property Values */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Total Value\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  ₹{totalPropertyValue.toLocaleString('en-IN')}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Price per Stock\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  ₹{value.pricePerStock.toLocaleString('en-IN')}\n                </p>\n              </div>\n\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Sold Percentage\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {value.totalStocks > 0 ? (((value.totalStocks - value.availableStocks) / value.totalStocks) * 100).toFixed(1) : 0}%\n                </p>\n              </div>\n            </div>\n\n            {/* Commission Values */}\n            {(value.referralCommissionRate > 0 || value.salesCommissionRate > 0) && (\n              <div>\n                <h5 className=\"font-medium text-sm mb-2\">Commission Breakdown</h5>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Referral Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{totalReferralCommission.toLocaleString('en-IN')}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {value.referralCommissionRate}% of total\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Sales Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{totalSalesCommission.toLocaleString('en-IN')}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {value.salesCommissionRate}% of total\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Total Commission\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{(totalReferralCommission + totalSalesCommission).toLocaleString('en-IN')}\n                    </p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <Badge variant=\"outline\" className=\"w-full justify-center\">\n                      Net Property Value\n                    </Badge>\n                    <p className=\"text-sm font-medium mt-1\">\n                      ₹{(totalPropertyValue - totalReferralCommission - totalSalesCommission).toLocaleString('en-IN')}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAWA,sBAAsB;AACtB,MAAM,sBAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,wBAAwB;QACxB,qBAAqB;IACvB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,wBAAwB;QACxB,qBAAqB;IACvB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,wBAAwB;QACxB,qBAAqB;IACvB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,sBAAsB;QACtB,aAAa;QACb,eAAe;QACf,wBAAwB;QACxB,qBAAqB;IACvB;CACD;AAoCc,SAAS,iBAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAyB;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,oBAAoB,CAAC,OAA4B;QACrD,IAAI,UAAU,iBAAiB,UAAU,kBAAkB;YACzD,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,MAAM,EAAE;YACX;QACF,OAAO;YACL,MAAM,eAAe,OAAO,eAAe,WAAW,WAAW,eAAe,IAAI;YACpF,SAAS;gBACP,GAAG,KAAK;gBACR,CAAC,MAAM,EAAE;YACX;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,WAAW,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxD,IAAI,UAAU;YACZ,SAAS;gBACP,GAAG,KAAK;gBACR,iBAAiB,SAAS,eAAe;gBACzC,sBAAsB,SAAS,oBAAoB;gBACnD,aAAa,SAAS,WAAW;gBACjC,eAAe,SAAS,aAAa;gBACrC,iBAAiB,SAAS,WAAW;gBACrC,wBAAwB,SAAS,sBAAsB;gBACvD,qBAAqB,SAAS,mBAAmB;gBACjD,gBAAgB;gBAChB,aAAa;gBACb,kBAAkB;gBAClB,4BAA4B,KAAK,KAAK,CAAC,SAAS,aAAa,GAAG,SAAS,sBAAsB,GAAG;gBAClG,yBAAyB,KAAK,KAAK,CAAC,SAAS,aAAa,GAAG,SAAS,mBAAmB,GAAG;YAC9F;YACA,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB,MAAM,WAAW,GAAG,MAAM,aAAa;IAClE,MAAM,gBAAgB,MAAM,oBAAoB,GAAG;IAEnD,kDAAkD;IAClD,MAAM,0BAA0B,AAAC,MAAM,sBAAsB,GAAG,MAAO;IACvE,MAAM,uBAAuB,AAAC,MAAM,mBAAmB,GAAG,MAAO;IAEjE,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,sSAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGpC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6WAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAGjD,6WAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAC,yBACxB,6WAAC,kIAAA,CAAA,SAAM;wCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;wCACxD,MAAK;wCACL,SAAS,IAAM,cAAc,SAAS,EAAE;wCACxC,WAAU;;0DAEV,6WAAC;gDAAI,WAAU;0DAAuB,SAAS,IAAI;;;;;;0DACnD,6WAAC;gDAAI,WAAU;0DACZ,SAAS,WAAW;;;;;;0DAEvB,6WAAC;gDAAI,WAAU;;oDACZ,SAAS,eAAe;oDAAC;oDAAK,SAAS,oBAAoB;oDAAC;oDAAM,SAAS,aAAa;;;;;;;;uCAXtF,SAAS,EAAE;;;;;;;;;;4BAiBrB,kCACC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAOnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,eAAe,IAAI;gDAChC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAW,QAAQ,kBAAkB,mBAAmB;;;;;;0DAE1D,6WAAC,sSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;oCAEvB,QAAQ,iCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,eAAe,CAAC,EAAE;;;;;;;;;;;;;0CAKhC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAuB;;;;;;0DACtC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,aAAY;wCACZ,OAAO,MAAM,oBAAoB,IAAI;wCACrC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wCACzE,WAAW,QAAQ,uBAAuB,mBAAmB;;;;;;oCAE9D,QAAQ,sCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,oBAAoB,CAAC,EAAE;;;;;;;oCAGlC,MAAM,oBAAoB,GAAG,mBAC5B,6WAAC;wCAAE,WAAU;;4CAAgC;4CACxC,cAAc,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;kCAOpC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,kSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAIpC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,WAAW,IAAI;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gDAChE,WAAW,QAAQ,cAAc,mBAAmB;;;;;;4CAErD,QAAQ,6BACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,WAAW,CAAC,EAAE;;;;;;;;;;;;;kDAK5B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACJ,aAAY;gDACZ,OAAO,MAAM,aAAa,IAAI;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,WAAW,QAAQ,gBAAgB,mBAAmB;;;;;;4CAEvD,QAAQ,+BACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,aAAa,CAAC,EAAE;;;;;;;;;;;;;kDAK9B,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,KAAK,MAAM,WAAW;gDACtB,aAAY;gDACZ,OAAO,MAAM,eAAe,IAAI;gDAChC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAW,QAAQ,kBAAkB,mBAAmB;;;;;;4CAEzD,QAAQ,iCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,eAAe,CAAC,EAAE;;;;;;;4CAG7B,MAAM,eAAe,GAAG,MAAM,WAAW,kBACxC,6WAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;4BAQ3C,MAAM,WAAW,GAAG,mBACnB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;kDAAgC;;;;;;kDAC/C,6WAAC;wCAAI,WAAU;;4CAAc;4CACV,OAAO,MAAM,WAAW,EAAE,QAAQ,CAAC,GAAG;4CAAK;4CAAG,MAAM,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;kCASzF,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAyB;;;;;;0DACxC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,aAAY;wDACZ,OAAO,MAAM,sBAAsB,IAAI;wDACvC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;wDAC3E,WAAW,QAAQ,yBAAyB,mBAAmB;;;;;;kEAEjE,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;4CAEpB,QAAQ,wCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,sBAAsB,CAAC,EAAE;;;;;;;;;;;;;kDAKvC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAsB;;;;;;0DACrC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,aAAY;wDACZ,OAAO,MAAM,mBAAmB,IAAI;wDACpC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wDACxE,WAAW,QAAQ,sBAAsB,mBAAmB;;;;;;kEAE9D,6WAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;;4CAEpB,QAAQ,qCACP,6WAAC;gDAAE,WAAU;;kEACX,6WAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,OAAO,mBAAmB,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;oBAQtC,MAAM,WAAW,GAAG,KAAK,MAAM,aAAa,GAAG,mBAC/C,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAsB;;;;;;0CAGpC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDAA2B;oDACpC,mBAAmB,cAAc,CAAC;;;;;;;;;;;;;kDAIxC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDAA2B;oDACpC,MAAM,aAAa,CAAC,cAAc,CAAC;;;;;;;;;;;;;kDAIzC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDACV,MAAM,WAAW,GAAG,IAAI,CAAC,AAAC,CAAC,MAAM,WAAW,GAAG,MAAM,eAAe,IAAI,MAAM,WAAW,GAAI,GAAG,EAAE,OAAO,CAAC,KAAK;oDAAE;;;;;;;;;;;;;;;;;;;4BAMvH,CAAC,MAAM,sBAAsB,GAAG,KAAK,MAAM,mBAAmB,GAAG,CAAC,mBACjE,6WAAC;;kDACC,6WAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,wBAAwB,cAAc,CAAC;;;;;;;kEAE3C,6WAAC;wDAAE,WAAU;;4DACV,MAAM,sBAAsB;4DAAC;;;;;;;;;;;;;0DAIlC,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,qBAAqB,cAAc,CAAC;;;;;;;kEAExC,6WAAC;wDAAE,WAAU;;4DACV,MAAM,mBAAmB;4DAAC;;;;;;;;;;;;;0DAI/B,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,CAAC,0BAA0B,oBAAoB,EAAE,cAAc,CAAC;;;;;;;;;;;;;0DAItE,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAwB;;;;;;kEAG3D,6WAAC;wDAAE,WAAU;;4DAA2B;4DACpC,CAAC,qBAAqB,0BAA0B,oBAAoB,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7G", "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/ConstructionTimeline.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Calendar, Building, Clock, AlertCircle, CheckCircle, Info } from 'lucide-react'\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\nimport { ConstructionData } from '@/types/shared'\n\ninterface ConstructionTimelineProps {\n  value: ConstructionData\n  onChange: (construction: ConstructionData) => void\n  errors?: {\n    constructionStatus?: string[]\n    launchDate?: string[]\n    expectedCompletion?: string[]\n    actualCompletion?: string[]\n    constructionTimeline?: string[]\n  }\n}\n\nconst constructionStatuses = [\n  {\n    value: 'planning',\n    label: 'Planning Phase',\n    color: 'bg-blue-500',\n    description: 'Project planning and approvals',\n    icon: '📋'\n  },\n  {\n    value: 'foundation',\n    label: 'Foundation Work',\n    color: 'bg-orange-500',\n    description: 'Foundation and basement construction',\n    icon: '🏗️'\n  },\n  {\n    value: 'structure',\n    label: 'Structure Development',\n    color: 'bg-yellow-500',\n    description: 'Main structure and framework',\n    icon: '🏢'\n  },\n  {\n    value: 'finishing',\n    label: 'Finishing Work',\n    color: 'bg-purple-500',\n    description: 'Interior finishing and utilities',\n    icon: '🎨'\n  },\n  {\n    value: 'completed',\n    label: 'Project Completed',\n    color: 'bg-green-500',\n    description: 'Construction fully completed',\n    icon: '✅'\n  }\n]\n\nexport default function ConstructionTimeline({ value, onChange, errors }: ConstructionTimelineProps) {\n  const handleInputChange = (field: keyof ConstructionData, inputValue: string) => {\n    onChange({\n      ...value,\n      [field]: inputValue\n    })\n  }\n\n  // Calculate project duration\n  const calculateDuration = () => {\n    if (value.launchDate && value.expectedCompletion) {\n      const launch = new Date(value.launchDate)\n      const completion = new Date(value.expectedCompletion)\n      const diffTime = completion.getTime() - launch.getTime()\n      const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))\n      return diffMonths > 0 ? diffMonths : 0\n    }\n    return 0\n  }\n\n  // Check if dates are valid\n  const isValidDateRange = () => {\n    if (value.launchDate && value.expectedCompletion) {\n      return new Date(value.launchDate) < new Date(value.expectedCompletion)\n    }\n    return true\n  }\n\n  const currentStatus = constructionStatuses.find(status => status.value === value.constructionStatus)\n  const projectDuration = calculateDuration()\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Building className=\"h-5 w-5\" />\n          Construction Timeline\n        </CardTitle>\n        <CardDescription>\n          Configure construction phases and project timeline\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Construction Status */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center gap-2\">\n            <Label htmlFor=\"constructionStatus\">Construction Status *</Label>\n            <TooltipProvider>\n              <Tooltip>\n                <TooltipTrigger>\n                  <Info className=\"h-4 w-4 text-muted-foreground\" />\n                </TooltipTrigger>\n                <TooltipContent>\n                  <p>Current phase of construction</p>\n                </TooltipContent>\n              </Tooltip>\n            </TooltipProvider>\n          </div>\n\n          {/* Construction Status Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {constructionStatuses.map((status) => (\n              <div\n                key={status.value}\n                className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${\n                  value.constructionStatus === status.value\n                    ? 'border-blue-500 bg-blue-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => handleInputChange('constructionStatus', status.value)}\n              >\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"text-2xl\">{status.icon}</div>\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <div className={`w-3 h-3 rounded-full ${status.color}`} />\n                      <h4 className=\"font-medium text-sm\">{status.label}</h4>\n                    </div>\n                    <p className=\"text-xs text-gray-600\">{status.description}</p>\n                  </div>\n                </div>\n                {value.constructionStatus === status.value && (\n                  <div className=\"absolute top-2 right-2\">\n                    <CheckCircle className=\"h-4 w-4 text-blue-500\" />\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {errors?.constructionStatus && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.constructionStatus[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Project Dates */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"launchDate\">Launch Date *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Project launch or start date</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"launchDate\"\n                type=\"date\"\n                value={value.launchDate}\n                onChange={(e) => handleInputChange('launchDate', e.target.value)}\n                className={errors?.launchDate ? 'border-red-500' : ''}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.launchDate && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.launchDate[0]}\n              </p>\n            )}\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"expectedCompletion\">Expected Completion *</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Expected project completion date</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"expectedCompletion\"\n                type=\"date\"\n                value={value.expectedCompletion}\n                onChange={(e) => handleInputChange('expectedCompletion', e.target.value)}\n                className={errors?.expectedCompletion ? 'border-red-500' : ''}\n                min={value.launchDate}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.expectedCompletion && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.expectedCompletion[0]}\n              </p>\n            )}\n            {!isValidDateRange() && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                Completion date must be after launch date\n              </p>\n            )}\n          </div>\n        </div>\n\n        {/* Actual Completion (if completed) */}\n        {value.constructionStatus === 'completed' && (\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center gap-2\">\n              <Label htmlFor=\"actualCompletion\">Actual Completion Date</Label>\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger>\n                    <Info className=\"h-4 w-4 text-muted-foreground\" />\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Actual date when construction was completed</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n            <div className=\"relative\">\n              <Input\n                id=\"actualCompletion\"\n                type=\"date\"\n                value={value.actualCompletion || ''}\n                onChange={(e) => handleInputChange('actualCompletion', e.target.value)}\n                className={errors?.actualCompletion ? 'border-red-500' : ''}\n                max={new Date().toISOString().split('T')[0]}\n              />\n              <Calendar className=\"absolute right-3 top-3 h-4 w-4 text-muted-foreground pointer-events-none\" />\n            </div>\n            {errors?.actualCompletion && (\n              <p className=\"text-sm text-red-500 flex items-center gap-1\">\n                <AlertCircle className=\"h-4 w-4\" />\n                {errors.actualCompletion[0]}\n              </p>\n            )}\n          </div>\n        )}\n\n        {/* Construction Timeline Description */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"constructionTimeline\">Construction Timeline Description</Label>\n          <Input\n            id=\"constructionTimeline\"\n            placeholder=\"Brief description of construction phases and milestones\"\n            value={value.constructionTimeline || ''}\n            onChange={(e) => handleInputChange('constructionTimeline', e.target.value)}\n            className={errors?.constructionTimeline ? 'border-red-500' : ''}\n          />\n          {errors?.constructionTimeline && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.constructionTimeline[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Project Summary */}\n        {(value.launchDate && value.expectedCompletion && isValidDateRange()) && (\n          <div className=\"p-4 bg-muted/50 rounded-lg space-y-3\">\n            <h4 className=\"font-medium text-sm flex items-center gap-2\">\n              <Clock className=\"h-4 w-4\" />\n              Project Summary\n            </h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Duration\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {projectDuration} months\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Launch Date\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {new Date(value.launchDate).toLocaleDateString('en-IN')}\n                </p>\n              </div>\n              \n              <div className=\"text-center\">\n                <Badge variant=\"secondary\" className=\"w-full justify-center\">\n                  Expected Completion\n                </Badge>\n                <p className=\"text-sm font-medium mt-1\">\n                  {new Date(value.expectedCompletion).toLocaleDateString('en-IN')}\n                </p>\n              </div>\n            </div>\n            \n            {value.actualCompletion && (\n              <div className=\"text-center\">\n                <Badge variant=\"outline\" className=\"flex items-center gap-1 justify-center\">\n                  <CheckCircle className=\"h-3 w-3\" />\n                  Completed on {new Date(value.actualCompletion).toLocaleDateString('en-IN')}\n                </Badge>\n              </div>\n            )}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;AAwBA,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEc,SAAS,qBAAqB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAA6B;IACjG,MAAM,oBAAoB,CAAC,OAA+B;QACxD,SAAS;YACP,GAAG,KAAK;YACR,CAAC,MAAM,EAAE;QACX;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,IAAI,MAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE;YAChD,MAAM,SAAS,IAAI,KAAK,MAAM,UAAU;YACxC,MAAM,aAAa,IAAI,KAAK,MAAM,kBAAkB;YACpD,MAAM,WAAW,WAAW,OAAO,KAAK,OAAO,OAAO;YACtD,MAAM,aAAa,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,KAAK,EAAE;YACjE,OAAO,aAAa,IAAI,aAAa;QACvC;QACA,OAAO;IACT;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,IAAI,MAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE;YAChD,OAAO,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,KAAK,MAAM,kBAAkB;QACvE;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,qBAAqB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,MAAM,kBAAkB;IACnG,MAAM,kBAAkB;IAExB,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,8RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGlC,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAqB;;;;;;kDACpC,6WAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8DACN,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOX,6WAAC;gCAAI,WAAU;0CACZ,qBAAqB,GAAG,CAAC,CAAC,uBACzB,6WAAC;wCAEC,WAAW,CAAC,+EAA+E,EACzF,MAAM,kBAAkB,KAAK,OAAO,KAAK,GACrC,+BACA,yCACJ;wCACF,SAAS,IAAM,kBAAkB,sBAAsB,OAAO,KAAK;;0DAEnE,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;kEAAY,OAAO,IAAI;;;;;;kEACtC,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAI,WAAW,CAAC,qBAAqB,EAAE,OAAO,KAAK,EAAE;;;;;;kFACtD,6WAAC;wEAAG,WAAU;kFAAuB,OAAO,KAAK;;;;;;;;;;;;0EAEnD,6WAAC;gEAAE,WAAU;0EAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;4CAG3D,MAAM,kBAAkB,KAAK,OAAO,KAAK,kBACxC,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC,+SAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;uCApBtB,OAAO,KAAK;;;;;;;;;;4BA2BtB,QAAQ,oCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,kBAAkB,CAAC,EAAE;;;;;;;;;;;;;kCAMnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,MAAM,UAAU;gDACvB,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAW,QAAQ,aAAa,mBAAmB;;;;;;0DAErD,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;oCAErB,QAAQ,4BACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;;0CAK3B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAqB;;;;;;0DACpC,6WAAC,mIAAA,CAAA,kBAAe;0DACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;sEACN,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6WAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,6WAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKX,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,MAAM,kBAAkB;gDAC/B,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gDACvE,WAAW,QAAQ,qBAAqB,mBAAmB;gDAC3D,KAAK,MAAM,UAAU;;;;;;0DAEvB,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;oCAErB,QAAQ,oCACP,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,OAAO,kBAAkB,CAAC,EAAE;;;;;;;oCAGhC,CAAC,oCACA,6WAAC;wCAAE,WAAU;;0DACX,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;oBAQ1C,MAAM,kBAAkB,KAAK,6BAC5B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAmB;;;;;;kDAClC,6WAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,6WAAC,mIAAA,CAAA,UAAO;;8DACN,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6WAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,6WAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKX,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,MAAM,gBAAgB,IAAI;wCACjC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACrE,WAAW,QAAQ,mBAAmB,mBAAmB;wCACzD,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;kDAE7C,6WAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;4BAErB,QAAQ,kCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,gBAAgB,CAAC,EAAE;;;;;;;;;;;;;kCAOnC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAuB;;;;;;0CACtC,6WAAC,iIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,MAAM,oBAAoB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;gCACzE,WAAW,QAAQ,uBAAuB,mBAAmB;;;;;;4BAE9D,QAAQ,sCACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,oBAAoB,CAAC,EAAE;;;;;;;;;;;;;oBAMnC,MAAM,UAAU,IAAI,MAAM,kBAAkB,IAAI,oCAChD,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;;oDACV;oDAAgB;;;;;;;;;;;;;kDAIrB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;kDAInD,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAwB;;;;;;0DAG7D,6WAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,MAAM,kBAAkB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;4BAK5D,MAAM,gBAAgB,kBACrB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;sDACjC,6WAAC,+SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAY;wCACrB,IAAI,KAAK,MAAM,gBAAgB,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpF", "debugId": null}}, {"offset": {"line": 2811, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/OwnerDeveloperSelector.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar'\nimport {\n  User,\n  Building,\n  Search,\n  Filter,\n  AlertCircle,\n  Briefcase,\n  Plus,\n  ExternalLink\n} from 'lucide-react'\nimport { useGetPropertyOwnersQuery } from '@/store/api/propertyOwnersApi'\n\ninterface OwnerDeveloperData {\n  ownerId?: string\n  developerId?: string\n}\n\ninterface OwnerDeveloperSelectorProps {\n  value: OwnerDeveloperData\n  onChange: (data: OwnerDeveloperData) => void\n  errors?: {\n    ownerId?: string[]\n    developerId?: string[]\n  }\n}\n\ninterface PropertyOwner {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  company?: string\n  roles?: {\n    isOwner: boolean\n    isDeveloper: boolean\n  }\n  isOwner?: boolean\n  isDeveloper?: boolean\n  address?: {\n    city: string\n    state: string\n  }\n  verificationStatus?: string\n  developerDetails?: {\n    experience: number\n    completedProjects: number\n    specialization: string[]\n  }\n}\n\nexport default function OwnerDeveloperSelector({ value, onChange, errors }: OwnerDeveloperSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [roleFilter, setRoleFilter] = useState<'all' | 'owner' | 'developer'>('all')\n  const [selectedOwner, setSelectedOwner] = useState<PropertyOwner | null>(null)\n  const [selectedDeveloper, setSelectedDeveloper] = useState<PropertyOwner | null>(null)\n\n  // Fetch property owners/developers\n  const { data: ownersData, isLoading, error } = useGetPropertyOwnersQuery({\n    page: 1,\n    limit: 100,\n    search: searchTerm\n  })\n\n  const owners = ownersData?.data?.data || []\n\n  // Filter owners based on role and search\n  const filteredOwners = owners.filter((owner: PropertyOwner) => {\n    const matchesSearch = searchTerm === '' ||\n      `${owner.firstName} ${owner.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      owner.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      owner.company?.toLowerCase().includes(searchTerm.toLowerCase())\n\n    const isOwner = owner.roles?.isOwner || owner.isOwner\n    const isDeveloper = owner.roles?.isDeveloper || owner.isDeveloper\n\n    const matchesRole = roleFilter === 'all' ||\n      (roleFilter === 'owner' && isOwner) ||\n      (roleFilter === 'developer' && isDeveloper)\n\n    return matchesSearch && matchesRole\n  })\n\n  // Get owners only\n  const propertyOwners = filteredOwners.filter((owner: PropertyOwner) => owner.roles?.isOwner || owner.isOwner)\n\n  // Get developers only\n  const developers = filteredOwners.filter((owner: PropertyOwner) => owner.roles?.isDeveloper || owner.isDeveloper)\n\n  // Update selected owner/developer when IDs change\n  useEffect(() => {\n    if (value.ownerId) {\n      const owner = owners.find((o: PropertyOwner) => o._id === value.ownerId)\n      setSelectedOwner(owner || null)\n    }\n    if (value.developerId) {\n      const developer = owners.find((o: PropertyOwner) => o._id === value.developerId)\n      setSelectedDeveloper(developer || null)\n    }\n  }, [value.ownerId, value.developerId, owners])\n\n  const handleOwnerSelect = (ownerId: string) => {\n    onChange({\n      ...value,\n      ownerId: ownerId\n    })\n  }\n\n  const handleDeveloperSelect = (developerId: string) => {\n    onChange({\n      ...value,\n      developerId: developerId\n    })\n  }\n\n  const getInitials = (firstName: string, lastName: string) => {\n    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()\n  }\n\n  const getRoleBadges = (owner: PropertyOwner) => {\n    const roles = []\n    const isOwner = owner.roles?.isOwner || owner.isOwner\n    const isDeveloper = owner.roles?.isDeveloper || owner.isDeveloper\n\n    if (isOwner) roles.push({ label: 'Owner', icon: Building, color: 'bg-blue-500' })\n    if (isDeveloper) roles.push({ label: 'Developer', icon: Briefcase, color: 'bg-green-500' })\n\n    return roles.map((role, index) => (\n      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n        <role.icon className=\"h-3 w-3 mr-1\" />\n        {role.label}\n      </Badge>\n    ))\n  }\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex items-center justify-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            <span className=\"ml-2\">Loading owners and developers...</span>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"text-center text-red-500\">\n            <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n            <p>Failed to load owners and developers</p>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <User className=\"h-5 w-5\" />\n          Property Owner & Developer\n          <Badge variant=\"secondary\" className=\"text-xs bg-green-100 text-green-800\">Optional</Badge>\n        </CardTitle>\n        <CardDescription>\n          Select either a property owner or developer (or both) for this property. You can skip this step if not available.\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Search and Filter */}\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-3 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search by name, email, or company...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"h-4 w-4 text-muted-foreground\" />\n            <Select value={roleFilter} onValueChange={(value: any) => setRoleFilter(value)}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All Roles</SelectItem>\n                <SelectItem value=\"owner\">Owners Only</SelectItem>\n                <SelectItem value=\"developer\">Developers Only</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n\n        {/* Property Owner Selection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label className=\"text-base font-medium\">Property Owner (Optional)</Label>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open('/properties/owners/add', '_blank')}\n              className=\"text-xs\"\n            >\n              <Plus className=\"h-3 w-3 mr-1\" />\n              Add New Owner\n              <ExternalLink className=\"h-3 w-3 ml-1\" />\n            </Button>\n          </div>\n\n          {propertyOwners.length === 0 && !isLoading && (\n            <div className=\"p-4 border border-dashed border-gray-300 rounded-lg text-center\">\n              <User className=\"h-8 w-8 text-gray-400 mx-auto mb-2\" />\n              <p className=\"text-sm text-gray-500 mb-2\">No property owners found</p>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => window.open('/properties/owners/add', '_blank')}\n                className=\"text-xs\"\n              >\n                <Plus className=\"h-3 w-3 mr-1\" />\n                Create First Owner\n              </Button>\n            </div>\n          )}\n          \n          {selectedOwner ? (\n            <div className=\"p-4 border rounded-lg bg-blue-50 border-blue-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Avatar>\n                    <AvatarFallback className=\"bg-blue-500 text-white\">\n                      {getInitials(selectedOwner.firstName, selectedOwner.lastName)}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <h4 className=\"font-medium\">{selectedOwner.firstName} {selectedOwner.lastName}</h4>\n                    <p className=\"text-sm text-muted-foreground\">{selectedOwner.email}</p>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      {getRoleBadges(selectedOwner)}\n                    </div>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleOwnerSelect('')}\n                >\n                  Change\n                </Button>\n              </div>\n            </div>\n          ) : propertyOwners.length > 0 ? (\n            <Select value={value.ownerId || ''} onValueChange={handleOwnerSelect}>\n              <SelectTrigger className={errors?.ownerId ? 'border-red-500' : ''}>\n                <SelectValue placeholder=\"Select property owner\" />\n              </SelectTrigger>\n              <SelectContent>\n                {propertyOwners.map((owner: PropertyOwner) => (\n                  <SelectItem key={owner._id} value={owner._id}>\n                    <div className=\"flex items-center gap-2\">\n                      <Avatar className=\"h-6 w-6\">\n                        <AvatarFallback className=\"text-xs\">\n                          {getInitials(owner.firstName, owner.lastName)}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <div className=\"font-medium\">{owner.firstName} {owner.lastName}</div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {owner.company || owner.email}\n                          {owner.verificationStatus === 'verified' && (\n                            <span className=\"ml-2 text-green-600\">✓ Verified</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          ) : null}\n          \n          {errors?.ownerId && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.ownerId?.[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Developer Selection */}\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label className=\"text-base font-medium\">Developer Information *</Label>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => window.open('/properties/owners/add', '_blank')}\n              className=\"text-xs\"\n            >\n              <Plus className=\"h-3 w-3 mr-1\" />\n              Add New Developer\n              <ExternalLink className=\"h-3 w-3 ml-1\" />\n            </Button>\n          </div>\n\n          {developers.length === 0 && !isLoading && (\n            <div className=\"p-4 border border-dashed border-red-300 rounded-lg text-center bg-red-50\">\n              <Briefcase className=\"h-8 w-8 text-red-400 mx-auto mb-2\" />\n              <p className=\"text-sm text-red-600 mb-2\">No developers found</p>\n              <p className=\"text-xs text-red-500 mb-3\">A developer is required to create a property</p>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => window.open('/properties/owners/add', '_blank')}\n                className=\"text-xs border-red-300 text-red-600 hover:bg-red-50\"\n              >\n                <Plus className=\"h-3 w-3 mr-1\" />\n                Create Developer\n              </Button>\n            </div>\n          )}\n          \n          {selectedDeveloper ? (\n            <div className=\"p-4 border rounded-lg bg-green-50 border-green-200\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <Avatar>\n                    <AvatarFallback className=\"bg-green-500 text-white\">\n                      {getInitials(selectedDeveloper.firstName, selectedDeveloper.lastName)}\n                    </AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <h4 className=\"font-medium\">{selectedDeveloper.firstName} {selectedDeveloper.lastName}</h4>\n                    <p className=\"text-sm text-muted-foreground\">{selectedDeveloper.company || selectedDeveloper.email}</p>\n                    <div className=\"flex items-center gap-2 mt-1\">\n                      {getRoleBadges(selectedDeveloper)}\n                      {selectedDeveloper.developerDetails && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {selectedDeveloper.developerDetails.experience}+ years exp\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleDeveloperSelect('')}\n                >\n                  Change\n                </Button>\n              </div>\n            </div>\n          ) : developers.length > 0 ? (\n            <Select value={value.developerId || ''} onValueChange={handleDeveloperSelect}>\n              <SelectTrigger className={errors?.developerId ? 'border-red-500' : ''}>\n                <SelectValue placeholder=\"Select developer\" />\n              </SelectTrigger>\n              <SelectContent>\n                {developers.map((developer: PropertyOwner) => (\n                  <SelectItem key={developer._id} value={developer._id}>\n                    <div className=\"flex items-center gap-2\">\n                      <Avatar className=\"h-6 w-6\">\n                        <AvatarFallback className=\"text-xs\">\n                          {getInitials(developer.firstName, developer.lastName)}\n                        </AvatarFallback>\n                      </Avatar>\n                      <div>\n                        <div className=\"font-medium\">{developer.firstName} {developer.lastName}</div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {developer.company || developer.email}\n                          {developer.developerDetails && (\n                            <span className=\"ml-2\">• {developer.developerDetails.experience}+ years</span>\n                          )}\n                          {developer.verificationStatus === 'verified' && (\n                            <span className=\"ml-2 text-green-600\">✓ Verified</span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          ) : null}\n          \n          {errors?.developerId && (\n            <p className=\"text-sm text-red-500 flex items-center gap-1\">\n              <AlertCircle className=\"h-4 w-4\" />\n              {errors.developerId?.[0]}\n            </p>\n          )}\n        </div>\n\n        {/* Summary */}\n        {(selectedOwner || selectedDeveloper) && (\n          <div className=\"p-4 bg-muted/50 rounded-lg\">\n            <h4 className=\"font-medium text-sm mb-2\">Selection Summary</h4>\n            <div className=\"space-y-2 text-sm\">\n              {selectedOwner && (\n                <div className=\"flex items-center gap-2\">\n                  <Building className=\"h-4 w-4 text-blue-500\" />\n                  <span>Owner: {selectedOwner.firstName} {selectedOwner.lastName}</span>\n                </div>\n              )}\n              {selectedDeveloper && (\n                <div className=\"flex items-center gap-2\">\n                  <Briefcase className=\"h-4 w-4 text-green-500\" />\n                  <span>Developer: {selectedDeveloper.firstName} {selectedDeveloper.lastName}</span>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AApBA;;;;;;;;;;;;AA6De,SAAS,uBAAuB,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAA+B;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiC;IAC5E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAwB;IAEjF,mCAAmC;IACnC,MAAM,EAAE,MAAM,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;QACvE,MAAM;QACN,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,SAAS,YAAY,MAAM,QAAQ,EAAE;IAE3C,yCAAyC;IACzC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC;QACpC,MAAM,gBAAgB,eAAe,MACnC,GAAG,MAAM,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACpF,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,MAAM,OAAO,EAAE,cAAc,SAAS,WAAW,WAAW;QAE9D,MAAM,UAAU,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;QACrD,MAAM,cAAc,MAAM,KAAK,EAAE,eAAe,MAAM,WAAW;QAEjE,MAAM,cAAc,eAAe,SAChC,eAAe,WAAW,WAC1B,eAAe,eAAe;QAEjC,OAAO,iBAAiB;IAC1B;IAEA,kBAAkB;IAClB,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAC,QAAyB,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;IAE5G,sBAAsB;IACtB,MAAM,aAAa,eAAe,MAAM,CAAC,CAAC,QAAyB,MAAM,KAAK,EAAE,eAAe,MAAM,WAAW;IAEhH,kDAAkD;IAClD,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAC,IAAqB,EAAE,GAAG,KAAK,MAAM,OAAO;YACvE,iBAAiB,SAAS;QAC5B;QACA,IAAI,MAAM,WAAW,EAAE;YACrB,MAAM,YAAY,OAAO,IAAI,CAAC,CAAC,IAAqB,EAAE,GAAG,KAAK,MAAM,WAAW;YAC/E,qBAAqB,aAAa;QACpC;IACF,GAAG;QAAC,MAAM,OAAO;QAAE,MAAM,WAAW;QAAE;KAAO;IAE7C,MAAM,oBAAoB,CAAC;QACzB,SAAS;YACP,GAAG,KAAK;YACR,SAAS;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,SAAS;YACP,GAAG,KAAK;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC,WAAmB;QACtC,OAAO,GAAG,UAAU,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC,IAAI,CAAC,WAAW;IAClE;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,EAAE;QAChB,MAAM,UAAU,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;QACrD,MAAM,cAAc,MAAM,KAAK,EAAE,eAAe,MAAM,WAAW;QAEjE,IAAI,SAAS,MAAM,IAAI,CAAC;YAAE,OAAO;YAAS,MAAM,8RAAA,CAAA,WAAQ;YAAE,OAAO;QAAc;QAC/E,IAAI,aAAa,MAAM,IAAI,CAAC;YAAE,OAAO;YAAa,MAAM,gSAAA,CAAA,YAAS;YAAE,OAAO;QAAe;QAEzF,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtB,6WAAC,iIAAA,CAAA,QAAK;gBAAa,SAAQ;gBAAY,WAAU;;kCAC/C,6WAAC,KAAK,IAAI;wBAAC,WAAU;;;;;;oBACpB,KAAK,KAAK;;eAFD;;;;;IAKhB;IAEA,IAAI,WAAW;QACb,qBACE,6WAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;;;;;sCACf,6WAAC;4BAAK,WAAU;sCAAO;;;;;;;;;;;;;;;;;;;;;;IAKjC;IAEA,IAAI,OAAO;QACT,qBACE,6WAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC,wSAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6WAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;0CAE5B,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAsC;;;;;;;;;;;;kCAE7E,6WAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6WAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe,CAAC,QAAe,cAAc;;0DACtE,6WAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6WAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,6WAAC,kIAAA,CAAA,gBAAa;;kEACZ,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,6WAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAwB;;;;;;kDACzC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;0DAEjC,6WAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAI3B,eAAe,MAAM,KAAK,KAAK,CAAC,2BAC/B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6WAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAMtC,8BACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;8DACL,cAAA,6WAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,YAAY,cAAc,SAAS,EAAE,cAAc,QAAQ;;;;;;;;;;;8DAGhE,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;;gEAAe,cAAc,SAAS;gEAAC;gEAAE,cAAc,QAAQ;;;;;;;sEAC7E,6WAAC;4DAAE,WAAU;sEAAiC,cAAc,KAAK;;;;;;sEACjE,6WAAC;4DAAI,WAAU;sEACZ,cAAc;;;;;;;;;;;;;;;;;;sDAIrB,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,kBAAkB;sDAClC;;;;;;;;;;;;;;;;uCAKH,eAAe,MAAM,GAAG,kBAC1B,6WAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,MAAM,OAAO,IAAI;gCAAI,eAAe;;kDACjD,6WAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,QAAQ,UAAU,mBAAmB;kDAC7D,cAAA,6WAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6WAAC,kIAAA,CAAA,gBAAa;kDACX,eAAe,GAAG,CAAC,CAAC,sBACnB,6WAAC,kIAAA,CAAA,aAAU;gDAAiB,OAAO,MAAM,GAAG;0DAC1C,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6WAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,YAAY,MAAM,SAAS,EAAE,MAAM,QAAQ;;;;;;;;;;;sEAGhD,6WAAC;;8EACC,6WAAC;oEAAI,WAAU;;wEAAe,MAAM,SAAS;wEAAC;wEAAE,MAAM,QAAQ;;;;;;;8EAC9D,6WAAC;oEAAI,WAAU;;wEACZ,MAAM,OAAO,IAAI,MAAM,KAAK;wEAC5B,MAAM,kBAAkB,KAAK,4BAC5B,6WAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;;;;;;;+CAZ/B,MAAM,GAAG;;;;;;;;;;;;;;;uCAqB9B;4BAEH,QAAQ,yBACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,OAAO,EAAE,CAAC,EAAE;;;;;;;;;;;;;kCAM1B,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,iIAAA,CAAA,QAAK;wCAAC,WAAU;kDAAwB;;;;;;kDACzC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;0DAEjC,6WAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAI3B,WAAW,MAAM,KAAK,KAAK,CAAC,2BAC3B,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,gSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6WAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,6WAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,0BAA0B;wCACrD,WAAU;;0DAEV,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAMtC,kCACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,kIAAA,CAAA,SAAM;8DACL,cAAA,6WAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,YAAY,kBAAkB,SAAS,EAAE,kBAAkB,QAAQ;;;;;;;;;;;8DAGxE,6WAAC;;sEACC,6WAAC;4DAAG,WAAU;;gEAAe,kBAAkB,SAAS;gEAAC;gEAAE,kBAAkB,QAAQ;;;;;;;sEACrF,6WAAC;4DAAE,WAAU;sEAAiC,kBAAkB,OAAO,IAAI,kBAAkB,KAAK;;;;;;sEAClG,6WAAC;4DAAI,WAAU;;gEACZ,cAAc;gEACd,kBAAkB,gBAAgB,kBACjC,6WAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAChC,kBAAkB,gBAAgB,CAAC,UAAU;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;sDAMzD,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB;sDACtC;;;;;;;;;;;;;;;;uCAKH,WAAW,MAAM,GAAG,kBACtB,6WAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,MAAM,WAAW,IAAI;gCAAI,eAAe;;kDACrD,6WAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAW,QAAQ,cAAc,mBAAmB;kDACjE,cAAA,6WAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6WAAC,kIAAA,CAAA,gBAAa;kDACX,WAAW,GAAG,CAAC,CAAC,0BACf,6WAAC,kIAAA,CAAA,aAAU;gDAAqB,OAAO,UAAU,GAAG;0DAClD,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6WAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,YAAY,UAAU,SAAS,EAAE,UAAU,QAAQ;;;;;;;;;;;sEAGxD,6WAAC;;8EACC,6WAAC;oEAAI,WAAU;;wEAAe,UAAU,SAAS;wEAAC;wEAAE,UAAU,QAAQ;;;;;;;8EACtE,6WAAC;oEAAI,WAAU;;wEACZ,UAAU,OAAO,IAAI,UAAU,KAAK;wEACpC,UAAU,gBAAgB,kBACzB,6WAAC;4EAAK,WAAU;;gFAAO;gFAAG,UAAU,gBAAgB,CAAC,UAAU;gFAAC;;;;;;;wEAEjE,UAAU,kBAAkB,KAAK,4BAChC,6WAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;;;;;;;+CAf/B,UAAU,GAAG;;;;;;;;;;;;;;;uCAwBlC;4BAEH,QAAQ,6BACP,6WAAC;gCAAE,WAAU;;kDACX,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,OAAO,WAAW,EAAE,CAAC,EAAE;;;;;;;;;;;;;oBAM7B,CAAC,iBAAiB,iBAAiB,mBAClC,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6WAAC;gCAAI,WAAU;;oCACZ,+BACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6WAAC;;oDAAK;oDAAQ,cAAc,SAAS;oDAAC;oDAAE,cAAc,QAAQ;;;;;;;;;;;;;oCAGjE,mCACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,gSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6WAAC;;oDAAK;oDAAY,kBAAkB,SAAS;oDAAC;oDAAE,kBAAkB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5F", "debugId": null}}, {"offset": {"line": 3895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/EnhancedFeaturesAmenities.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useMemo } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Search, Plus, X, Filter, Check } from 'lucide-react'\n\ninterface EnhancedFeaturesAmenitiesProps {\n  features: string[]\n  amenities: string[]\n  onFeaturesChange: (features: string[]) => void\n  onAmenitiesChange: (amenities: string[]) => void\n  featuresList: string[]\n  amenitiesList: string[]\n}\n\nconst amenityCategories = {\n  basic: ['Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground', 'Club House', 'Power Backup', 'Elevator', 'CCTV', 'Intercom', 'Water Supply'],\n  premium: ['Spa & Wellness Center', 'Rooftop Terrace', 'Business Center', 'Conference Room', 'Library', 'Meditation Room', 'Yoga Studio', 'Indoor Games Room', 'Outdoor Sports Court', 'Jogging Track', 'Cycling Track', 'Pet Park'],\n  safety: ['24/7 Security', 'Gated Community', 'Fire Safety', 'Emergency Exit', 'Smoke Detectors', 'Sprinkler System', 'Security Cameras', 'Access Control'],\n  convenience: ['Concierge Service', 'Housekeeping', 'Laundry Service', 'Valet Parking', 'Car Wash', 'ATM', 'Shopping Center', 'Food Court'],\n  utilities: ['High Speed Internet', 'Cable TV', 'Intercom System', 'Video Door Phone', 'Centralized Gas', 'Solar Power', 'Rainwater Harvesting', 'Waste Management'],\n  outdoor: ['Landscaped Gardens', 'Water Features', 'BBQ Area', 'Outdoor Seating', 'Kids Play Area', 'Senior Citizen Area', 'Amphitheater', 'Gazebo']\n}\n\nconst featureCategories = {\n  structural: ['Modular Kitchen', 'Walk-in Closet', 'Master Bedroom', 'Guest Room', 'Study Room', 'Prayer Room', 'Servant Room', 'Store Room', 'Balcony', 'Terrace', 'Basement', 'Attic'],\n  interior: ['Wooden Flooring', 'Marble Flooring', 'Vitrified Tiles', 'False Ceiling', 'Designer Lighting', 'Built-in Wardrobes', 'Kitchen Cabinets', 'Granite Countertops', 'Modular Switches', 'Premium Fittings', 'Designer Bathroom', 'Jacuzzi'],\n  technology: ['Smart Home Automation', 'Video Door Bell', 'Smart Locks', 'Motion Sensors', 'Smart Lighting', 'Climate Control', 'Home Theater Setup', 'Sound System'],\n  energy: ['Solar Panels', 'Energy Efficient Windows', 'LED Lighting', 'Insulation', 'Double Glazed Windows', 'Ventilation System', 'Natural Lighting', 'Cross Ventilation'],\n  luxury: ['Private Pool', 'Private Garden', 'Private Elevator', 'Butler Service', 'Wine Cellar', 'Home Office', 'Guest House', 'Driver Room'],\n  accessibility: ['Wheelchair Accessible', 'Ramp Access', 'Wide Doorways', 'Accessible Bathroom', 'Emergency Call System', 'Braille Signage', 'Audio Visual Alerts', 'Grab Bars']\n}\n\nexport default function EnhancedFeaturesAmenities({\n  features,\n  amenities,\n  onFeaturesChange,\n  onAmenitiesChange,\n  featuresList,\n  amenitiesList\n}: EnhancedFeaturesAmenitiesProps) {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [newFeature, setNewFeature] = useState('')\n  const [newAmenity, setNewAmenity] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  // Filter functions\n  const filteredFeatures = useMemo(() => {\n    let filtered = featuresList\n    \n    if (selectedCategory !== 'all' && featureCategories[selectedCategory as keyof typeof featureCategories]) {\n      filtered = featureCategories[selectedCategory as keyof typeof featureCategories]\n    }\n    \n    if (searchTerm) {\n      filtered = filtered.filter(feature => \n        feature.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n    \n    return filtered\n  }, [featuresList, searchTerm, selectedCategory])\n\n  const filteredAmenities = useMemo(() => {\n    let filtered = amenitiesList\n    \n    if (selectedCategory !== 'all' && amenityCategories[selectedCategory as keyof typeof amenityCategories]) {\n      filtered = amenityCategories[selectedCategory as keyof typeof amenityCategories]\n    }\n    \n    if (searchTerm) {\n      filtered = filtered.filter(amenity => \n        amenity.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n    \n    return filtered\n  }, [amenitiesList, searchTerm, selectedCategory])\n\n  const handleFeatureToggle = (feature: string) => {\n    if (features.includes(feature)) {\n      onFeaturesChange(features.filter(f => f !== feature))\n    } else {\n      onFeaturesChange([...features, feature])\n    }\n  }\n\n  const handleAmenityToggle = (amenity: string) => {\n    if (amenities.includes(amenity)) {\n      onAmenitiesChange(amenities.filter(a => a !== amenity))\n    } else {\n      onAmenitiesChange([...amenities, amenity])\n    }\n  }\n\n  const addCustomFeature = () => {\n    if (newFeature.trim() && !features.includes(newFeature.trim())) {\n      onFeaturesChange([...features, newFeature.trim()])\n      setNewFeature('')\n    }\n  }\n\n  const addCustomAmenity = () => {\n    if (newAmenity.trim() && !amenities.includes(newAmenity.trim())) {\n      onAmenitiesChange([...amenities, newAmenity.trim()])\n      setNewAmenity('')\n    }\n  }\n\n  const removeFeature = (feature: string) => {\n    onFeaturesChange(features.filter(f => f !== feature))\n  }\n\n  const removeAmenity = (amenity: string) => {\n    onAmenitiesChange(amenities.filter(a => a !== amenity))\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Filter className=\"h-5 w-5\" />\n          Features & Amenities\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Search and Filter */}\n        <div className=\"flex gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n              <Input\n                placeholder=\"Search features & amenities...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <Tabs defaultValue=\"features\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"features\">Features</TabsTrigger>\n            <TabsTrigger value=\"amenities\">Amenities</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"features\" className=\"space-y-4\">\n            {/* Category Filter for Features */}\n            <div className=\"flex flex-wrap gap-2\">\n              <Button\n                variant={selectedCategory === 'all' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory('all')}\n              >\n                All\n              </Button>\n              {Object.keys(featureCategories).map((category) => (\n                <Button\n                  key={category}\n                  variant={selectedCategory === category ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedCategory(category)}\n                  className=\"capitalize\"\n                >\n                  {category}\n                </Button>\n              ))}\n            </div>\n\n            {/* Selected Features */}\n            {features.length > 0 && (\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Selected Features ({features.length})</Label>\n                <div className=\"flex flex-wrap gap-2\">\n                  {features.map((feature) => (\n                    <Badge key={feature} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      {feature}\n                      <X\n                        className=\"h-3 w-3 cursor-pointer hover:text-red-500\"\n                        onClick={() => removeFeature(feature)}\n                      />\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Add Custom Feature */}\n            <div className=\"flex gap-2\">\n              <Input\n                placeholder=\"Add custom feature...\"\n                value={newFeature}\n                onChange={(e) => setNewFeature(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && addCustomFeature()}\n              />\n              <Button onClick={addCustomFeature} size=\"sm\">\n                <Plus className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {/* Available Features */}\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-60 overflow-y-auto\">\n              {filteredFeatures.map((feature) => (\n                <div\n                  key={feature}\n                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-sm ${\n                    features.includes(feature)\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => handleFeatureToggle(feature)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium\">{feature}</span>\n                    {features.includes(feature) && (\n                      <Check className=\"h-4 w-4 text-blue-600\" />\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"amenities\" className=\"space-y-4\">\n            {/* Category Filter for Amenities */}\n            <div className=\"flex flex-wrap gap-2\">\n              <Button\n                variant={selectedCategory === 'all' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory('all')}\n              >\n                All\n              </Button>\n              {Object.keys(amenityCategories).map((category) => (\n                <Button\n                  key={category}\n                  variant={selectedCategory === category ? 'default' : 'outline'}\n                  size=\"sm\"\n                  onClick={() => setSelectedCategory(category)}\n                  className=\"capitalize\"\n                >\n                  {category}\n                </Button>\n              ))}\n            </div>\n\n            {/* Selected Amenities */}\n            {amenities.length > 0 && (\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Selected Amenities ({amenities.length})</Label>\n                <div className=\"flex flex-wrap gap-2\">\n                  {amenities.map((amenity) => (\n                    <Badge key={amenity} variant=\"secondary\" className=\"flex items-center gap-1\">\n                      {amenity}\n                      <X\n                        className=\"h-3 w-3 cursor-pointer hover:text-red-500\"\n                        onClick={() => removeAmenity(amenity)}\n                      />\n                    </Badge>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Add Custom Amenity */}\n            <div className=\"flex gap-2\">\n              <Input\n                placeholder=\"Add custom amenity...\"\n                value={newAmenity}\n                onChange={(e) => setNewAmenity(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && addCustomAmenity()}\n              />\n              <Button onClick={addCustomAmenity} size=\"sm\">\n                <Plus className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {/* Available Amenities */}\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-60 overflow-y-auto\">\n              {filteredAmenities.map((amenity) => (\n                <div\n                  key={amenity}\n                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-sm ${\n                    amenities.includes(amenity)\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                  onClick={() => handleAmenityToggle(amenity)}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium\">{amenity}</span>\n                    {amenities.includes(amenity) && (\n                      <Check className=\"h-4 w-4 text-blue-600\" />\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </TabsContent>\n        </Tabs>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAoBA,MAAM,oBAAoB;IACxB,OAAO;QAAC;QAAiB;QAAO;QAAW;QAAY;QAAU;QAAc;QAAc;QAAgB;QAAY;QAAQ;QAAY;KAAe;IAC5J,SAAS;QAAC;QAAyB;QAAmB;QAAmB;QAAmB;QAAW;QAAmB;QAAe;QAAqB;QAAwB;QAAiB;QAAiB;KAAW;IACnO,QAAQ;QAAC;QAAiB;QAAmB;QAAe;QAAkB;QAAmB;QAAoB;QAAoB;KAAiB;IAC1J,aAAa;QAAC;QAAqB;QAAgB;QAAmB;QAAiB;QAAY;QAAO;QAAmB;KAAa;IAC1I,WAAW;QAAC;QAAuB;QAAY;QAAmB;QAAoB;QAAmB;QAAe;QAAwB;KAAmB;IACnK,SAAS;QAAC;QAAsB;QAAkB;QAAY;QAAmB;QAAkB;QAAuB;QAAgB;KAAS;AACrJ;AAEA,MAAM,oBAAoB;IACxB,YAAY;QAAC;QAAmB;QAAkB;QAAkB;QAAc;QAAc;QAAe;QAAgB;QAAc;QAAW;QAAW;QAAY;KAAQ;IACvL,UAAU;QAAC;QAAmB;QAAmB;QAAmB;QAAiB;QAAqB;QAAsB;QAAoB;QAAuB;QAAoB;QAAoB;QAAqB;KAAU;IAClP,YAAY;QAAC;QAAyB;QAAmB;QAAe;QAAkB;QAAkB;QAAmB;QAAsB;KAAe;IACpK,QAAQ;QAAC;QAAgB;QAA4B;QAAgB;QAAc;QAAyB;QAAsB;QAAoB;KAAoB;IAC1K,QAAQ;QAAC;QAAgB;QAAkB;QAAoB;QAAkB;QAAe;QAAe;QAAe;KAAc;IAC5I,eAAe;QAAC;QAAyB;QAAe;QAAiB;QAAuB;QAAyB;QAAmB;QAAuB;KAAY;AACjL;AAEe,SAAS,0BAA0B,EAChD,QAAQ,EACR,SAAS,EACT,gBAAgB,EAChB,iBAAiB,EACjB,YAAY,EACZ,aAAa,EACkB;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,mBAAmB;IACnB,MAAM,mBAAmB,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,WAAW;QAEf,IAAI,qBAAqB,SAAS,iBAAiB,CAAC,iBAAmD,EAAE;YACvG,WAAW,iBAAiB,CAAC,iBAAmD;QAClF;QAEA,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEzD;QAEA,OAAO;IACT,GAAG;QAAC;QAAc;QAAY;KAAiB;IAE/C,MAAM,oBAAoB,CAAA,GAAA,oUAAA,CAAA,UAAO,AAAD,EAAE;QAChC,IAAI,WAAW;QAEf,IAAI,qBAAqB,SAAS,iBAAiB,CAAC,iBAAmD,EAAE;YACvG,WAAW,iBAAiB,CAAC,iBAAmD;QAClF;QAEA,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEzD;QAEA,OAAO;IACT,GAAG;QAAC;QAAe;QAAY;KAAiB;IAEhD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO;YACL,iBAAiB;mBAAI;gBAAU;aAAQ;QACzC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,UAAU,QAAQ,CAAC,UAAU;YAC/B,kBAAkB,UAAU,MAAM,CAAC,CAAA,IAAK,MAAM;QAChD,OAAO;YACL,kBAAkB;mBAAI;gBAAW;aAAQ;QAC3C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,MAAM,CAAC,SAAS,QAAQ,CAAC,WAAW,IAAI,KAAK;YAC9D,iBAAiB;mBAAI;gBAAU,WAAW,IAAI;aAAG;YACjD,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,QAAQ,CAAC,WAAW,IAAI,KAAK;YAC/D,kBAAkB;mBAAI;gBAAW,WAAW,IAAI;aAAG;YACnD,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,iBAAiB,SAAS,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,MAAM,gBAAgB,CAAC;QACrB,kBAAkB,UAAU,MAAM,CAAC,CAAA,IAAK,MAAM;IAChD;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6WAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAIlC,6WAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,6WAAC,gIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAW,WAAU;;0CACtC,6WAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6WAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6WAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAY;;;;;;;;;;;;0CAGjC,6WAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDAEtC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,qBAAqB,QAAQ,YAAY;gDAClD,MAAK;gDACL,SAAS,IAAM,oBAAoB;0DACpC;;;;;;4CAGA,OAAO,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,yBACnC,6WAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,qBAAqB,WAAW,YAAY;oDACrD,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,WAAU;8DAET;mDANI;;;;;;;;;;;oCAYV,SAAS,MAAM,GAAG,mBACjB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;;oDAAsB;oDAAoB,SAAS,MAAM;oDAAC;;;;;;;0DAC3E,6WAAC;gDAAI,WAAU;0DACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6WAAC,iIAAA,CAAA,QAAK;wDAAe,SAAQ;wDAAY,WAAU;;4DAChD;0EACD,6WAAC,gRAAA,CAAA,IAAC;gEACA,WAAU;gEACV,SAAS,IAAM,cAAc;;;;;;;uDAJrB;;;;;;;;;;;;;;;;kDAapB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0DAE1C,6WAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAkB,MAAK;0DACtC,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKpB,6WAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6WAAC;gDAEC,WAAW,CAAC,oEAAoE,EAC9E,SAAS,QAAQ,CAAC,WACd,6CACA,yCACJ;gDACF,SAAS,IAAM,oBAAoB;0DAEnC,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAK,WAAU;sEAAuB;;;;;;wDACtC,SAAS,QAAQ,CAAC,0BACjB,6WAAC,wRAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;+CAXhB;;;;;;;;;;;;;;;;0CAmBb,6WAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;;kDAEvC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,kIAAA,CAAA,SAAM;gDACL,SAAS,qBAAqB,QAAQ,YAAY;gDAClD,MAAK;gDACL,SAAS,IAAM,oBAAoB;0DACpC;;;;;;4CAGA,OAAO,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,yBACnC,6WAAC,kIAAA,CAAA,SAAM;oDAEL,SAAS,qBAAqB,WAAW,YAAY;oDACrD,MAAK;oDACL,SAAS,IAAM,oBAAoB;oDACnC,WAAU;8DAET;mDANI;;;;;;;;;;;oCAYV,UAAU,MAAM,GAAG,mBAClB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;;oDAAsB;oDAAqB,UAAU,MAAM;oDAAC;;;;;;;0DAC7E,6WAAC;gDAAI,WAAU;0DACZ,UAAU,GAAG,CAAC,CAAC,wBACd,6WAAC,iIAAA,CAAA,QAAK;wDAAe,SAAQ;wDAAY,WAAU;;4DAChD;0EACD,6WAAC,gRAAA,CAAA,IAAC;gEACA,WAAU;gEACV,SAAS,IAAM,cAAc;;;;;;;uDAJrB;;;;;;;;;;;;;;;;kDAapB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;0DAE1C,6WAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAkB,MAAK;0DACtC,cAAA,6WAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAKpB,6WAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,6WAAC;gDAEC,WAAW,CAAC,oEAAoE,EAC9E,UAAU,QAAQ,CAAC,WACf,6CACA,yCACJ;gDACF,SAAS,IAAM,oBAAoB;0DAEnC,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAK,WAAU;sEAAuB;;;;;;wDACtC,UAAU,QAAQ,CAAC,0BAClB,6WAAC,wRAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;+CAXhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBvB", "debugId": null}}, {"offset": {"line": 4581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/LegalDocumentsUpload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { FileText, Upload, X, Check, AlertCircle, Info } from 'lucide-react'\nimport EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'\n\ninterface LegalDocument {\n  type: string\n  label: string\n  required: boolean\n  files: Array<{\n    key: string\n    name: string\n    type: string\n    url: string\n    uploadedAt?: Date\n  }>\n}\n\ninterface LegalDocumentsUploadProps {\n  legalDocuments: string[]\n  onLegalDocumentsChange: (documents: string[]) => void\n}\n\nconst legalDocumentTypes = [\n  // Property Title Documents\n  { value: 'title_deed', label: 'Title Deed', required: false, category: 'title', description: 'Legal document proving ownership' },\n  { value: 'sale_deed', label: 'Sale Deed', required: false, category: 'title', description: 'Document of property sale transaction' },\n  { value: 'property_card', label: 'Property Card', required: false, category: 'title', description: 'Government issued property card' },\n  { value: 'survey_settlement', label: 'Survey Settlement', required: false, category: 'title', description: 'Land survey and settlement records' },\n  \n  // Revenue Documents\n  { value: 'revenue_records', label: 'Revenue Records', required: false, category: 'revenue', description: 'Government revenue department records' },\n  { value: 'mutation_certificate', label: 'Mutation Certificate', required: false, category: 'revenue', description: 'Certificate of property transfer' },\n  { value: 'encumbrance_certificate', label: 'Encumbrance Certificate', required: false, category: 'revenue', description: 'Certificate of property transactions' },\n  { value: 'tax_receipts', label: 'Tax Receipts', required: false, category: 'revenue', description: 'Property tax payment receipts' },\n  \n  // Building Approvals\n  { value: 'building_approval', label: 'Building Approval', required: false, category: 'approvals', description: 'Municipal building approval' },\n  { value: 'completion_certificate', label: 'Completion Certificate', required: false, category: 'approvals', description: 'Building completion certificate' },\n  { value: 'occupancy_certificate', label: 'Occupancy Certificate', required: false, category: 'approvals', description: 'Certificate for occupancy' },\n  { value: 'environmental_clearance', label: 'Environmental Clearance', required: false, category: 'approvals', description: 'Environmental impact clearance' },\n  \n  // Safety & Utilities\n  { value: 'fire_safety_certificate', label: 'Fire Safety Certificate', required: false, category: 'utilities', description: 'Fire department safety certificate' },\n  { value: 'electricity_connection', label: 'Electricity Connection', required: false, category: 'utilities', description: 'Electricity connection documents' },\n  { value: 'water_connection', label: 'Water Connection', required: false, category: 'utilities', description: 'Water supply connection documents' },\n  { value: 'sewage_connection', label: 'Sewage Connection', required: false, category: 'utilities', description: 'Sewage connection documents' },\n  \n  // Legal & Society\n  { value: 'society_documents', label: 'Society Documents', required: false, category: 'legal', description: 'Housing society registration documents' },\n  { value: 'power_of_attorney', label: 'Power of Attorney', required: false, category: 'legal', description: 'Legal power of attorney documents' },\n  { value: 'legal_opinion', label: 'Legal Opinion', required: false, category: 'legal', description: 'Legal expert opinion on property' },\n  { value: 'insurance_documents', label: 'Insurance Documents', required: false, category: 'legal', description: 'Property insurance documents' }\n]\n\nconst documentCategories = {\n  title: { label: 'Property Title', icon: '📋', color: 'bg-blue-50 border-blue-200' },\n  revenue: { label: 'Revenue Records', icon: '💰', color: 'bg-green-50 border-green-200' },\n  approvals: { label: 'Building Approvals', icon: '🏗️', color: 'bg-orange-50 border-orange-200' },\n  utilities: { label: 'Utilities & Safety', icon: '⚡', color: 'bg-purple-50 border-purple-200' },\n  legal: { label: 'Legal Documents', icon: '⚖️', color: 'bg-red-50 border-red-200' }\n}\n\nexport default function LegalDocumentsUpload({\n  legalDocuments,\n  onLegalDocumentsChange\n}: LegalDocumentsUploadProps) {\n  const [uploadedDocuments, setUploadedDocuments] = useState<Record<string, any[]>>({})\n  const [selectedCategory, setSelectedCategory] = useState<string>('title')\n\n  const handleFileUpload = (documentType: string, files: any[]) => {\n    setUploadedDocuments(prev => ({\n      ...prev,\n      [documentType]: files\n    }))\n    \n    // Update the legal documents array with URLs\n    const urls = files.map(file => file.url)\n    const currentUrls = legalDocuments.filter(url => \n      !Object.values(uploadedDocuments).flat().some(doc => doc.url === url)\n    )\n    onLegalDocumentsChange([...currentUrls, ...urls])\n  }\n\n  const removeDocument = (documentType: string, fileKey: string) => {\n    const updatedFiles = uploadedDocuments[documentType]?.filter(file => file.key !== fileKey) || []\n    setUploadedDocuments(prev => ({\n      ...prev,\n      [documentType]: updatedFiles\n    }))\n    \n    // Update legal documents array\n    const removedFile = uploadedDocuments[documentType]?.find(file => file.key === fileKey)\n    if (removedFile) {\n      onLegalDocumentsChange(legalDocuments.filter(url => url !== removedFile.url))\n    }\n  }\n\n  const getDocumentsByCategory = (category: string) => {\n    return legalDocumentTypes.filter(doc => doc.category === category)\n  }\n\n  const getTotalUploadedCount = () => {\n    return Object.values(uploadedDocuments).reduce((total, files) => total + files.length, 0)\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <FileText className=\"h-5 w-5\" />\n          Legal Documents Upload\n          <Badge variant=\"secondary\" className=\"ml-auto\">\n            {getTotalUploadedCount()} documents uploaded\n          </Badge>\n        </CardTitle>\n        <Alert>\n          <Info className=\"h-4 w-4\" />\n          <AlertDescription>\n            All legal documents are optional. Upload relevant documents to enhance property credibility and legal compliance.\n          </AlertDescription>\n        </Alert>\n      </CardHeader>\n      <CardContent>\n        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-5\">\n            {Object.entries(documentCategories).map(([key, category]) => (\n              <TabsTrigger key={key} value={key} className=\"text-xs\">\n                <span className=\"mr-1\">{category.icon}</span>\n                {category.label}\n              </TabsTrigger>\n            ))}\n          </TabsList>\n\n          {Object.entries(documentCategories).map(([categoryKey, category]) => (\n            <TabsContent key={categoryKey} value={categoryKey} className=\"space-y-4\">\n              <div className={`p-4 rounded-lg border ${category.color}`}>\n                <h3 className=\"font-medium text-gray-900 mb-2 flex items-center gap-2\">\n                  <span className=\"text-lg\">{category.icon}</span>\n                  {category.label}\n                </h3>\n                <p className=\"text-sm text-gray-600 mb-4\">\n                  Upload documents related to {category.label.toLowerCase()}. All documents are optional.\n                </p>\n\n                <div className=\"space-y-4\">\n                  {getDocumentsByCategory(categoryKey).map((docType) => (\n                    <div key={docType.value} className=\"bg-white p-4 rounded-lg border\">\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-2\">\n                            <Label className=\"font-medium\">{docType.label}</Label>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              Optional\n                            </Badge>\n                            {uploadedDocuments[docType.value]?.length && uploadedDocuments[docType.value]!.length > 0 && (\n                              <Badge variant=\"default\" className=\"text-xs\">\n                                <Check className=\"h-3 w-3 mr-1\" />\n                                {uploadedDocuments[docType.value]?.length || 0} uploaded\n                              </Badge>\n                            )}\n                          </div>\n                          <p className=\"text-sm text-gray-600 mt-1\">{docType.description}</p>\n                        </div>\n                      </div>\n\n                      {/* File Upload */}\n                      <div className=\"space-y-3\">\n                        <EnhancedS3Upload\n                          uploadType=\"property-document\"\n                          onUploadComplete={(results) => {\n                            const files = results.map(result => ({\n                              key: result.fileKey,\n                              name: result.fileName,\n                              type: 'application/pdf', // Default type\n                              url: result.publicUrl,\n                              uploadedAt: new Date()\n                            }))\n                            handleFileUpload(docType.value, files)\n                          }}\n                          onUploadError={(error) => {\n                            console.error('Upload error:', error)\n                          }}\n                          maxFiles={5}\n                          allowMultiple={true}\n                          className=\"border-dashed border-2 border-gray-300 rounded-lg p-3 hover:border-gray-400 transition-colors min-h-[120px] max-h-[200px] overflow-hidden\"\n                        />\n\n                        {/* Uploaded Files */}\n                        {uploadedDocuments[docType.value]?.length && uploadedDocuments[docType.value]!.length > 0 && (\n                          <div className=\"space-y-2\">\n                            <Label className=\"text-sm font-medium\">Uploaded Files:</Label>\n                            <div className=\"space-y-2\">\n                              {uploadedDocuments[docType.value]?.map((file, index) => (\n                                <div\n                                  key={file.key || index}\n                                  className=\"flex items-center justify-between p-2 bg-gray-50 rounded border\"\n                                >\n                                  <div className=\"flex items-center gap-2\">\n                                    <FileText className=\"h-4 w-4 text-gray-500\" />\n                                    <span className=\"text-sm font-medium\">{file.name}</span>\n                                    <Badge variant=\"outline\" className=\"text-xs\">\n                                      {file.type}\n                                    </Badge>\n                                  </div>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    onClick={() => removeDocument(docType.value, file.key)}\n                                    className=\"text-red-500 hover:text-red-700 hover:bg-red-50\"\n                                  >\n                                    <X className=\"h-4 w-4\" />\n                                  </Button>\n                                </div>\n                              ))}\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </TabsContent>\n          ))}\n        </Tabs>\n\n        {/* Summary */}\n        {getTotalUploadedCount() > 0 && (\n          <div className=\"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg\">\n            <div className=\"flex items-center gap-2 text-green-800\">\n              <Check className=\"h-5 w-5\" />\n              <span className=\"font-medium\">\n                {getTotalUploadedCount()} legal documents uploaded successfully\n              </span>\n            </div>\n            <p className=\"text-sm text-green-700 mt-1\">\n              These documents will help establish property credibility and legal compliance.\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;;AA8BA,MAAM,qBAAqB;IACzB,2BAA2B;IAC3B;QAAE,OAAO;QAAc,OAAO;QAAc,UAAU;QAAO,UAAU;QAAS,aAAa;IAAmC;IAChI;QAAE,OAAO;QAAa,OAAO;QAAa,UAAU;QAAO,UAAU;QAAS,aAAa;IAAwC;IACnI;QAAE,OAAO;QAAiB,OAAO;QAAiB,UAAU;QAAO,UAAU;QAAS,aAAa;IAAkC;IACrI;QAAE,OAAO;QAAqB,OAAO;QAAqB,UAAU;QAAO,UAAU;QAAS,aAAa;IAAqC;IAEhJ,oBAAoB;IACpB;QAAE,OAAO;QAAmB,OAAO;QAAmB,UAAU;QAAO,UAAU;QAAW,aAAa;IAAwC;IACjJ;QAAE,OAAO;QAAwB,OAAO;QAAwB,UAAU;QAAO,UAAU;QAAW,aAAa;IAAmC;IACtJ;QAAE,OAAO;QAA2B,OAAO;QAA2B,UAAU;QAAO,UAAU;QAAW,aAAa;IAAuC;IAChK;QAAE,OAAO;QAAgB,OAAO;QAAgB,UAAU;QAAO,UAAU;QAAW,aAAa;IAAgC;IAEnI,qBAAqB;IACrB;QAAE,OAAO;QAAqB,OAAO;QAAqB,UAAU;QAAO,UAAU;QAAa,aAAa;IAA8B;IAC7I;QAAE,OAAO;QAA0B,OAAO;QAA0B,UAAU;QAAO,UAAU;QAAa,aAAa;IAAkC;IAC3J;QAAE,OAAO;QAAyB,OAAO;QAAyB,UAAU;QAAO,UAAU;QAAa,aAAa;IAA4B;IACnJ;QAAE,OAAO;QAA2B,OAAO;QAA2B,UAAU;QAAO,UAAU;QAAa,aAAa;IAAiC;IAE5J,qBAAqB;IACrB;QAAE,OAAO;QAA2B,OAAO;QAA2B,UAAU;QAAO,UAAU;QAAa,aAAa;IAAqC;IAChK;QAAE,OAAO;QAA0B,OAAO;QAA0B,UAAU;QAAO,UAAU;QAAa,aAAa;IAAmC;IAC5J;QAAE,OAAO;QAAoB,OAAO;QAAoB,UAAU;QAAO,UAAU;QAAa,aAAa;IAAoC;IACjJ;QAAE,OAAO;QAAqB,OAAO;QAAqB,UAAU;QAAO,UAAU;QAAa,aAAa;IAA8B;IAE7I,kBAAkB;IAClB;QAAE,OAAO;QAAqB,OAAO;QAAqB,UAAU;QAAO,UAAU;QAAS,aAAa;IAAyC;IACpJ;QAAE,OAAO;QAAqB,OAAO;QAAqB,UAAU;QAAO,UAAU;QAAS,aAAa;IAAoC;IAC/I;QAAE,OAAO;QAAiB,OAAO;QAAiB,UAAU;QAAO,UAAU;QAAS,aAAa;IAAmC;IACtI;QAAE,OAAO;QAAuB,OAAO;QAAuB,UAAU;QAAO,UAAU;QAAS,aAAa;IAA+B;CAC/I;AAED,MAAM,qBAAqB;IACzB,OAAO;QAAE,OAAO;QAAkB,MAAM;QAAM,OAAO;IAA6B;IAClF,SAAS;QAAE,OAAO;QAAmB,MAAM;QAAM,OAAO;IAA+B;IACvF,WAAW;QAAE,OAAO;QAAsB,MAAM;QAAO,OAAO;IAAiC;IAC/F,WAAW;QAAE,OAAO;QAAsB,MAAM;QAAK,OAAO;IAAiC;IAC7F,OAAO;QAAE,OAAO;QAAmB,MAAM;QAAM,OAAO;IAA2B;AACnF;AAEe,SAAS,qBAAqB,EAC3C,cAAc,EACd,sBAAsB,EACI;IAC1B,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAyB,CAAC;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,mBAAmB,CAAC,cAAsB;QAC9C,qBAAqB,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,aAAa,EAAE;YAClB,CAAC;QAED,6CAA6C;QAC7C,MAAM,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,GAAG;QACvC,MAAM,cAAc,eAAe,MAAM,CAAC,CAAA,MACxC,CAAC,OAAO,MAAM,CAAC,mBAAmB,IAAI,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAEnE,uBAAuB;eAAI;eAAgB;SAAK;IAClD;IAEA,MAAM,iBAAiB,CAAC,cAAsB;QAC5C,MAAM,eAAe,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAA,OAAQ,KAAK,GAAG,KAAK,YAAY,EAAE;QAChG,qBAAqB,CAAA,OAAQ,CAAC;gBAC5B,GAAG,IAAI;gBACP,CAAC,aAAa,EAAE;YAClB,CAAC;QAED,+BAA+B;QAC/B,MAAM,cAAc,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAA,OAAQ,KAAK,GAAG,KAAK;QAC/E,IAAI,aAAa;YACf,uBAAuB,eAAe,MAAM,CAAC,CAAA,MAAO,QAAQ,YAAY,GAAG;QAC7E;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO,mBAAmB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;IAC3D;IAEA,MAAM,wBAAwB;QAC5B,OAAO,OAAO,MAAM,CAAC,mBAAmB,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,MAAM,EAAE;IACzF;IAEA,qBACE,6WAAC,gIAAA,CAAA,OAAI;;0BACH,6WAAC,gIAAA,CAAA,aAAU;;kCACT,6WAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6WAAC,kSAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;0CAEhC,6WAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC;oCAAwB;;;;;;;;;;;;;kCAG7B,6WAAC,iIAAA,CAAA,QAAK;;0CACJ,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6WAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;;;;;;;0BAKtB,6WAAC,gIAAA,CAAA,cAAW;;kCACV,6WAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAkB,eAAe;wBAAqB,WAAU;;0CAC3E,6WAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;0CACjB,OAAO,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBACtD,6WAAC,gIAAA,CAAA,cAAW;wCAAW,OAAO;wCAAK,WAAU;;0DAC3C,6WAAC;gDAAK,WAAU;0DAAQ,SAAS,IAAI;;;;;;4CACpC,SAAS,KAAK;;uCAFC;;;;;;;;;;4BAOrB,OAAO,OAAO,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,aAAa,SAAS,iBAC9D,6WAAC,gIAAA,CAAA,cAAW;oCAAmB,OAAO;oCAAa,WAAU;8CAC3D,cAAA,6WAAC;wCAAI,WAAW,CAAC,sBAAsB,EAAE,SAAS,KAAK,EAAE;;0DACvD,6WAAC;gDAAG,WAAU;;kEACZ,6WAAC;wDAAK,WAAU;kEAAW,SAAS,IAAI;;;;;;oDACvC,SAAS,KAAK;;;;;;;0DAEjB,6WAAC;gDAAE,WAAU;;oDAA6B;oDACX,SAAS,KAAK,CAAC,WAAW;oDAAG;;;;;;;0DAG5D,6WAAC;gDAAI,WAAU;0DACZ,uBAAuB,aAAa,GAAG,CAAC,CAAC,wBACxC,6WAAC;wDAAwB,WAAU;;0EACjC,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAI,WAAU;;8FACb,6WAAC,iIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAe,QAAQ,KAAK;;;;;;8FAC7C,6WAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;8FAAU;;;;;;gFAG5C,iBAAiB,CAAC,QAAQ,KAAK,CAAC,EAAE,UAAU,iBAAiB,CAAC,QAAQ,KAAK,CAAC,CAAE,MAAM,GAAG,mBACtF,6WAAC,iIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;;sGACjC,6WAAC,wRAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAChB,iBAAiB,CAAC,QAAQ,KAAK,CAAC,EAAE,UAAU;wFAAE;;;;;;;;;;;;;sFAIrD,6WAAC;4EAAE,WAAU;sFAA8B,QAAQ,WAAW;;;;;;;;;;;;;;;;;0EAKlE,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,oJAAA,CAAA,UAAgB;wEACf,YAAW;wEACX,kBAAkB,CAAC;4EACjB,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oFACnC,KAAK,OAAO,OAAO;oFACnB,MAAM,OAAO,QAAQ;oFACrB,MAAM;oFACN,KAAK,OAAO,SAAS;oFACrB,YAAY,IAAI;gFAClB,CAAC;4EACD,iBAAiB,QAAQ,KAAK,EAAE;wEAClC;wEACA,eAAe,CAAC;4EACd,QAAQ,KAAK,CAAC,iBAAiB;wEACjC;wEACA,UAAU;wEACV,eAAe;wEACf,WAAU;;;;;;oEAIX,iBAAiB,CAAC,QAAQ,KAAK,CAAC,EAAE,UAAU,iBAAiB,CAAC,QAAQ,KAAK,CAAC,CAAE,MAAM,GAAG,mBACtF,6WAAC;wEAAI,WAAU;;0FACb,6WAAC,iIAAA,CAAA,QAAK;gFAAC,WAAU;0FAAsB;;;;;;0FACvC,6WAAC;gFAAI,WAAU;0FACZ,iBAAiB,CAAC,QAAQ,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,sBAC5C,6WAAC;wFAEC,WAAU;;0GAEV,6WAAC;gGAAI,WAAU;;kHACb,6WAAC,kSAAA,CAAA,WAAQ;wGAAC,WAAU;;;;;;kHACpB,6WAAC;wGAAK,WAAU;kHAAuB,KAAK,IAAI;;;;;;kHAChD,6WAAC,iIAAA,CAAA,QAAK;wGAAC,SAAQ;wGAAU,WAAU;kHAChC,KAAK,IAAI;;;;;;;;;;;;0GAGd,6WAAC,kIAAA,CAAA,SAAM;gGACL,SAAQ;gGACR,MAAK;gGACL,SAAS,IAAM,eAAe,QAAQ,KAAK,EAAE,KAAK,GAAG;gGACrD,WAAU;0GAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oGAAC,WAAU;;;;;;;;;;;;uFAhBV,KAAK,GAAG,IAAI;;;;;;;;;;;;;;;;;;;;;;;uDAhDrB,QAAQ,KAAK;;;;;;;;;;;;;;;;mCAZb;;;;;;;;;;;oBA6FrB,0BAA0B,mBACzB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6WAAC;wCAAK,WAAU;;4CACb;4CAAwB;;;;;;;;;;;;;0CAG7B,6WAAC;gCAAE,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 5226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/PropertyImagesUpload.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Upload, X, Image as ImageIcon, Eye, AlertCircle, Info } from 'lucide-react'\nimport EnhancedS3Upload from '@/components/FileUpload/EnhancedS3Upload'\nimport { toast } from 'sonner'\nimport { PropertyImage, PropertyImageFormData } from '@/types/shared'\n\ninterface PropertyImagesUploadProps {\n  images: PropertyImageFormData[]\n  onImagesChange: (images: PropertyImageFormData[]) => void\n}\n\nconst PropertyImagesUpload: React.FC<PropertyImagesUploadProps> = ({\n  images,\n  onImagesChange\n}) => {\n  const [isUploading, setIsUploading] = useState(false)\n\n  const handleImageUpload = (uploadResults: Array<{ fileKey: string; publicUrl: string; fileName: string }>) => {\n    const newImages = uploadResults.map(result => ({\n      key: result.fileKey,\n      name: result.fileName,\n      type: 'image/jpeg', // Default type, could be enhanced to detect actual type\n      url: result.publicUrl,\n      uploadedAt: new Date()\n    }))\n\n    const updatedImages = [...images, ...newImages]\n    onImagesChange(updatedImages)\n    toast.success(`${newImages.length} image(s) uploaded successfully!`)\n  }\n\n  const handleImageRemove = (imageKey: string) => {\n    const updatedImages = images.filter(img => img.key !== imageKey)\n    onImagesChange(updatedImages)\n    toast.success('Image removed successfully')\n  }\n\n  const handleImagePreview = (imageUrl: string) => {\n    window.open(imageUrl, '_blank')\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Property Images</h2>\n        <p className=\"text-gray-600\">\n          Upload high-quality images of your property. At least 1 image is required, maximum 10 images allowed.\n        </p>\n      </div>\n\n      {/* Upload Guidelines */}\n      <Alert>\n        <Info className=\"h-4 w-4\" />\n        <AlertDescription>\n          <strong>Image Guidelines:</strong>\n          <ul className=\"mt-2 space-y-1 text-sm\">\n            <li>• Upload high-resolution images (minimum 800x600 pixels)</li>\n            <li>• Supported formats: JPG, PNG, WebP</li>\n            <li>• Maximum file size: 5MB per image</li>\n            <li>• Include exterior, interior, and amenity photos</li>\n            <li>• First image will be used as the main property image</li>\n          </ul>\n        </AlertDescription>\n      </Alert>\n\n      {/* Upload Section */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Upload className=\"h-5 w-5\" />\n            Upload Property Images\n            <span className=\"text-sm font-normal text-gray-500\">\n              ({images.length}/10 images)\n            </span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          {images.length < 10 && (\n            <EnhancedS3Upload\n              uploadType=\"property-image\"\n              onUploadComplete={handleImageUpload}\n              onUploadError={(error) => {\n                console.error('Upload error:', error)\n                toast.error(`Upload failed: ${error}`)\n              }}\n              maxFiles={10 - images.length}\n              allowMultiple={true}\n              className=\"mb-6\"\n            />\n          )}\n\n          {images.length >= 10 && (\n            <Alert>\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>\n                Maximum limit of 10 images reached. Remove some images to upload new ones.\n              </AlertDescription>\n            </Alert>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Uploaded Images Grid */}\n      {images.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <ImageIcon className=\"h-5 w-5\" />\n              Uploaded Images ({images.length})\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n              {images.map((image, index) => (\n                <div\n                  key={image.key}\n                  className=\"relative group border border-gray-200 rounded-lg overflow-hidden bg-gray-50\"\n                >\n                  {/* Main Image Badge */}\n                  {index === 0 && (\n                    <div className=\"absolute top-2 left-2 z-10\">\n                      <span className=\"bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium\">\n                        Main Image\n                      </span>\n                    </div>\n                  )}\n\n                  {/* Image */}\n                  <div className=\"aspect-square relative\">\n                    <img\n                      src={image.url}\n                      alt={image.name}\n                      className=\"w-full h-full object-cover\"\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement\n                        target.src = '/placeholder-image.jpg' // Fallback image\n                      }}\n                    />\n                    \n                    {/* Overlay with actions */}\n                    <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100\">\n                      <div className=\"flex gap-2\">\n                        <Button\n                          size=\"sm\"\n                          variant=\"secondary\"\n                          onClick={() => handleImagePreview(image.url)}\n                          className=\"bg-white text-gray-900 hover:bg-gray-100\"\n                        >\n                          <Eye className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"destructive\"\n                          onClick={() => handleImageRemove(image.key)}\n                        >\n                          <X className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Image Info */}\n                  <div className=\"p-3\">\n                    <p className=\"text-sm font-medium text-gray-900 truncate\">\n                      {image.name}\n                    </p>\n                    <p className=\"text-xs text-gray-500 mt-1\">\n                      Uploaded {image.uploadedAt?.toLocaleDateString() || 'Recently'}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Reorder Instructions */}\n            {images.length > 1 && (\n              <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n                <p className=\"text-sm text-blue-800\">\n                  <strong>Tip:</strong> The first image will be used as the main property image in listings. \n                  You can reorder images by removing and re-uploading them in your preferred order.\n                </p>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Validation Message */}\n      {images.length === 0 && (\n        <Alert variant=\"destructive\">\n          <AlertCircle className=\"h-4 w-4\" />\n          <AlertDescription>\n            At least 1 property image is required to proceed.\n          </AlertDescription>\n        </Alert>\n      )}\n    </div>\n  )\n}\n\nexport default PropertyImagesUpload\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;AAiBA,MAAM,uBAA4D,CAAC,EACjE,MAAM,EACN,cAAc,EACf;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,YAAY,cAAc,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC7C,KAAK,OAAO,OAAO;gBACnB,MAAM,OAAO,QAAQ;gBACrB,MAAM;gBACN,KAAK,OAAO,SAAS;gBACrB,YAAY,IAAI;YAClB,CAAC;QAED,MAAM,gBAAgB;eAAI;eAAW;SAAU;QAC/C,eAAe;QACf,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,MAAM,CAAC,gCAAgC,CAAC;IACrE;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QACvD,eAAe;QACf,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6WAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6WAAC,iIAAA,CAAA,QAAK;;kCACJ,6WAAC,sRAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6WAAC,iIAAA,CAAA,mBAAgB;;0CACf,6WAAC;0CAAO;;;;;;0CACR,6WAAC;gCAAG,WAAU;;kDACZ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;kDACJ,6WAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAMV,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,0RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;8CAE9B,6WAAC;oCAAK,WAAU;;wCAAoC;wCAChD,OAAO,MAAM;wCAAC;;;;;;;;;;;;;;;;;;kCAItB,6WAAC,gIAAA,CAAA,cAAW;;4BACT,OAAO,MAAM,GAAG,oBACf,6WAAC,oJAAA,CAAA,UAAgB;gCACf,YAAW;gCACX,kBAAkB;gCAClB,eAAe,CAAC;oCACd,QAAQ,KAAK,CAAC,iBAAiB;oCAC/B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,OAAO;gCACvC;gCACA,UAAU,KAAK,OAAO,MAAM;gCAC5B,eAAe;gCACf,WAAU;;;;;;4BAIb,OAAO,MAAM,IAAI,oBAChB,6WAAC,iIAAA,CAAA,QAAK;;kDACJ,6WAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6WAAC,iIAAA,CAAA,mBAAgB;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;YASzB,OAAO,MAAM,GAAG,mBACf,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,wRAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;gCAAY;gCACf,OAAO,MAAM;gCAAC;;;;;;;;;;;;kCAGpC,6WAAC,gIAAA,CAAA,cAAW;;0CACV,6WAAC;gCAAI,WAAU;0CACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6WAAC;wCAEC,WAAU;;4CAGT,UAAU,mBACT,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;8DAAoE;;;;;;;;;;;0DAOxF,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDACC,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,IAAI;wDACf,WAAU;wDACV,SAAS,CAAC;4DACR,MAAM,SAAS,EAAE,MAAM;4DACvB,OAAO,GAAG,GAAG,yBAAyB,iBAAiB;;wDACzD;;;;;;kEAIF,6WAAC;wDAAI,WAAU;kEACb,cAAA,6WAAC;4DAAI,WAAU;;8EACb,6WAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,mBAAmB,MAAM,GAAG;oEAC3C,WAAU;8EAEV,cAAA,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;8EAEjB,6WAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,IAAM,kBAAkB,MAAM,GAAG;8EAE1C,cAAA,6WAAC,gRAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOrB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAE,WAAU;kEACV,MAAM,IAAI;;;;;;kEAEb,6WAAC;wDAAE,WAAU;;4DAA6B;4DAC9B,MAAM,UAAU,EAAE,wBAAwB;;;;;;;;;;;;;;uCApDnD,MAAM,GAAG;;;;;;;;;;4BA4DnB,OAAO,MAAM,GAAG,mBACf,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAE,WAAU;;sDACX,6WAAC;sDAAO;;;;;;wCAAa;;;;;;;;;;;;;;;;;;;;;;;;YAUhC,OAAO,MAAM,KAAK,mBACjB,6WAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,6WAAC,wSAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6WAAC,iIAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;;;;;;;AAO5B;uCAEe", "debugId": null}}, {"offset": {"line": 5699, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/forms/property/BookingForm.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { toast } from 'sonner'\nimport {\n  Download,\n  CreditCard,\n  FileText,\n  User,\n  IndianRupee,\n  Building,\n  CheckCircle\n} from 'lucide-react'\nimport { jsPDF } from \"jspdf\";\n\ninterface BookingFormData {\n  // Customer Details\n  customerName: string\n  customerEmail: string\n  customerPhone: string\n  customerAddress: string\n  \n  // Booking Details\n  bookingAmount: number\n  paymentMode: 'online' | 'cheque' | 'cash' | 'bank_transfer'\n  chequeNumber?: string\n  bankName?: string\n  transactionId?: string\n  \n  // Property Preferences\n  floorPreference: string\n  unitType: string\n  facingPreference: string\n  \n  // Financial Details\n  loanRequired: boolean\n  monthlyIncome?: number\n  \n  // Nominee Details\n  nomineeName?: string\n  nomineeRelation?: string\n  nomineePhone?: string\n  \n  // Additional\n  specialRequests?: string\n}\n\ninterface BookingFormProps {\n  propertyData: {\n    name: string\n    location: {\n      address: string\n      city: string\n      state: string\n      pincode: string\n    }\n    pricePerStock: number\n    totalStocks: number\n    expectedReturns: number\n    maturityPeriodMonths: number\n    propertyType: string\n  }\n  onBookingComplete?: (bookingData: BookingFormData) => void\n}\n\nexport default function BookingForm({ propertyData, onBookingComplete }: BookingFormProps) {\n  const [formData, setFormData] = useState<BookingFormData>({\n    customerName: '',\n    customerEmail: '',\n    customerPhone: '',\n    customerAddress: '',\n    bookingAmount: Math.round(propertyData.pricePerStock * 0.1), // 10% of price per stock\n    paymentMode: 'online',\n    floorPreference: '',\n    unitType: '',\n    facingPreference: '',\n    loanRequired: false,\n    specialRequests: ''\n  })\n\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)\n  const [bookingId, setBookingId] = useState('')\n\n  useEffect(() => {\n    // Generate unique booking ID\n    const generateBookingId = () => {\n      const timestamp = Date.now().toString(36)\n      const random = Math.random().toString(36).substring(2, 7)\n      return `BK${timestamp}${random}`.toUpperCase()\n    }\n    setBookingId(generateBookingId())\n  }, [])\n\n  const handleInputChange = (field: string, value: string | number | boolean) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }))\n  }\n\n  const generateBookingPDF = async () => {\n    setIsGeneratingPDF(true)\n\n    try {\n      // Create PDF using static import\n      const pdf = new jsPDF()\n      const pageWidth = pdf.internal.pageSize.width\n      const margin = 20\n      let yPosition = 30\n\n      // Header\n      pdf.setFontSize(20)\n      pdf.setFont('helvetica', 'bold')\n      pdf.text('PROPERTY BOOKING RECEIPT', pageWidth / 2, yPosition, { align: 'center' })\n      \n      yPosition += 20\n      pdf.setFontSize(12)\n      pdf.setFont('helvetica', 'normal')\n      pdf.text(`Booking ID: ${bookingId}`, pageWidth / 2, yPosition, { align: 'center' })\n      \n      yPosition += 10\n      pdf.text(`Date: ${new Date().toLocaleDateString('en-IN')}`, pageWidth / 2, yPosition, { align: 'center' })\n      \n      yPosition += 25\n\n      // Property Details Section\n      pdf.setFontSize(14)\n      pdf.setFont('helvetica', 'bold')\n      pdf.text('PROPERTY DETAILS', margin, yPosition)\n      yPosition += 15\n\n      pdf.setFontSize(10)\n      pdf.setFont('helvetica', 'normal')\n      \n      const propertyDetails = [\n        ['Property Name:', propertyData.name],\n        ['Property Type:', propertyData.propertyType],\n        ['Location:', `${propertyData.location.address}, ${propertyData.location.city}`],\n        ['City/State:', `${propertyData.location.city}, ${propertyData.location.state} - ${propertyData.location.pincode}`],\n        ['Price per Stock:', `₹${propertyData.pricePerStock.toLocaleString('en-IN')}`],\n        ['Total Stocks Available:', propertyData.totalStocks.toString()],\n        ['Expected Returns:', `${propertyData.expectedReturns}% per annum`],\n        ['Maturity Period:', `${propertyData.maturityPeriodMonths} months`]\n      ]\n\n      propertyDetails.forEach(([label, value]) => {\n        pdf.text(label || '', margin, yPosition)\n        pdf.text(value || '', margin + 60, yPosition)\n        yPosition += 12\n      })\n\n      yPosition += 10\n\n      // Customer Details Section\n      pdf.setFontSize(14)\n      pdf.setFont('helvetica', 'bold')\n      pdf.text('CUSTOMER DETAILS', margin, yPosition)\n      yPosition += 15\n\n      pdf.setFontSize(10)\n      pdf.setFont('helvetica', 'normal')\n      \n      const customerDetails = [\n        ['Name:', formData.customerName],\n        ['Email:', formData.customerEmail],\n        ['Phone:', formData.customerPhone],\n        ['Address:', formData.customerAddress],\n        ['Floor Preference:', formData.floorPreference || 'No preference'],\n        ['Unit Type:', formData.unitType || 'Standard'],\n        ['Facing Preference:', formData.facingPreference || 'No preference']\n      ]\n\n      customerDetails.forEach(([label, value]) => {\n        pdf.text(label || '', margin, yPosition)\n        pdf.text(value || '', margin + 60, yPosition)\n        yPosition += 12\n      })\n\n      yPosition += 10\n\n      // Booking Details Section\n      pdf.setFontSize(14)\n      pdf.setFont('helvetica', 'bold')\n      pdf.text('BOOKING DETAILS', margin, yPosition)\n      yPosition += 15\n\n      pdf.setFontSize(10)\n      pdf.setFont('helvetica', 'normal')\n      \n      const bookingDetails = [\n        ['Booking Amount:', `₹${formData.bookingAmount.toLocaleString('en-IN')}`],\n        ['Payment Mode:', formData.paymentMode.toUpperCase()],\n        ...(formData.chequeNumber ? [['Cheque Number:', formData.chequeNumber]] : []),\n        ...(formData.bankName ? [['Bank Name:', formData.bankName]] : []),\n        ...(formData.transactionId ? [['Transaction ID:', formData.transactionId]] : []),\n        ['Loan Required:', formData.loanRequired ? 'Yes' : 'No']\n      ]\n\n      bookingDetails.forEach(([label, value]) => {\n        pdf.text(label || '', margin, yPosition)\n        pdf.text(value || '', margin + 60, yPosition)\n        yPosition += 12\n      })\n\n      if (formData.nomineeName) {\n        yPosition += 10\n        pdf.setFontSize(14)\n        pdf.setFont('helvetica', 'bold')\n        pdf.text('NOMINEE DETAILS', margin, yPosition)\n        yPosition += 15\n\n        pdf.setFontSize(10)\n        pdf.setFont('helvetica', 'normal')\n        \n        const nomineeDetails = [\n          ['Nominee Name:', formData.nomineeName],\n          ['Relation:', formData.nomineeRelation || ''],\n          ['Phone:', formData.nomineePhone || '']\n        ]\n\n        nomineeDetails.forEach(([label, value]) => {\n          if (value) {\n            pdf.text(label || '', margin, yPosition)\n            pdf.text(value || '', margin + 60, yPosition)\n            yPosition += 12\n          }\n        })\n      }\n\n      // Terms and Conditions\n      yPosition += 20\n      pdf.setFontSize(12)\n      pdf.setFont('helvetica', 'bold')\n      pdf.text('TERMS & CONDITIONS', margin, yPosition)\n      yPosition += 15\n\n      pdf.setFontSize(9)\n      pdf.setFont('helvetica', 'normal')\n      \n      const terms = [\n        '1. This booking amount is adjustable against the total property cost.',\n        '2. Booking amount is refundable within 15 days of booking (cooling period).',\n        '3. Final allotment subject to availability and completion of documentation.',\n        '4. All payments should be made through proper banking channels.',\n        '5. Property registration charges and stamp duty are additional.',\n        '6. Possession will be given as per the construction timeline.',\n        '7. This receipt is computer generated and does not require signature.'\n      ]\n\n      terms.forEach(term => {\n        const lines = pdf.splitTextToSize(term, pageWidth - 2 * margin)\n        pdf.text(lines, margin, yPosition)\n        yPosition += lines.length * 8\n      })\n\n      // Footer\n      yPosition += 20\n      pdf.setFontSize(10)\n      pdf.setFont('helvetica', 'italic')\n      pdf.text('Thank you for choosing our property. For any queries, please contact our sales team.', \n               pageWidth / 2, yPosition, { align: 'center' })\n\n      // Save PDF\n      pdf.save(`Booking_Receipt_${bookingId}.pdf`)\n      \n      toast.success('Booking receipt downloaded successfully!')\n      \n      if (onBookingComplete) {\n        onBookingComplete(formData)\n      }\n      \n    } catch (error) {\n      console.error('Error generating PDF:', error)\n      toast.error('Failed to generate booking receipt')\n    } finally {\n      setIsGeneratingPDF(false)\n    }\n  }\n\n  const handleSubmit = async () => {\n    // Validate required fields\n    if (!formData.customerName || !formData.customerEmail || !formData.customerPhone) {\n      toast.error('Please fill all required customer details')\n      return\n    }\n\n    if (!formData.bookingAmount || formData.bookingAmount <= 0) {\n      toast.error('Please enter a valid booking amount')\n      return\n    }\n\n    await generateBookingPDF()\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Property Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Building className=\"h-5 w-5\" />\n            Property Summary\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <p className=\"font-semibold\">{propertyData.name}</p>\n              <p className=\"text-sm text-gray-600\">{propertyData.location.address}</p>\n              <p className=\"text-sm text-gray-600\">{propertyData.location.city}, {propertyData.location.state}</p>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between\">\n                <span>Price per Stock:</span>\n                <span className=\"font-semibold\">₹{propertyData.pricePerStock.toLocaleString('en-IN')}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Expected Returns:</span>\n                <span className=\"font-semibold\">{propertyData.expectedReturns}% p.a.</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span>Maturity Period:</span>\n                <span className=\"font-semibold\">{propertyData.maturityPeriodMonths} months</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Booking ID Display */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"text-center\">\n            <Badge variant=\"outline\" className=\"text-lg px-4 py-2\">\n              Booking ID: {bookingId}\n            </Badge>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Customer Details */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <User className=\"h-5 w-5\" />\n            Customer Details\n          </CardTitle>\n          <CardDescription>\n            Please provide accurate customer information for booking\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"customerName\">Full Name *</Label>\n              <Input\n                id=\"customerName\"\n                value={formData.customerName}\n                onChange={(e) => handleInputChange('customerName', e.target.value)}\n                placeholder=\"Enter customer full name\"\n                required\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"customerEmail\">Email Address *</Label>\n              <Input\n                id=\"customerEmail\"\n                type=\"email\"\n                value={formData.customerEmail}\n                onChange={(e) => handleInputChange('customerEmail', e.target.value)}\n                placeholder=\"<EMAIL>\"\n                required\n              />\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"customerPhone\">Phone Number *</Label>\n              <Input\n                id=\"customerPhone\"\n                value={formData.customerPhone}\n                onChange={(e) => handleInputChange('customerPhone', e.target.value)}\n                placeholder=\"+91 9876543210\"\n                required\n              />\n            </div>\n            <div>\n              <Label htmlFor=\"customerAddress\">Address</Label>\n              <Input\n                id=\"customerAddress\"\n                value={formData.customerAddress}\n                onChange={(e) => handleInputChange('customerAddress', e.target.value)}\n                placeholder=\"Complete address\"\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Property Preferences */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Building className=\"h-5 w-5\" />\n            Property Preferences\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <Label htmlFor=\"floorPreference\">Floor Preference</Label>\n              <Select value={formData.floorPreference} onValueChange={(value) => handleInputChange('floorPreference', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select floor\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"ground\">Ground Floor</SelectItem>\n                  <SelectItem value=\"1-3\">1st to 3rd Floor</SelectItem>\n                  <SelectItem value=\"4-7\">4th to 7th Floor</SelectItem>\n                  <SelectItem value=\"8-12\">8th to 12th Floor</SelectItem>\n                  <SelectItem value=\"above-12\">Above 12th Floor</SelectItem>\n                  <SelectItem value=\"no-preference\">No Preference</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"unitType\">Unit Type</Label>\n              <Select value={formData.unitType} onValueChange={(value) => handleInputChange('unitType', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select unit type\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"1bhk\">1 BHK</SelectItem>\n                  <SelectItem value=\"2bhk\">2 BHK</SelectItem>\n                  <SelectItem value=\"3bhk\">3 BHK</SelectItem>\n                  <SelectItem value=\"4bhk\">4 BHK</SelectItem>\n                  <SelectItem value=\"penthouse\">Penthouse</SelectItem>\n                  <SelectItem value=\"studio\">Studio</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"facingPreference\">Facing Preference</Label>\n              <Select value={formData.facingPreference} onValueChange={(value) => handleInputChange('facingPreference', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select facing\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"north\">North Facing</SelectItem>\n                  <SelectItem value=\"south\">South Facing</SelectItem>\n                  <SelectItem value=\"east\">East Facing</SelectItem>\n                  <SelectItem value=\"west\">West Facing</SelectItem>\n                  <SelectItem value=\"north-east\">North-East</SelectItem>\n                  <SelectItem value=\"south-east\">South-East</SelectItem>\n                  <SelectItem value=\"north-west\">North-West</SelectItem>\n                  <SelectItem value=\"south-west\">South-West</SelectItem>\n                  <SelectItem value=\"no-preference\">No Preference</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Booking Amount & Payment */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CreditCard className=\"h-5 w-5\" />\n            Booking Amount & Payment\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label htmlFor=\"bookingAmount\">Booking Amount (₹) *</Label>\n              <Input\n                id=\"bookingAmount\"\n                type=\"number\"\n                value={formData.bookingAmount}\n                onChange={(e) => handleInputChange('bookingAmount', parseInt(e.target.value) || 0)}\n                placeholder=\"Enter booking amount\"\n                min=\"1000\"\n                required\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                Suggested: ₹{Math.round(propertyData.pricePerStock * 0.1).toLocaleString('en-IN')} (10% of stock price)\n              </p>\n            </div>\n\n            <div>\n              <Label htmlFor=\"paymentMode\">Payment Mode *</Label>\n              <Select value={formData.paymentMode} onValueChange={(value) => handleInputChange('paymentMode', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select payment mode\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"online\">Online Transfer</SelectItem>\n                  <SelectItem value=\"cheque\">Cheque</SelectItem>\n                  <SelectItem value=\"cash\">Cash</SelectItem>\n                  <SelectItem value=\"bank_transfer\">Bank Transfer</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Conditional Payment Details */}\n          {formData.paymentMode === 'cheque' && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"chequeNumber\">Cheque Number</Label>\n                <Input\n                  id=\"chequeNumber\"\n                  value={formData.chequeNumber || ''}\n                  onChange={(e) => handleInputChange('chequeNumber', e.target.value)}\n                  placeholder=\"Enter cheque number\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"bankName\">Bank Name</Label>\n                <Input\n                  id=\"bankName\"\n                  value={formData.bankName || ''}\n                  onChange={(e) => handleInputChange('bankName', e.target.value)}\n                  placeholder=\"Enter bank name\"\n                />\n              </div>\n            </div>\n          )}\n\n          {(formData.paymentMode === 'online' || formData.paymentMode === 'bank_transfer') && (\n            <div>\n              <Label htmlFor=\"transactionId\">Transaction ID / Reference Number</Label>\n              <Input\n                id=\"transactionId\"\n                value={formData.transactionId || ''}\n                onChange={(e) => handleInputChange('transactionId', e.target.value)}\n                placeholder=\"Enter transaction ID or reference number\"\n              />\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Financial Details */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IndianRupee className=\"h-5 w-5\" />\n            Financial Details\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox\n              id=\"loanRequired\"\n              checked={formData.loanRequired}\n              onCheckedChange={(checked) => handleInputChange('loanRequired', checked === true)}\n            />\n            <Label htmlFor=\"loanRequired\">Loan Required</Label>\n          </div>\n\n          {formData.loanRequired && (\n            <div>\n              <Label htmlFor=\"monthlyIncome\">Monthly Income (₹)</Label>\n              <Input\n                id=\"monthlyIncome\"\n                type=\"number\"\n                value={formData.monthlyIncome || ''}\n                onChange={(e) => handleInputChange('monthlyIncome', parseInt(e.target.value) || 0)}\n                placeholder=\"Enter monthly income\"\n              />\n              <p className=\"text-sm text-gray-500 mt-1\">\n                This helps us suggest suitable loan options\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Nominee Details */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <User className=\"h-5 w-5\" />\n            Nominee Details (Optional)\n          </CardTitle>\n          <CardDescription>\n            Nominee information for legal purposes\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <Label htmlFor=\"nomineeName\">Nominee Name</Label>\n              <Input\n                id=\"nomineeName\"\n                value={formData.nomineeName || ''}\n                onChange={(e) => handleInputChange('nomineeName', e.target.value)}\n                placeholder=\"Enter nominee name\"\n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"nomineeRelation\">Relation</Label>\n              <Select value={formData.nomineeRelation || ''} onValueChange={(value) => handleInputChange('nomineeRelation', value)}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select relation\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"spouse\">Spouse</SelectItem>\n                  <SelectItem value=\"father\">Father</SelectItem>\n                  <SelectItem value=\"mother\">Mother</SelectItem>\n                  <SelectItem value=\"son\">Son</SelectItem>\n                  <SelectItem value=\"daughter\">Daughter</SelectItem>\n                  <SelectItem value=\"brother\">Brother</SelectItem>\n                  <SelectItem value=\"sister\">Sister</SelectItem>\n                  <SelectItem value=\"other\">Other</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"nomineePhone\">Nominee Phone</Label>\n              <Input\n                id=\"nomineePhone\"\n                value={formData.nomineePhone || ''}\n                onChange={(e) => handleInputChange('nomineePhone', e.target.value)}\n                placeholder=\"Enter nominee phone\"\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Special Requests */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Special Requests\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div>\n            <Label htmlFor=\"specialRequests\">Additional Requirements or Comments</Label>\n            <Textarea\n              id=\"specialRequests\"\n              value={formData.specialRequests || ''}\n              onChange={(e) => handleInputChange('specialRequests', e.target.value)}\n              placeholder=\"Any special requests, requirements, or comments...\"\n              rows={4}\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generate Booking Receipt */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5\" />\n            Generate Booking Receipt\n          </CardTitle>\n          <CardDescription>\n            Review all details and generate your booking receipt\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {/* Summary */}\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h4 className=\"font-semibold mb-2\">Booking Summary</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm\">\n                <div>Customer: {formData.customerName || 'Not provided'}</div>\n                <div>Phone: {formData.customerPhone || 'Not provided'}</div>\n                <div>Email: {formData.customerEmail || 'Not provided'}</div>\n                <div>Booking Amount: ₹{formData.bookingAmount.toLocaleString('en-IN')}</div>\n                <div>Payment Mode: {formData.paymentMode.toUpperCase()}</div>\n                <div>Booking ID: {bookingId}</div>\n              </div>\n            </div>\n\n            <Button\n              onClick={handleSubmit}\n              disabled={isGeneratingPDF}\n              className=\"w-full bg-green-600 hover:bg-green-700 text-white\"\n              size=\"lg\"\n            >\n              {isGeneratingPDF ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Generating Receipt...\n                </>\n              ) : (\n                <>\n                  <Download className=\"h-4 w-4 mr-2\" />\n                  Generate & Download Booking Receipt\n                </>\n              )}\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AArBA;;;;;;;;;;;;;;AAyEe,SAAS,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAoB;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,cAAc;QACd,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe,KAAK,KAAK,CAAC,aAAa,aAAa,GAAG;QACvD,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,kBAAkB;QAClB,cAAc;QACd,iBAAiB;IACnB;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,oBAAoB;YACxB,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;YACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YACvD,OAAO,CAAC,EAAE,EAAE,YAAY,QAAQ,CAAC,WAAW;QAC9C;QACA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,qBAAqB;QACzB,mBAAmB;QAEnB,IAAI;YACF,iCAAiC;YACjC,MAAM,MAAM,IAAI,qMAAA,CAAA,QAAK;YACrB,MAAM,YAAY,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK;YAC7C,MAAM,SAAS;YACf,IAAI,YAAY;YAEhB,SAAS;YACT,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,4BAA4B,YAAY,GAAG,WAAW;gBAAE,OAAO;YAAS;YAEjF,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,GAAG,WAAW;gBAAE,OAAO;YAAS;YAEjF,aAAa;YACb,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,OAAO,kBAAkB,CAAC,UAAU,EAAE,YAAY,GAAG,WAAW;gBAAE,OAAO;YAAS;YAExG,aAAa;YAEb,2BAA2B;YAC3B,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,oBAAoB,QAAQ;YACrC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YAEzB,MAAM,kBAAkB;gBACtB;oBAAC;oBAAkB,aAAa,IAAI;iBAAC;gBACrC;oBAAC;oBAAkB,aAAa,YAAY;iBAAC;gBAC7C;oBAAC;oBAAa,GAAG,aAAa,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,IAAI,EAAE;iBAAC;gBAChF;oBAAC;oBAAe,GAAG,aAAa,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,aAAa,QAAQ,CAAC,OAAO,EAAE;iBAAC;gBACnH;oBAAC;oBAAoB,CAAC,CAAC,EAAE,aAAa,aAAa,CAAC,cAAc,CAAC,UAAU;iBAAC;gBAC9E;oBAAC;oBAA2B,aAAa,WAAW,CAAC,QAAQ;iBAAG;gBAChE;oBAAC;oBAAqB,GAAG,aAAa,eAAe,CAAC,WAAW,CAAC;iBAAC;gBACnE;oBAAC;oBAAoB,GAAG,aAAa,oBAAoB,CAAC,OAAO,CAAC;iBAAC;aACpE;YAED,gBAAgB,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBACrC,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;gBAC9B,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI;gBACnC,aAAa;YACf;YAEA,aAAa;YAEb,2BAA2B;YAC3B,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,oBAAoB,QAAQ;YACrC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YAEzB,MAAM,kBAAkB;gBACtB;oBAAC;oBAAS,SAAS,YAAY;iBAAC;gBAChC;oBAAC;oBAAU,SAAS,aAAa;iBAAC;gBAClC;oBAAC;oBAAU,SAAS,aAAa;iBAAC;gBAClC;oBAAC;oBAAY,SAAS,eAAe;iBAAC;gBACtC;oBAAC;oBAAqB,SAAS,eAAe,IAAI;iBAAgB;gBAClE;oBAAC;oBAAc,SAAS,QAAQ,IAAI;iBAAW;gBAC/C;oBAAC;oBAAsB,SAAS,gBAAgB,IAAI;iBAAgB;aACrE;YAED,gBAAgB,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBACrC,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;gBAC9B,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI;gBACnC,aAAa;YACf;YAEA,aAAa;YAEb,0BAA0B;YAC1B,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,mBAAmB,QAAQ;YACpC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YAEzB,MAAM,iBAAiB;gBACrB;oBAAC;oBAAmB,CAAC,CAAC,EAAE,SAAS,aAAa,CAAC,cAAc,CAAC,UAAU;iBAAC;gBACzE;oBAAC;oBAAiB,SAAS,WAAW,CAAC,WAAW;iBAAG;mBACjD,SAAS,YAAY,GAAG;oBAAC;wBAAC;wBAAkB,SAAS,YAAY;qBAAC;iBAAC,GAAG,EAAE;mBACxE,SAAS,QAAQ,GAAG;oBAAC;wBAAC;wBAAc,SAAS,QAAQ;qBAAC;iBAAC,GAAG,EAAE;mBAC5D,SAAS,aAAa,GAAG;oBAAC;wBAAC;wBAAmB,SAAS,aAAa;qBAAC;iBAAC,GAAG,EAAE;gBAC/E;oBAAC;oBAAkB,SAAS,YAAY,GAAG,QAAQ;iBAAK;aACzD;YAED,eAAe,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBACpC,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;gBAC9B,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI;gBACnC,aAAa;YACf;YAEA,IAAI,SAAS,WAAW,EAAE;gBACxB,aAAa;gBACb,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,aAAa;gBACzB,IAAI,IAAI,CAAC,mBAAmB,QAAQ;gBACpC,aAAa;gBAEb,IAAI,WAAW,CAAC;gBAChB,IAAI,OAAO,CAAC,aAAa;gBAEzB,MAAM,iBAAiB;oBACrB;wBAAC;wBAAiB,SAAS,WAAW;qBAAC;oBACvC;wBAAC;wBAAa,SAAS,eAAe,IAAI;qBAAG;oBAC7C;wBAAC;wBAAU,SAAS,YAAY,IAAI;qBAAG;iBACxC;gBAED,eAAe,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;oBACpC,IAAI,OAAO;wBACT,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ;wBAC9B,IAAI,IAAI,CAAC,SAAS,IAAI,SAAS,IAAI;wBACnC,aAAa;oBACf;gBACF;YACF;YAEA,uBAAuB;YACvB,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,sBAAsB,QAAQ;YACvC,aAAa;YAEb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YAEzB,MAAM,QAAQ;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,OAAO,CAAC,CAAA;gBACZ,MAAM,QAAQ,IAAI,eAAe,CAAC,MAAM,YAAY,IAAI;gBACxD,IAAI,IAAI,CAAC,OAAO,QAAQ;gBACxB,aAAa,MAAM,MAAM,GAAG;YAC9B;YAEA,SAAS;YACT,aAAa;YACb,IAAI,WAAW,CAAC;YAChB,IAAI,OAAO,CAAC,aAAa;YACzB,IAAI,IAAI,CAAC,wFACA,YAAY,GAAG,WAAW;gBAAE,OAAO;YAAS;YAErD,WAAW;YACX,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE,UAAU,IAAI,CAAC;YAE3C,wQAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,IAAI,mBAAmB;gBACrB,kBAAkB;YACpB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,eAAe;QACnB,2BAA2B;QAC3B,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,aAAa,EAAE;YAChF,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,aAAa,IAAI,SAAS,aAAa,IAAI,GAAG;YAC1D,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM;IACR;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,6WAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;;sDACC,6WAAC;4CAAE,WAAU;sDAAiB,aAAa,IAAI;;;;;;sDAC/C,6WAAC;4CAAE,WAAU;sDAAyB,aAAa,QAAQ,CAAC,OAAO;;;;;;sDACnE,6WAAC;4CAAE,WAAU;;gDAAyB,aAAa,QAAQ,CAAC,IAAI;gDAAC;gDAAG,aAAa,QAAQ,CAAC,KAAK;;;;;;;;;;;;;8CAEjG,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAK;;;;;;8DACN,6WAAC;oDAAK,WAAU;;wDAAgB;wDAAE,aAAa,aAAa,CAAC,cAAc,CAAC;;;;;;;;;;;;;sDAE9E,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAK;;;;;;8DACN,6WAAC;oDAAK,WAAU;;wDAAiB,aAAa,eAAe;wDAAC;;;;;;;;;;;;;sDAEhE,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;8DAAK;;;;;;8DACN,6WAAC;oDAAK,WAAU;;wDAAiB,aAAa,oBAAoB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7E,6WAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,6WAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAAoB;gCACxC;;;;;;;;;;;;;;;;;;;;;;0BAOrB,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;;0CACT,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG9B,6WAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAGZ,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;gDACZ,QAAQ;;;;;;;;;;;;;;;;;;0CAKd,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAClE,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAGZ,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAkB;;;;;;0DACjC,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtB,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6WAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,eAAe;4CAAE,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;;8DACtG,6WAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,6WAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6WAAC,kIAAA,CAAA,gBAAa;;sEACZ,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAKxC,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,6WAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,QAAQ;4CAAE,eAAe,CAAC,QAAU,kBAAkB,YAAY;;8DACxF,6WAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,6WAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6WAAC,kIAAA,CAAA,gBAAa;;sEACZ,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;8CAKjC,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,6WAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,gBAAgB;4CAAE,eAAe,CAAC,QAAU,kBAAkB,oBAAoB;;8DACxG,6WAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,6WAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6WAAC,kIAAA,CAAA,gBAAa;;sEACZ,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAa;;;;;;sEAC/B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9C,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,sSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAItC,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAgB;;;;;;0DAC/B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,aAAa;gDAC7B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAChF,aAAY;gDACZ,KAAI;gDACJ,QAAQ;;;;;;0DAEV,6WAAC;gDAAE,WAAU;;oDAA6B;oDAC3B,KAAK,KAAK,CAAC,aAAa,aAAa,GAAG,KAAK,cAAc,CAAC;oDAAS;;;;;;;;;;;;;kDAItF,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6WAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO,SAAS,WAAW;gDAAE,eAAe,CAAC,QAAU,kBAAkB,eAAe;;kEAC9F,6WAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,6WAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6WAAC,kIAAA,CAAA,gBAAa;;0EACZ,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;0EACzB,6WAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOzC,SAAS,WAAW,KAAK,0BACxB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,YAAY,IAAI;gDAChC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;;;;;;;;;;;;kDAGhB,6WAAC;;0DACC,6WAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6WAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,QAAQ,IAAI;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC7D,aAAY;;;;;;;;;;;;;;;;;;4BAMnB,CAAC,SAAS,WAAW,KAAK,YAAY,SAAS,WAAW,KAAK,eAAe,mBAC7E,6WAAC;;kDACC,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;kDAC/B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO,SAAS,aAAa,IAAI;wCACjC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wCAClE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAQtB,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,wSAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,SAAS,SAAS,YAAY;wCAC9B,iBAAiB,CAAC,UAAY,kBAAkB,gBAAgB,YAAY;;;;;;kDAE9E,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;;;;;;;4BAG/B,SAAS,YAAY,kBACpB,6WAAC;;kDACC,6WAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;kDAC/B,6WAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,aAAa,IAAI;wCACjC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAChF,aAAY;;;;;;kDAEd,6WAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;;0CACT,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG9B,6WAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAc;;;;;;sDAC7B,6WAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,WAAW,IAAI;4CAC/B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAChE,aAAY;;;;;;;;;;;;8CAIhB,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6WAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,SAAS,eAAe,IAAI;4CAAI,eAAe,CAAC,QAAU,kBAAkB,mBAAmB;;8DAC5G,6WAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,6WAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6WAAC,kIAAA,CAAA,gBAAa;;sEACZ,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;sEAC7B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAU;;;;;;sEAC5B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6WAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,6WAAC;;sDACC,6WAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAe;;;;;;sDAC9B,6WAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,YAAY,IAAI;4CAChC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CACjE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtB,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6WAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6WAAC,kSAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,6WAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;;8CACC,6WAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,6WAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,SAAS,eAAe,IAAI;oCACnC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCACpE,aAAY;oCACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6WAAC,gIAAA,CAAA,OAAI;;kCACH,6WAAC,gIAAA,CAAA,aAAU;;0CACT,6WAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6WAAC,+SAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,6WAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6WAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6WAAC;4CAAI,WAAU;;8DACb,6WAAC;;wDAAI;wDAAW,SAAS,YAAY,IAAI;;;;;;;8DACzC,6WAAC;;wDAAI;wDAAQ,SAAS,aAAa,IAAI;;;;;;;8DACvC,6WAAC;;wDAAI;wDAAQ,SAAS,aAAa,IAAI;;;;;;;8DACvC,6WAAC;;wDAAI;wDAAkB,SAAS,aAAa,CAAC,cAAc,CAAC;;;;;;;8DAC7D,6WAAC;;wDAAI;wDAAe,SAAS,WAAW,CAAC,WAAW;;;;;;;8DACpD,6WAAC;;wDAAI;wDAAa;;;;;;;;;;;;;;;;;;;8CAItB,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;oCACV,MAAK;8CAEJ,gCACC;;0DACE,6WAAC;gDAAI,WAAU;;;;;;4CAAuE;;qEAIxF;;0DACE,6WAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD", "debugId": null}}]}