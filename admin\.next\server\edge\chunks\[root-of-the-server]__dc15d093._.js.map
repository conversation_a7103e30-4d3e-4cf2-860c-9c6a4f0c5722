{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\n// Route configurations\nconst publicRoutes = [\n  '/login',\n  '/api',\n  '/favicon.ico',\n  '/_next',\n  '/images',\n  '/icons'\n]\n\nconst protectedRoutes = [\n  '/dashboard',\n  '/users',\n  '/properties',\n  '/analytics',\n  '/kyc',\n  '/transactions',\n  '/settings',\n  '/reports'\n]\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  console.log(`🔍 Admin Middleware: ${pathname}`)\n\n  // Helper function to check if route is public\n  const isPublicRoute = (path: string) => {\n    return publicRoutes.some(route => path.startsWith(route))\n  }\n\n  // Helper function to check if route is protected\n  const isProtectedRoute = (path: string) => {\n    return protectedRoutes.some(route => path.startsWith(route)) || path === '/'\n  }\n\n  // Allow public routes\n  if (isPublicRoute(pathname)) {\n    console.log(`✅ Public route allowed: ${pathname}`)\n    return NextResponse.next()\n  }\n\n  // Check for authentication token - FIXED: Better cookie detection for production\n  const accessToken = request.cookies.get('accessToken')?.value ||\n                     request.cookies.get('token')?.value\n\n  console.log(`🔍 Cookie check for ${pathname}:`, {\n    hasAccessToken: !!accessToken,\n    accessTokenLength: accessToken?.length || 0,\n    userAgent: request.headers.get('user-agent')?.includes('Mozilla') ? 'browser' : 'other'\n  })\n\n  // Protected routes require authentication\n  if (isProtectedRoute(pathname)) {\n    if (!accessToken) {\n      console.log(`🔒 No token found, redirecting to login from: ${pathname}`)\n      const loginUrl = new URL('/login', request.url)\n      loginUrl.searchParams.set('redirect', pathname)\n      return NextResponse.redirect(loginUrl)\n    }\n\n    console.log(`✅ Token found, allowing access to: ${pathname}`)\n    return NextResponse.next()\n  }\n\n  // Default: allow the request\n  return NextResponse.next()\n}\n\n// Configure which routes this middleware runs on\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n\n// export const config = {\n//   matcher: [\n//     /*\n//      * Match all request paths except for the ones starting with:\n//      * - api (API routes)\n//      * - _next/static (static files)\n//      * - _next/image (image optimization files)\n//      * - favicon.ico (favicon file)\n//      * - public folder\n//      */\n//     '/((?!api|_next/static|_next/image|favicon.ico|public).*)',\n//   ],\n// }\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU;IAE9C,8CAA8C;IAC9C,MAAM,gBAAgB,CAAC;QACrB,OAAO,aAAa,IAAI,CAAC,CAAA,QAAS,KAAK,UAAU,CAAC;IACpD;IAEA,iDAAiD;IACjD,MAAM,mBAAmB,CAAC;QACxB,OAAO,gBAAgB,IAAI,CAAC,CAAA,QAAS,KAAK,UAAU,CAAC,WAAW,SAAS;IAC3E;IAEA,sBAAsB;IACtB,IAAI,cAAc,WAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,UAAU;QACjD,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,iFAAiF;IACjF,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB,SACrC,QAAQ,OAAO,CAAC,GAAG,CAAC,UAAU;IAEjD,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9C,gBAAgB,CAAC,CAAC;QAClB,mBAAmB,aAAa,UAAU;QAC1C,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SAAS,aAAa,YAAY;IAClF;IAEA,0CAA0C;IAC1C,IAAI,iBAAiB,WAAW;QAC9B,IAAI,CAAC,aAAa;YAChB,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,UAAU;YACvE,MAAM,WAAW,IAAI,IAAI,UAAU,QAAQ,GAAG;YAC9C,SAAS,YAAY,CAAC,GAAG,CAAC,YAAY;YACtC,OAAO,4TAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU;QAC5D,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,6BAA6B;IAC7B,OAAO,4TAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH,EAEA,0BAA0B;CAC1B,eAAe;CACf,SAAS;CACT,oEAAoE;CACpE,4BAA4B;CAC5B,uCAAuC;CACvC,kDAAkD;CAClD,sCAAsC;CACtC,yBAAyB;CACzB,UAAU;CACV,kEAAkE;CAClE,OAAO;CACP,IAAI"}}]}