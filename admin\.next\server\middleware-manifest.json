{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_abc1480a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_7ee683ed.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KCqBHLbp4ou5mngrNx0X5OW7XXQOwYB64JNs4QzatE4=", "__NEXT_PREVIEW_MODE_ID": "38150dd79a8a4b490e69bb5e5f63033c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d7d9364179bdd6d401ba5bf648c51bb4094aac9ce1eba6b76c90bfc13eae6379", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f47fd26b9a5797f97033566a0d86193235ee8ea224711da10dc73e636899c1e1"}}}, "sortedMiddleware": ["/"], "functions": {}}