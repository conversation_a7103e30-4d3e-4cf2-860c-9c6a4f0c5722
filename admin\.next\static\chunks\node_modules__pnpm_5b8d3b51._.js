(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/canvg@3.0.11/node_modules/canvg/lib/index.es.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules__pnpm_c03e8470._.js",
  "static/chunks/656c1_canvg_lib_index_es_62379e22.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/canvg@3.0.11/node_modules/canvg/lib/index.es.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/html2canvas@1.4.1/node_modules/html2canvas/dist/html2canvas.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/1ebf7_html2canvas_dist_html2canvas_b038fb80.js",
  "static/chunks/1ebf7_html2canvas_dist_html2canvas_62379e22.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/html2canvas@1.4.1/node_modules/html2canvas/dist/html2canvas.js [app-client] (ecmascript)");
    });
});
}}),
"[project]/node_modules/.pnpm/dompurify@3.2.6/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/61f2f_dompurify_dist_purify_es_mjs_6450e66d._.js",
  "static/chunks/61f2f_dompurify_dist_purify_es_mjs_62379e22._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/dompurify@3.2.6/node_modules/dompurify/dist/purify.es.mjs [app-client] (ecmascript)");
    });
});
}}),
}]);