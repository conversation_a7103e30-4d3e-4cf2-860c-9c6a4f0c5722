{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  })}`\n}\n\n// Format percentage\nexport function formatPercentage(value: number | undefined | null): string {\n  if (value === undefined || value === null || isNaN(value)) {\n    return '0.00%'\n  }\n  return `${value.toFixed(2)}%`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,iBAAiB,KAAgC;IAC/D,IAAI,UAAU,aAAa,UAAU,QAAQ,MAAM,QAAQ;QACzD,OAAO;IACT;IACA,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAA0B;QAE7D,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACvD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;QACvD;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;QAC1D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAChD;IACF;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,iRAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-white\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8QACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,iRAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,4TAAC,2RAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,4TAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,4TAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,4TAAC;;;;;0BACD,4TAAC;;;;;;;;;;;AAGP;KAvEM;uCAyES", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard,\n  Heart\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"user-wishlists\",\n          label: \"User Wishlists\",\n          icon: Heart,\n          href: \"/wishlists\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Owners & Developers\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-owner\",\n          label: \"Add Owner/Developer\",\n          icon: UserPlus,\n          href: \"/properties/owners/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAyCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;;IAC3E,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;wCAAE,CAAC,QAAU,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kTAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,wHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,mIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;0CAAE;;;;;;0CACH,4TAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,4TAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,4TAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,4TAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,4TAAC,8RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,4TAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,4TAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,4TAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,4TAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE;GArWwB;;QACL,oQAAA,CAAA,cAAW;QACf,wHAAA,CAAA,iBAAc;;;KAFL", "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC,0IAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAO,WAAU;sCAChB,cAAA,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,4TAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,4TAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,4TAAC;4CAAI,WAAU;;8DAEb,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,4TAAC,qTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,4TAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAI,WAAU;sFACb,cAAA,4TAAC;gFAAI,WAAU;0FACb,cAAA,4TAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,yRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,iSAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,4TAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,4TAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;GArJwB;;QAGP,oQAAA,CAAA,YAAS;QACP,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;;;KALL", "debugId": null}}, {"offset": {"line": 2020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/owners/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectIsAuthenticated, selectUser } from '@/store/slices/authSlice'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport {\n  useGetPropertyOwnersQuery,\n  useDeletePropertyOwnerMutation,\n  useVerifyPropertyOwnerMutation\n} from '@/store/api/propertyOwnersApi'\nimport { UserRole } from '@/types'\nimport { \n  Users,\n  Search,\n  Filter,\n  Download,\n  Upload,\n  Eye,\n  Edit,\n  MapPin,\n  TrendingUp,\n  CheckCircle,\n  Plus,\n  Building,\n  User,\n  DollarSign,\n  Calendar,\n  Trash2,\n  Star,\n  Award,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight,\n  Shield,\n  AlertCircle,\n  Phone,\n  Mail,\n  FileText,\n  Briefcase\n} from 'lucide-react'\n\nexport default function PropertyOwnersPage() {\n  const router = useRouter()\n  const isAuthenticated = useAppSelector(selectIsAuthenticated)\n  const currentUser = useAppSelector(selectUser)\n\n  // State management\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedFilter, setSelectedFilter] = useState('all')\n  const [selectedOwners, setSelectedOwners] = useState<string[]>([])\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pageSize, setPageSize] = useState(9)\n\n  // API queries\n  const { data: ownersResponse, isLoading, refetch } = useGetPropertyOwnersQuery({\n    page: currentPage,\n    limit: pageSize,\n    ...(searchQuery && { search: searchQuery }),\n    ...(selectedFilter !== 'all' && { status: selectedFilter })\n  })\n\n  // Mutations\n  const [deleteOwner] = useDeletePropertyOwnerMutation()\n  const [verifyOwner] = useVerifyPropertyOwnerMutation()\n\n  // Authentication check\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, router])\n\n  const canManageOwners = currentUser?.role === UserRole.ADMIN || currentUser?.role === UserRole.SUBADMIN\n\n  if (!isAuthenticated || !canManageOwners) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"bg-white p-8 rounded-lg shadow-lg\">\n            <h1 className=\"text-2xl font-bold mb-4 text-gray-900\">Access Denied</h1>\n            <p className=\"text-gray-600 mb-4\">You don't have permission to access property owner management.</p>\n            <Button onClick={() => router.push('/dashboard')} className=\"bg-emerald-600 hover:bg-emerald-700\">\n              Go to Dashboard\n            </Button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // Extract data safely\n  const owners = Array.isArray(ownersResponse?.data?.data) ? ownersResponse.data.data : []\n  const pagination = ownersResponse?.data?.meta?.pagination || {\n    currentPage: 1,\n    totalPages: 1,\n    totalItems: 0,\n    itemsPerPage: pageSize\n  }\n\n  // Calculate analytics\n  const totalOwners = pagination.totalItems\n  const verifiedOwners = owners.filter(owner => owner.verificationStatus === 'verified').length\n  const pendingOwners = owners.filter(owner => owner.verificationStatus === 'pending').length\n  const totalProperties = owners.reduce((sum, owner) => sum + (owner.properties?.length || 0), 0)\n\n  // Filter owners\n  const filteredOwners = owners.filter((owner: any) => {\n    const matchesSearch = !searchQuery ||\n                         owner.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         owner.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         owner.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         owner.company?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         owner.address?.city?.toLowerCase().includes(searchQuery.toLowerCase())\n\n    const matchesFilter = selectedFilter === 'all' ||\n                         (selectedFilter === 'verified' && owner.verificationStatus === 'verified') ||\n                         (selectedFilter === 'pending' && owner.verificationStatus === 'pending') ||\n                         (selectedFilter === 'rejected' && owner.verificationStatus === 'rejected') ||\n                         (selectedFilter === 'developer' && (owner.roles?.isDeveloper || owner.isDeveloper)) ||\n                         (selectedFilter === 'owner' && (owner.roles?.isOwner || owner.isOwner))\n\n    return matchesSearch && matchesFilter\n  })\n\n  // Helper functions\n  const formatCurrency = (amount: number) => {\n    if (amount >= 10000000) {\n      return `₹${(amount / 10000000).toFixed(1)}Cr`\n    } else if (amount >= 100000) {\n      return `₹${(amount / 100000).toFixed(1)}L`\n    } else {\n      return `₹${amount.toLocaleString()}`\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'verified':\n        return <Badge className=\"bg-green-100 text-green-800 border-green-200\"><CheckCircle className=\"h-3 w-3 mr-1\" />Verified</Badge>\n      case 'pending':\n        return <Badge className=\"bg-yellow-100 text-yellow-800 border-yellow-200\"><AlertCircle className=\"h-3 w-3 mr-1\" />Pending</Badge>\n      case 'rejected':\n        return <Badge className=\"bg-red-100 text-red-800 border-red-200\"><AlertCircle className=\"h-3 w-3 mr-1\" />Rejected</Badge>\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>\n    }\n  }\n\n  const getRoleBadge = (owner: any) => {\n    const roles = []\n    if (owner.roles?.isOwner || owner.isOwner) roles.push('Owner')\n    if (owner.roles?.isDeveloper || owner.isDeveloper) roles.push('Developer')\n\n    return roles.map((role, index) => (\n      <Badge key={index} variant=\"outline\" className=\"text-xs mr-1\">\n        {role === 'Developer' && <Briefcase className=\"h-3 w-3 mr-1\" />}\n        {role === 'Owner' && <Building className=\"h-3 w-3 mr-1\" />}\n        {role}\n      </Badge>\n    ))\n  }\n\n  const handleOwnerSelect = (ownerId: string) => {\n    setSelectedOwners(prev => \n      prev.includes(ownerId) \n        ? prev.filter(id => id !== ownerId)\n        : [...prev, ownerId]\n    )\n  }\n\n  const handleSelectAllOwners = (checked: boolean) => {\n    if (checked) {\n      setSelectedOwners(filteredOwners.map(o => o._id))\n    } else {\n      setSelectedOwners([])\n    }\n  }\n\n  const handleDeleteOwner = async (ownerId: string) => {\n    if (!confirm('Are you sure you want to delete this property owner? This action cannot be undone.')) return\n    try {\n      await deleteOwner(ownerId).unwrap()\n      toast.success('Property owner deleted successfully!')\n      refetch()\n    } catch (error) {\n      toast.error('Failed to delete property owner')\n      console.error('Delete owner error:', error)\n    }\n  }\n\n  const handleVerifyOwner = async (ownerId: string) => {\n    try {\n      await verifyOwner({ id: ownerId, status: 'verified' }).unwrap()\n      toast.success('Property owner verified successfully!')\n      refetch()\n    } catch (error) {\n      toast.error('Failed to verify property owner')\n      console.error('Verify owner error:', error)\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6 space-y-6\">\n        {/* Beautiful Header with Gradient */}\n        <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\n                <Users className=\"h-8 w-8\" />\n                Property Owners & Developers\n              </h1>\n              <p className=\"text-blue-100\">Manage property owners, developers, and their verification status</p>\n            </div>\n            \n            <div className=\"flex items-center gap-3\">\n              <Button \n                variant=\"secondary\"\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export Data\n              </Button>\n              \n              <Button \n                variant=\"secondary\"\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Import Owners\n              </Button>\n              \n              <Button \n                onClick={() => router.push('/properties/owners/add')}\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Owner/Developer\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Owner Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-blue-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Owners</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{totalOwners}</p>\n                  <p className=\"text-xs text-blue-600 mt-1 flex items-center\">\n                    <TrendingUp className=\"h-3 w-3 mr-1\" />\n                    +8% from last month\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-blue-100\">\n                  <Users className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-green-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Verified Owners</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{verifiedOwners}</p>\n                  <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                    <CheckCircle className=\"h-3 w-3 mr-1\" />\n                    Verified accounts\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-green-100\">\n                  <Shield className=\"h-6 w-6 text-green-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-yellow-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending Verification</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{pendingOwners}</p>\n                  <p className=\"text-xs text-yellow-600 mt-1 flex items-center\">\n                    <AlertCircle className=\"h-3 w-3 mr-1\" />\n                    Awaiting review\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-yellow-100\">\n                  <AlertCircle className=\"h-6 w-6 text-yellow-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-purple-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Properties</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{totalProperties}</p>\n                  <p className=\"text-xs text-purple-600 mt-1 flex items-center\">\n                    <Building className=\"h-3 w-3 mr-1\" />\n                    Managed properties\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-purple-100\">\n                  <Building className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Bulk Actions Section */}\n        {selectedOwners.length > 0 && (\n          <Card className=\"border-blue-200 bg-blue-50\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <CheckCircle className=\"h-5 w-5 text-blue-600\" />\n                  <span className=\"font-medium text-blue-800\">\n                    {selectedOwners.length} owners selected\n                  </span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"bg-blue-100 border-blue-300 text-blue-700 hover:bg-blue-200\"\n                  >\n                    <Shield className=\"h-4 w-4 mr-2\" />\n                    Bulk Verify\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setSelectedOwners([])}\n                    className=\"border-gray-300 text-gray-600 hover:bg-gray-50\"\n                  >\n                    Clear Selection\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Advanced Filters & Search */}\n        <Card className=\"border-blue-200\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center text-blue-700\">\n              <Filter className=\"h-5 w-5 mr-2\" />\n              Owner Filters & Search\n            </CardTitle>\n            <CardDescription>\n              Filter and search through property owners and developers\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6\">\n              {/* Search */}\n              <div className=\"lg:col-span-2\">\n                <Label className=\"text-sm font-medium mb-2 block\">Search Owners</Label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <Input\n                    placeholder=\"Search by name, email, company...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"pl-10 border-blue-200 focus:border-blue-500\"\n                  />\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <Label className=\"text-sm font-medium mb-2 block\">Filter by Status</Label>\n                <Select value={selectedFilter} onValueChange={setSelectedFilter}>\n                  <SelectTrigger className=\"border-blue-200 focus:border-blue-500\">\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All Owners</SelectItem>\n                    <SelectItem value=\"verified\">Verified</SelectItem>\n                    <SelectItem value=\"pending\">Pending</SelectItem>\n                    <SelectItem value=\"rejected\">Rejected</SelectItem>\n                    <SelectItem value=\"developer\">Developers</SelectItem>\n                    <SelectItem value=\"owner\">Property Owners</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Quick Actions */}\n              <div>\n                <Label className=\"text-sm font-medium mb-2 block\">Quick Actions</Label>\n                <Button\n                  className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n                  onClick={() => router.push('/properties/owners/add')}\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Owner\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Owners Grid */}\n        <Card className=\"border-blue-200\">\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle className=\"flex items-center text-blue-700\">\n                  <Users className=\"h-5 w-5 mr-2\" />\n                  Property Owners & Developers\n                </CardTitle>\n                <CardDescription>\n                  {filteredOwners.length} of {totalOwners} owners\n                </CardDescription>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Checkbox\n                  checked={selectedOwners.length === filteredOwners.length && filteredOwners.length > 0}\n                  onCheckedChange={handleSelectAllOwners}\n                />\n                <span className=\"text-sm text-gray-600\">Select All</span>\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <div className=\"flex items-center justify-center py-12\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3\"></div>\n                  <p className=\"text-gray-600\">Loading owners...</p>\n                </div>\n              </div>\n            ) : filteredOwners.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Users className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                <p className=\"text-gray-500 mb-2\">No owners found</p>\n                <p className=\"text-sm text-gray-400 mb-4\">Start by adding your first property owner or developer</p>\n                <Button\n                  onClick={() => router.push('/properties/owners/add')}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Your First Owner\n                </Button>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {filteredOwners.map((owner: any) => (\n                  <Card\n                    key={owner._id}\n                    className={`border-blue-200 hover:shadow-xl hover:border-blue-300 transition-all duration-300 transform hover:-translate-y-1 ${\n                      selectedOwners.includes(owner._id)\n                        ? 'ring-2 ring-blue-500 bg-blue-50/50 shadow-lg'\n                        : 'hover:bg-white/80'\n                    }`}\n                  >\n                    <CardContent className=\"p-6\">\n                      {/* Owner Header */}\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex items-start gap-3\">\n                          <Checkbox\n                            checked={selectedOwners.includes(owner._id)}\n                            onCheckedChange={() => handleOwnerSelect(owner._id)}\n                            className=\"mt-1\"\n                          />\n                          <div className=\"flex-1\">\n                            <h3 className=\"text-lg font-bold text-gray-900 line-clamp-1 mb-1\">\n                              {owner.firstName} {owner.lastName}\n                            </h3>\n                            <div className=\"flex items-center gap-2 mb-2\">\n                              <div className=\"flex items-center gap-1 text-sm text-gray-600\">\n                                <Mail className=\"h-3 w-3 text-blue-600\" />\n                                <span>{owner.email}</span>\n                              </div>\n                            </div>\n                            {owner.phone && (\n                              <div className=\"flex items-center gap-1 text-xs text-gray-500 mb-2\">\n                                <Phone className=\"h-3 w-3\" />\n                                <span>{owner.phone}</span>\n                              </div>\n                            )}\n                            <div className=\"flex items-center gap-2 text-xs text-gray-500\">\n                              <Calendar className=\"h-3 w-3\" />\n                              <span>Joined {new Date(owner.createdAt).toLocaleDateString()}</span>\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"flex flex-col gap-2\">\n                          {getStatusBadge(owner.verificationStatus)}\n                          {owner.company && (\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              <Briefcase className=\"h-3 w-3 mr-1\" />\n                              {owner.company}\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n\n                      {/* Role Badges */}\n                      <div className=\"flex flex-wrap gap-1 mb-4\">\n                        {getRoleBadge(owner)}\n                      </div>\n\n                      {/* Location */}\n                      {owner.address && (\n                        <div className=\"flex items-center gap-2 mb-4 text-sm text-gray-600\">\n                          <MapPin className=\"h-4 w-4 text-blue-600\" />\n                          <span>{owner.address.city}, {owner.address.state}</span>\n                        </div>\n                      )}\n\n                      {/* Properties Count */}\n                      <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 mb-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <p className=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Properties</p>\n                            <p className=\"text-xl font-bold text-blue-900\">{owner.properties?.length || 0}</p>\n                          </div>\n                          <Building className=\"h-6 w-6 text-blue-600\" />\n                        </div>\n                      </div>\n\n                      {/* Action Buttons */}\n                      <div className=\"flex items-center gap-2 pt-2\">\n                        <Button\n                          size=\"sm\"\n                          onClick={() => router.push(`/properties/owners/${owner._id}`)}\n                          className=\"flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-md hover:shadow-lg transition-all duration-200\"\n                        >\n                          <Eye className=\"h-4 w-4 mr-2\" />\n                          View Details\n                        </Button>\n                        {owner.verificationStatus === 'pending' && (\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => handleVerifyOwner(owner._id)}\n                            className=\"border-green-300 text-green-700 hover:bg-green-50 hover:border-green-400 transition-all duration-200\"\n                          >\n                            <Shield className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => router.push(`/properties/owners/${owner._id}/edit`)}\n                          className=\"border-blue-300 text-blue-700 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200\"\n                        >\n                          <Edit className=\"h-4 w-4\" />\n                        </Button>\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => handleDeleteOwner(owner._id)}\n                          className=\"border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400 transition-all duration-200\"\n                        >\n                          <Trash2 className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            )}\n\n            {/* Pagination Controls */}\n            {pagination.totalPages > 1 && (\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n                <div className=\"flex items-center gap-2\">\n                  <Label className=\"text-sm font-medium\">Show:</Label>\n                  <Select value={pageSize.toString()} onValueChange={(value) => {\n                    setPageSize(parseInt(value))\n                    setCurrentPage(1)\n                  }}>\n                    <SelectTrigger className=\"w-20 border-blue-200\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"6\">6</SelectItem>\n                      <SelectItem value=\"9\">9</SelectItem>\n                      <SelectItem value=\"12\">12</SelectItem>\n                      <SelectItem value=\"18\">18</SelectItem>\n                    </SelectContent>\n                  </Select>\n                  <span className=\"text-sm text-gray-600\">\n                    Showing {((currentPage - 1) * pageSize) + 1}-{Math.min(currentPage * pageSize, pagination.totalItems)} of {pagination.totalItems} owners\n                  </span>\n                </div>\n\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(1)}\n                    disabled={currentPage === 1}\n                    className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\n                  >\n                    <ChevronsLeft className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(currentPage - 1)}\n                    disabled={currentPage === 1}\n                    className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\n                  >\n                    <ChevronLeft className=\"h-4 w-4\" />\n                  </Button>\n\n                  <div className=\"flex items-center gap-1\">\n                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {\n                      let pageNum\n                      if (pagination.totalPages <= 5) {\n                        pageNum = i + 1\n                      } else if (currentPage <= 3) {\n                        pageNum = i + 1\n                      } else if (currentPage >= pagination.totalPages - 2) {\n                        pageNum = pagination.totalPages - 4 + i\n                      } else {\n                        pageNum = currentPage - 2 + i\n                      }\n\n                      return (\n                        <Button\n                          key={pageNum}\n                          variant={currentPage === pageNum ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setCurrentPage(pageNum)}\n                          className={currentPage === pageNum\n                            ? \"bg-blue-600 hover:bg-blue-700 text-white\"\n                            : \"border-blue-200 text-blue-600 hover:bg-blue-50\"\n                          }\n                        >\n                          {pageNum}\n                        </Button>\n                      )\n                    })}\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(currentPage + 1)}\n                    disabled={currentPage === pagination.totalPages}\n                    className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\n                  >\n                    <ChevronRight className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(pagination.totalPages)}\n                    disabled={currentPage === pagination.totalPages}\n                    className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\n                  >\n                    <ChevronsRight className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AArBA;;;;;;;;;;;;;;;;;AAoDe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,wBAAqB;IAC5D,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAE7C,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,cAAc;IACd,MAAM,EAAE,MAAM,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,4BAAyB,AAAD,EAAE;QAC7E,MAAM;QACN,OAAO;QACP,GAAI,eAAe;YAAE,QAAQ;QAAY,CAAC;QAC1C,GAAI,mBAAmB,SAAS;YAAE,QAAQ;QAAe,CAAC;IAC5D;IAEA,YAAY;IACZ,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,2IAAA,CAAA,iCAA8B,AAAD;IACnD,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,2IAAA,CAAA,iCAA8B,AAAD;IAEnD,uBAAuB;IACvB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;uCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,kBAAkB,aAAa,SAAS,wHAAA,CAAA,WAAQ,CAAC,KAAK,IAAI,aAAa,SAAS,wHAAA,CAAA,WAAQ,CAAC,QAAQ;IAEvG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;QACxC,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,4TAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,4TAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAAe,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;;;;;IAO5G;IAEA,sBAAsB;IACtB,MAAM,SAAS,MAAM,OAAO,CAAC,gBAAgB,MAAM,QAAQ,eAAe,IAAI,CAAC,IAAI,GAAG,EAAE;IACxF,MAAM,aAAa,gBAAgB,MAAM,MAAM,cAAc;QAC3D,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,cAAc;IAChB;IAEA,sBAAsB;IACtB,MAAM,cAAc,WAAW,UAAU;IACzC,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,kBAAkB,KAAK,YAAY,MAAM;IAC7F,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,kBAAkB,KAAK,WAAW,MAAM;IAC3F,MAAM,kBAAkB,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,UAAU,EAAE,UAAU,CAAC,GAAG;IAE7F,gBAAgB;IAChB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC;QACpC,MAAM,gBAAgB,CAAC,eACF,MAAM,SAAS,EAAE,cAAc,SAAS,YAAY,WAAW,OAC/D,MAAM,QAAQ,EAAE,cAAc,SAAS,YAAY,WAAW,OAC9D,MAAM,KAAK,EAAE,cAAc,SAAS,YAAY,WAAW,OAC3D,MAAM,OAAO,EAAE,cAAc,SAAS,YAAY,WAAW,OAC7D,MAAM,OAAO,EAAE,MAAM,cAAc,SAAS,YAAY,WAAW;QAExF,MAAM,gBAAgB,mBAAmB,SACnB,mBAAmB,cAAc,MAAM,kBAAkB,KAAK,cAC9D,mBAAmB,aAAa,MAAM,kBAAkB,KAAK,aAC7D,mBAAmB,cAAc,MAAM,kBAAkB,KAAK,cAC9D,mBAAmB,eAAe,CAAC,MAAM,KAAK,EAAE,eAAe,MAAM,WAAW,KAChF,mBAAmB,WAAW,CAAC,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;QAE1F,OAAO,iBAAiB;IAC1B;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,UAAU;YACtB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QAC/C,OAAO,IAAI,UAAU,QAAQ;YAC3B,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,OAAO,cAAc,IAAI;QACtC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA+C,4TAAC,kTAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACjH,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAAkD,4TAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACpH,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAAyC,4TAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAC3G;gBACE,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,EAAE;QAChB,IAAI,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO,EAAE,MAAM,IAAI,CAAC;QACtD,IAAI,MAAM,KAAK,EAAE,eAAe,MAAM,WAAW,EAAE,MAAM,IAAI,CAAC;QAE9D,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtB,4TAAC,oIAAA,CAAA,QAAK;gBAAa,SAAQ;gBAAU,WAAU;;oBAC5C,SAAS,6BAAe,4TAAC,mSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;oBAC7C,SAAS,yBAAW,4TAAC,iSAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBACxC;;eAHS;;;;;IAMhB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB,CAAA,OAChB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,WACzB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,SAAS;YACX,kBAAkB,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;QACjD,OAAO;YACL,kBAAkB,EAAE;QACtB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,uFAAuF;QACpG,IAAI;YACF,MAAM,YAAY,SAAS,MAAM;YACjC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,YAAY;gBAAE,IAAI;gBAAS,QAAQ;YAAW,GAAG,MAAM;YAC7D,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,qBACE,4TAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;;0DACZ,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG/B,4TAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAG/B,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIvC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQzC,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI3C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,kTAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI5C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM1B,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,2SAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI5C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,2SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/B,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,iSAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAIzC,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQ7B,eAAe,MAAM,GAAG,mBACvB,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,kTAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,4TAAC;4CAAK,WAAU;;gDACb,eAAe,MAAM;gDAAC;;;;;;;;;;;;;8CAG3B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,4TAAC,6RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGrC,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,kBAAkB,EAAE;4CACnC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUX,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,4TAAC,mIAAA,CAAA,aAAU;;8CACT,4TAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,4TAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,4TAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,4TAAC;gCAAI,WAAU;;kDAEb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,4TAAC;;0DACC,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAgB,eAAe;;kEAC5C,4TAAC,qIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,4TAAC,qIAAA,CAAA,gBAAa;;0EACZ,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,4TAAC;;0DACC,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC,qIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS3C,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,4TAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;;0DACC,4TAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,4TAAC,2RAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,4TAAC,mIAAA,CAAA,kBAAe;;oDACb,eAAe,MAAM;oDAAC;oDAAK;oDAAY;;;;;;;;;;;;;kDAG5C,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,uIAAA,CAAA,WAAQ;gDACP,SAAS,eAAe,MAAM,KAAK,eAAe,MAAM,IAAI,eAAe,MAAM,GAAG;gDACpF,iBAAiB;;;;;;0DAEnB,4TAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI9C,4TAAC,mIAAA,CAAA,cAAW;;gCACT,0BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;;;;;0DACf,4TAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;2CAG/B,eAAe,MAAM,KAAK,kBAC5B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,4TAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,4TAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKrC,4TAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,4TAAC,mIAAA,CAAA,OAAI;4CAEH,WAAW,CAAC,iHAAiH,EAC3H,eAAe,QAAQ,CAAC,MAAM,GAAG,IAC7B,iDACA,qBACJ;sDAEF,cAAA,4TAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,uIAAA,CAAA,WAAQ;wEACP,SAAS,eAAe,QAAQ,CAAC,MAAM,GAAG;wEAC1C,iBAAiB,IAAM,kBAAkB,MAAM,GAAG;wEAClD,WAAU;;;;;;kFAEZ,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAG,WAAU;;oFACX,MAAM,SAAS;oFAAC;oFAAE,MAAM,QAAQ;;;;;;;0FAEnC,4TAAC;gFAAI,WAAU;0FACb,cAAA,4TAAC;oFAAI,WAAU;;sGACb,4TAAC,yRAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;sGAChB,4TAAC;sGAAM,MAAM,KAAK;;;;;;;;;;;;;;;;;4EAGrB,MAAM,KAAK,kBACV,4TAAC;gFAAI,WAAU;;kGACb,4TAAC,2RAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,4TAAC;kGAAM,MAAM,KAAK;;;;;;;;;;;;0FAGtB,4TAAC;gFAAI,WAAU;;kGACb,4TAAC,iSAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;kGACpB,4TAAC;;4FAAK;4FAAQ,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0EAIhE,4TAAC;gEAAI,WAAU;;oEACZ,eAAe,MAAM,kBAAkB;oEACvC,MAAM,OAAO,kBACZ,4TAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;;0FACjC,4TAAC,mSAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EACpB,MAAM,OAAO;;;;;;;;;;;;;;;;;;;kEAOtB,4TAAC;wDAAI,WAAU;kEACZ,aAAa;;;;;;oDAIf,MAAM,OAAO,kBACZ,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,iSAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,4TAAC;;oEAAM,MAAM,OAAO,CAAC,IAAI;oEAAC;oEAAG,MAAM,OAAO,CAAC,KAAK;;;;;;;;;;;;;kEAKpD,4TAAC;wDAAI,WAAU;kEACb,cAAA,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;;sFACC,4TAAC;4EAAE,WAAU;sFAA4D;;;;;;sFACzE,4TAAC;4EAAE,WAAU;sFAAmC,MAAM,UAAU,EAAE,UAAU;;;;;;;;;;;;8EAE9E,4TAAC,iSAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAKxB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM,GAAG,EAAE;gEAC5D,WAAU;;kFAEV,4TAAC,uRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;4DAGjC,MAAM,kBAAkB,KAAK,2BAC5B,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,kBAAkB,MAAM,GAAG;gEAC1C,WAAU;0EAEV,cAAA,4TAAC,6RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAGtB,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,MAAM,GAAG,CAAC,KAAK,CAAC;gEACjE,WAAU;0EAEV,cAAA,4TAAC,kSAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,kBAAkB,MAAM,GAAG;gEAC1C,WAAU;0EAEV,cAAA,4TAAC,iSAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA3GnB,MAAM,GAAG;;;;;;;;;;gCAqHrB,WAAW,UAAU,GAAG,mBACvB,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,4TAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,QAAQ;oDAAI,eAAe,CAAC;wDAClD,YAAY,SAAS;wDACrB,eAAe;oDACjB;;sEACE,4TAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,4TAAC,qIAAA,CAAA,gBAAa;;8EACZ,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAI;;;;;;8EACtB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAI;;;;;;8EACtB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;8EACvB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;;;;;;;;;;;;;8DAG3B,4TAAC;oDAAK,WAAU;;wDAAwB;wDAC5B,CAAC,cAAc,CAAC,IAAI,WAAY;wDAAE;wDAAE,KAAK,GAAG,CAAC,cAAc,UAAU,WAAW,UAAU;wDAAE;wDAAK,WAAW,UAAU;wDAAC;;;;;;;;;;;;;sDAIrI,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe;oDAC9B,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,6SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,cAAc;oDAC5C,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,2SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAGzB,4TAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI,CAAC;wDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG,WAAW,UAAU;oDAAE,GAAG,CAAC,GAAG;wDAC9D,IAAI;wDACJ,IAAI,WAAW,UAAU,IAAI,GAAG;4DAC9B,UAAU,IAAI;wDAChB,OAAO,IAAI,eAAe,GAAG;4DAC3B,UAAU,IAAI;wDAChB,OAAO,IAAI,eAAe,WAAW,UAAU,GAAG,GAAG;4DACnD,UAAU,WAAW,UAAU,GAAG,IAAI;wDACxC,OAAO;4DACL,UAAU,cAAc,IAAI;wDAC9B;wDAEA,qBACE,4TAAC,qIAAA,CAAA,SAAM;4DAEL,SAAS,gBAAgB,UAAU,YAAY;4DAC/C,MAAK;4DACL,SAAS,IAAM,eAAe;4DAC9B,WAAW,gBAAgB,UACvB,6CACA;sEAGH;2DATI;;;;;oDAYX;;;;;;8DAGF,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,cAAc;oDAC5C,UAAU,gBAAgB,WAAW,UAAU;oDAC/C,WAAU;8DAEV,cAAA,4TAAC,6SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,WAAW,UAAU;oDACnD,UAAU,gBAAgB,WAAW,UAAU;oDAC/C,WAAU;8DAEV,cAAA,4TAAC,+SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C;GA1nBwB;;QACP,oQAAA,CAAA,YAAS;QACA,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;QAUmB,2IAAA,CAAA,4BAAyB;QAQxD,2IAAA,CAAA,iCAA8B;QAC9B,2IAAA,CAAA,iCAA8B;;;KAtB9B", "debugId": null}}]}