{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Format currency\nexport function formatCurrency(amount: number, currency = \"₹\"): string {\n  return `${currency}${amount.toLocaleString('en-IN', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  })}`\n}\n\n// Format percentage\nexport function formatPercentage(value: number | undefined | null): string {\n  if (value === undefined || value === null || isNaN(value)) {\n    return '0.00%'\n  }\n  return `${value.toFixed(2)}%`\n}\n\n// Format date\nexport function formatDate(date: string | Date, format = \"dd/MM/yyyy\"): string {\n  const d = new Date(date)\n  \n  if (format === \"dd/MM/yyyy\") {\n    return d.toLocaleDateString('en-GB')\n  }\n  \n  if (format === \"relative\") {\n    const now = new Date()\n    const diffInMs = now.getTime() - d.getTime()\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n    \n    if (diffInDays === 0) return \"Today\"\n    if (diffInDays === 1) return \"Yesterday\"\n    if (diffInDays < 7) return `${diffInDays} days ago`\n    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`\n    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`\n    return `${Math.floor(diffInDays / 365)} years ago`\n  }\n  \n  return d.toLocaleDateString()\n}\n\n// Truncate text\nexport function truncateText(text: string, length = 50): string {\n  if (text.length <= length) return text\n  return text.substring(0, length) + \"...\"\n}\n\n// Generate initials\nexport function getInitials(firstName?: string, lastName?: string, email?: string): string {\n  if (firstName && lastName) {\n    return `${firstName[0]}${lastName[0]}`.toUpperCase()\n  }\n  \n  if (firstName) {\n    return firstName.substring(0, 2).toUpperCase()\n  }\n  \n  if (email) {\n    return email.substring(0, 2).toUpperCase()\n  }\n  \n  return \"U\"\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Sleep function\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n// Generate random ID\nexport function generateId(prefix = \"\"): string {\n  const timestamp = Date.now().toString(36)\n  const random = Math.random().toString(36).substring(2, 8)\n  return `${prefix}${timestamp}${random}`.toUpperCase()\n}\n\n// Validate email\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\n// Validate phone\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\n// Format phone number\nexport function formatPhone(phone: string): string {\n  const cleaned = phone.replace(/\\D/g, '')\n  \n  if (cleaned.length === 10) {\n    return `+91 ${cleaned.substring(0, 5)} ${cleaned.substring(5)}`\n  }\n  \n  if (cleaned.length === 12 && cleaned.startsWith('91')) {\n    return `+${cleaned.substring(0, 2)} ${cleaned.substring(2, 7)} ${cleaned.substring(7)}`\n  }\n  \n  return phone\n}\n\n// Calculate percentage\nexport function calculatePercentage(value: number, total: number): number {\n  if (total === 0) return 0\n  return Math.round((value / total) * 100)\n}\n\n// Get status color\nexport function getStatusColor(status: string): string {\n  const statusColors: Record<string, string> = {\n    active: \"text-green-600 bg-green-100\",\n    inactive: \"text-gray-600 bg-gray-100\",\n    pending: \"text-yellow-600 bg-yellow-100\",\n    suspended: \"text-red-600 bg-red-100\",\n    completed: \"text-green-600 bg-green-100\",\n    failed: \"text-red-600 bg-red-100\",\n    cancelled: \"text-gray-600 bg-gray-100\",\n    new: \"text-blue-600 bg-blue-100\",\n    contacted: \"text-purple-600 bg-purple-100\",\n    qualified: \"text-indigo-600 bg-indigo-100\",\n    converted: \"text-green-600 bg-green-100\",\n    lost: \"text-red-600 bg-red-100\",\n  }\n  \n  return statusColors[status.toLowerCase()] || \"text-gray-600 bg-gray-100\"\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n\n// Download file\nexport function downloadFile(data: any, filename: string, type = 'application/json'): void {\n  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement('a')\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Format file size\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)\n}\n\n// Check if mobile device\nexport function isMobile(): boolean {\n  if (typeof window === 'undefined') return false\n  return window.innerWidth < 768\n}\n\n// Scroll to element\nexport function scrollToElement(elementId: string, offset = 0): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const top = element.offsetTop - offset\n    window.scrollTo({ top, behavior: 'smooth' })\n  }\n}\n\n// Local storage helpers\nexport const storage = {\n  get: <T>(key: string, defaultValue?: T): T | null => {\n    if (typeof window === 'undefined') return defaultValue || null\n    \n    try {\n      const item = localStorage.getItem(key)\n      return item ? JSON.parse(item) : defaultValue || null\n    } catch (error) {\n      console.error(`Error getting item from localStorage:`, error)\n      return defaultValue || null\n    }\n  },\n  \n  set: <T>(key: string, value: T): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.setItem(key, JSON.stringify(value))\n    } catch (error) {\n      console.error(`Error setting item in localStorage:`, error)\n    }\n  },\n  \n  remove: (key: string): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.removeItem(key)\n    } catch (error) {\n      console.error(`Error removing item from localStorage:`, error)\n    }\n  },\n  \n  clear: (): void => {\n    if (typeof window === 'undefined') return\n    \n    try {\n      localStorage.clear()\n    } catch (error) {\n      console.error(`Error clearing localStorage:`, error)\n    }\n  }\n}\n\n// Format time\nexport function formatTime(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleTimeString('en-US', {\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: true\n  })\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,MAAc,EAAE,WAAW,GAAG;IAC3D,OAAO,GAAG,WAAW,OAAO,cAAc,CAAC,SAAS;QAClD,uBAAuB;QACvB,uBAAuB;IACzB,IAAI;AACN;AAGO,SAAS,iBAAiB,KAAgC;IAC/D,IAAI,UAAU,aAAa,UAAU,QAAQ,MAAM,QAAQ;QACzD,OAAO;IACT;IACA,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/B;AAGO,SAAS,WAAW,IAAmB,EAAE,SAAS,YAAY;IACnE,MAAM,IAAI,IAAI,KAAK;IAEnB,IAAI,WAAW,cAAc;QAC3B,OAAO,EAAE,kBAAkB,CAAC;IAC9B;IAEA,IAAI,WAAW,YAAY;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,EAAE,OAAO;QAC1C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,eAAe,GAAG,OAAO;QAC7B,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,SAAS,CAAC;QACnD,IAAI,aAAa,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC;QACrE,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,IAAI,WAAW,CAAC;QACxE,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,UAAU,CAAC;IACpD;IAEA,OAAO,EAAE,kBAAkB;AAC7B;AAGO,SAAS,aAAa,IAAY,EAAE,SAAS,EAAE;IACpD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,SAAS,CAAC,GAAG,UAAU;AACrC;AAGO,SAAS,YAAY,SAAkB,EAAE,QAAiB,EAAE,KAAc;IAC/E,IAAI,aAAa,UAAU;QACzB,OAAO,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW;IACpD;IAEA,IAAI,WAAW;QACb,OAAO,UAAU,SAAS,CAAC,GAAG,GAAG,WAAW;IAC9C;IAEA,IAAI,OAAO;QACT,OAAO,MAAM,SAAS,CAAC,GAAG,GAAG,WAAW;IAC1C;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAGO,SAAS,WAAW,SAAS,EAAE;IACpC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,CAAC;IACtC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,OAAO,GAAG,SAAS,YAAY,QAAQ,CAAC,WAAW;AACrD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAGO,SAAS,YAAY,KAAa;IACvC,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;IAErC,IAAI,QAAQ,MAAM,KAAK,IAAI;QACzB,OAAO,CAAC,IAAI,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACjE;IAEA,IAAI,QAAQ,MAAM,KAAK,MAAM,QAAQ,UAAU,CAAC,OAAO;QACrD,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,SAAS,CAAC,IAAI;IACzF;IAEA,OAAO;AACT;AAGO,SAAS,oBAAoB,KAAa,EAAE,KAAa;IAC9D,IAAI,UAAU,GAAG,OAAO;IACxB,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;AACtC;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAuC;QAC3C,QAAQ;QACR,UAAU;QACV,SAAS;QACT,WAAW;QACX,WAAW;QACX,QAAQ;QACR,WAAW;QACX,KAAK;QACL,WAAW;QACX,WAAW;QACX,WAAW;QACX,MAAM;IACR;IAEA,OAAO,YAAY,CAAC,OAAO,WAAW,GAAG,IAAI;AAC/C;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAGO,SAAS,aAAa,IAAS,EAAE,QAAgB,EAAE,OAAO,kBAAkB;IACjF,MAAM,OAAO,IAAI,KAAK;QAAC,OAAO,SAAS,WAAW,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM;KAAG,EAAE;QAAE;IAAK;IAChG,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS;IACd,uCAAmC;;IAAW;IAC9C,OAAO,OAAO,UAAU,GAAG;AAC7B;AAGO,SAAS,gBAAgB,SAAiB,EAAE,SAAS,CAAC;IAC3D,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,MAAM,QAAQ,SAAS,GAAG;QAChC,OAAO,QAAQ,CAAC;YAAE;YAAK,UAAU;QAAS;IAC5C;AACF;AAGO,MAAM,UAAU;IACrB,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAA0B;QAE7D,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,CAAC,EAAE;YACvD,OAAO,gBAAgB;QACzB;IACF;IAEA,KAAK,CAAI,KAAa;QACpB,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,CAAC,EAAE;QACvD;IACF;IAEA,QAAQ,CAAC;QACP,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,CAAC,EAAE;QAC1D;IACF;IAEA,OAAO;QACL,uCAAmC;;QAAK;QAExC,IAAI;YACF,aAAa,KAAK;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAChD;IACF;AACF;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"card-base text-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACnC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"btn-primary\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"btn-outline\",\n        secondary: \"btn-secondary\",\n        ghost: \"hover:bg-primary-100 text-primary\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,4TAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"input-field flex h-10 w-full rounded-md px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,4TAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2RACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,iRAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kRAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kRAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,uSAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,kRAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,kRAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kRAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,kRAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,4TAAC,kRAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,2RAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kRAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,kRAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,kRAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/checkbox.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-white\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4TAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8QACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,iRAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,4TAAC,2RAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,4RAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,4TAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,iRAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/ui/logo.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'full' | 'icon' | 'text'\n  className?: string\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  variant = 'full', \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'h-6 w-6',\n    md: 'h-8 w-8', \n    lg: 'h-10 w-10',\n    xl: 'h-12 w-12'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl', \n    xl: 'text-3xl'\n  }\n\n  const LogoIcon = () => (\n    <div className={`${sizeClasses[size]} bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg flex items-center justify-center shadow-lg ${className}`}>\n      <div className=\"relative\">\n        {/* S */}\n        <div className=\"absolute -left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-1 h-2 bg-white rounded-full mt-0.5 ml-1\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n        </div>\n        \n        {/* G */}\n        <div className=\"absolute left-1 top-0\">\n          <div className=\"w-2 h-1 bg-white rounded-full\"></div>\n          <div className=\"w-1 h-4 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"w-2 h-1 bg-white rounded-full mt-0.5\"></div>\n          <div className=\"absolute right-0 top-2 w-1 h-2 bg-white rounded-full\"></div>\n          <div className=\"absolute right-0 top-3 w-1.5 h-1 bg-white rounded-full\"></div>\n        </div>\n        \n        {/* M */}\n        <div className=\"absolute left-4 top-0\">\n          <div className=\"w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-0.5 top-1 w-1 h-1 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1 top-0 w-1 h-5 bg-white rounded-full\"></div>\n          <div className=\"absolute left-1.5 top-0 w-1 h-5 bg-white rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  )\n\n  const LogoText = () => (\n    <span className={`font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent ${textSizeClasses[size]} ${className}`}>\n      SGM\n    </span>\n  )\n\n  if (variant === 'icon') {\n    return <LogoIcon />\n  }\n\n  if (variant === 'text') {\n    return <LogoText />\n  }\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <LogoIcon />\n      <LogoText />\n    </div>\n  )\n}\n\nexport default Logo\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,kBACf,4TAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,mGAAmG,EAAE,WAAW;sBACnJ,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,WAAW,kBACf,4TAAC;YAAK,WAAW,CAAC,mFAAmF,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;sBAAE;;;;;;IAK/I,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,IAAI,YAAY,QAAQ;QACtB,qBAAO,4TAAC;;;;;IACV;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,4TAAC;;;;;0BACD,4TAAC;;;;;;;;;;;AAGP;KAvEM;uCAyES", "debugId": null}}, {"offset": {"line": 1072, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectUser } from '@/store/slices/authSlice'\nimport { UserRole } from '@/types'\nimport { cn } from '@/lib/utils'\nimport Logo from '@/components/ui/logo'\nimport {\n  LayoutDashboard,\n  Users,\n  Building,\n  TrendingUp,\n  Target,\n  DollarSign,\n  Headphones,\n  Settings,\n  Plus,\n  UserPlus,\n  UserCheck,\n  BarChart3,\n  Shield,\n  Bell,\n  PieChart,\n  CheckSquare,\n  CalendarDays,\n  UserCog,\n  Award,\n  Minus,\n  Receipt,\n  CreditCard,\n  Heart\n} from 'lucide-react'\n\ninterface SidebarProps {\n  collapsed?: boolean\n  onToggle?: () => void\n}\n\nexport default function Sidebar({ collapsed = false, onToggle }: SidebarProps) {\n  const pathname = usePathname()\n  const user = useAppSelector((state) => selectUser(state as any))\n\n  console.log('Sidebar rendering...', { collapsed, pathname, user })\n\n  const menuSections = [\n    {\n      title: \"Dashboard\",\n      items: [\n        {\n          id: \"main-dashboard\",\n          label: \"Overview\",\n          icon: LayoutDashboard,\n          href: \"/dashboard\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES, UserRole.USER]\n        }\n        \n      ]\n    },\n    {\n      title: \"User Management\",\n      items: [\n        {\n          id: \"users-overview\",\n          label: \"All Users\",\n          icon: Users,\n          href: \"/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-management-comprehensive\",\n          label: \"User Management\",\n          icon: Users,\n          href: \"/user-management\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-user\",\n          label: \"Add User\",\n          icon: UserPlus,\n          href: \"/users/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"role-management\",\n          label: \"Role Management\",\n          icon: UserCheck,\n          href: \"/users/roles\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"user-wishlists\",\n          label: \"User Wishlists\",\n          icon: Heart,\n          href: \"/wishlists\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Property Management\",\n      items: [\n        {\n          id: \"properties-overview\",\n          label: \"All Properties\",\n          icon: Building,\n          href: \"/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-property\",\n          label: \"Add Property\",\n          icon: Plus,\n          href: \"/properties/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-owners\",\n          label: \"Owners & Developers\",\n          icon: UserCheck,\n          href: \"/property-owners\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-owner\",\n          label: \"Add Owner/Developer\",\n          icon: UserPlus,\n          href: \"/properties/owners/add\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Stock Investments\",\n      items: [\n        {\n          id: \"stocks-overview\",\n          label: \"All Stocks\",\n          icon: TrendingUp,\n          href: \"/stocks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"create-stock\",\n          label: \"Create Stock\",\n          icon: Plus,\n          href: \"/stocks/create\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Lead Management\",\n      items: [\n        {\n          id: \"leads-overview\",\n          label: \"Lead Management\",\n          icon: Target,\n          href: \"/leads\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN, UserRole.SALES]\n        },\n        {\n          id: \"sales-analytics\",\n          label: \"Sales Analytics\",\n          icon: BarChart3,\n          href: \"/sales-analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Sales Management\",\n      items: [\n        {\n          id: \"sales-team\",\n          label: \"Sales Team\",\n          icon: UserCog,\n          href: \"/sales-team\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-tasks\",\n          label: \"Sales Tasks\",\n          icon: CheckSquare,\n          href: \"/sales-tasks\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-calendar\",\n          label: \"Sales Calendar\",\n          icon: CalendarDays,\n          href: \"/sales-calendar\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"sales-targets\",\n          label: \"Sales Targets\",\n          icon: Award,\n          href: \"/sales-targets\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"commissions\",\n          label: \"Commissions\",\n          icon: DollarSign,\n          href: \"/commissions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Financial Management\",\n      items: [\n        {\n          id: \"finance-overview\",\n          label: \"Financial Management\",\n          icon: DollarSign,\n          href: \"/finance\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"add-funds\",\n          label: \"Add Funds\",\n          icon: Plus,\n          href: \"/add-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"deduct-funds\",\n          label: \"Deduct Funds\",\n          icon: Minus,\n          href: \"/deduct-funds\",\n          roles: [UserRole.ADMIN]\n        },\n        {\n          id: \"admin-transactions\",\n          label: \"All Transactions\",\n          icon: Receipt,\n          href: \"/admin-transactions\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"withdrawal-requests\",\n          label: \"Withdrawal Requests\",\n          icon: CreditCard,\n          href: \"/withdrawal-requests\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Support Management\",\n      items: [\n        {\n          id: \"support-dashboard\",\n          label: \"Support Management\",\n          icon: Headphones,\n          href: \"/support\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        }\n      ]\n    },\n    {\n      title: \"System & Settings\",\n      items: [\n        {\n          id: \"system-settings\",\n          label: \"Settings Management\",\n          icon: Settings,\n          href: \"/settings\",\n          roles: [UserRole.ADMIN]\n        }\n      ]\n    },\n    {\n      title: \"Reports & Analytics\",\n      items: [\n        {\n          id: \"analytics-dashboard\",\n          label: \"Analytics Dashboard\",\n          icon: BarChart3,\n          href: \"/analytics\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"user-reports\",\n          label: \"User Reports\",\n          icon: Users,\n          href: \"/reports/users\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"property-reports\",\n          label: \"Property Reports\",\n          icon: Building,\n          href: \"/reports/properties\",\n          roles: [UserRole.ADMIN, UserRole.SUBADMIN]\n        },\n        {\n          id: \"financial-reports\",\n          label: \"Financial Reports\",\n          icon: DollarSign,\n          href: \"/reports/financial\",\n          roles: [UserRole.ADMIN]\n        },\n       \n      ]\n    }\n  ]\n\n  // Filter menu sections based on user role\n  const userRole = user?.role as UserRole || UserRole.USER\n  const filteredSections = menuSections.map(section => ({\n    ...section,\n    items: section.items.filter(item => item.roles.includes(userRole))\n  })).filter(section => section.items.length > 0)\n\n  // Debug logging\n  console.log('User:', user)\n  console.log('User Role:', user?.role)\n  console.log('UserRole enum:', userRole)\n  console.log('Menu Sections:', menuSections.length)\n  console.log('Filtered Sections:', filteredSections.length)\n\n  // Use filtered sections based on user role\n  const sectionsToShow = filteredSections\n\n  const isActive = (href: string) => {\n    if (!pathname) return false\n    if (href === '/dashboard') {\n      return pathname === '/dashboard'\n    }\n    return pathname.startsWith(href)\n  }\n\n  return (\n    <div className={cn(\n      \"h-screen bg-white border-r border-sky-200 flex flex-col transition-all duration-300 ease-in-out shadow-lg\",\n      collapsed ? \"w-20\" : \"w-72\"\n    )}>\n      {/* Logo */}\n      <div className=\"flex items-center space-x-3 p-4 border-b border-sky-200 flex-shrink-0 bg-gradient-to-r from-sky-50 to-sky-100\">\n        <Logo size={collapsed ? \"lg\" : \"xl\"} variant={collapsed ? \"icon\" : \"full\"} />\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-4 overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-sky-100\">\n        <div className=\"space-y-2\">\n          {sectionsToShow.length === 0 ? (\n            <div className=\"text-center text-red-500 p-4\">\n              <p>No menu items found!</p>\n              <p>User: {user?.firstName || 'Not logged in'}</p>\n              <p>Role: {user?.role || 'No role'}</p>\n            </div>\n          ) : (\n            sectionsToShow.map((section) => (\n              <div key={section.title} className=\"space-y-1 mb-6\">\n                {!collapsed && (\n                  <h3 className=\"px-3 py-2 text-xs font-semibold text-sky-600 uppercase tracking-wider\">\n                    {section.title}\n                  </h3>\n                )}\n\n                {section.items.map((item) => (\n                  <Link\n                    key={item.id}\n                    href={item.href || '#'}\n                    className={cn(\n                      \"flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200\",\n                      isActive(item.href || '')\n                        ? \"bg-sky-600 text-white shadow-md\"\n                        : \"text-gray-700 hover:text-sky-600 hover:bg-sky-50\"\n                    )}\n                  >\n                    <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                    {!collapsed && (\n                      <span className=\"flex-1 truncate\">{item.label}</span>\n                    )}\n                  </Link>\n                ))}\n              </div>\n            ))\n          )}\n        </div>\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t border-sky-200 bg-gradient-to-r from-sky-50 to-sky-100\">\n        {!collapsed && (\n          <div className=\"text-xs text-sky-600 text-center font-medium\">\n            © 2025 SGM. All rights reserved.\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAyCe,SAAS,QAAQ,EAAE,YAAY,KAAK,EAAE,QAAQ,EAAgB;;IAC3E,MAAM,WAAW,CAAA,GAAA,oQAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;wCAAE,CAAC,QAAU,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE;;IAElD,QAAQ,GAAG,CAAC,wBAAwB;QAAE;QAAW;QAAU;IAAK;IAEhE,MAAM,eAAe;QACnB;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mTAAA,CAAA,kBAAe;oBACrB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,IAAI;qBAAC;gBAC3E;aAED;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,uSAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6RAAA,CAAA,SAAM;oBACZ,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;wBAAE,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBAC5D;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,mSAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,kTAAA,CAAA,cAAW;oBACjB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,6SAAA,CAAA,eAAY;oBAClB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,yRAAA,CAAA,OAAI;oBACV,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,+RAAA,CAAA,UAAO;oBACb,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,qSAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,YAAS;oBACf,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,2RAAA,CAAA,QAAK;oBACX,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,iSAAA,CAAA,WAAQ;oBACd,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;wBAAE,wHAAA,CAAA,WAAQ,CAAC,QAAQ;qBAAC;gBAC5C;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM,ySAAA,CAAA,aAAU;oBAChB,MAAM;oBACN,OAAO;wBAAC,wHAAA,CAAA,WAAQ,CAAC,KAAK;qBAAC;gBACzB;aAED;QACH;KACD;IAED,0CAA0C;IAC1C,MAAM,WAAW,MAAM,QAAoB,wHAAA,CAAA,WAAQ,CAAC,IAAI;IACxD,MAAM,mBAAmB,aAAa,GAAG,CAAC,CAAA,UAAW,CAAC;YACpD,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC1D,CAAC,GAAG,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE7C,gBAAgB;IAChB,QAAQ,GAAG,CAAC,SAAS;IACrB,QAAQ,GAAG,CAAC,cAAc,MAAM;IAChC,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,QAAQ,GAAG,CAAC,kBAAkB,aAAa,MAAM;IACjD,QAAQ,GAAG,CAAC,sBAAsB,iBAAiB,MAAM;IAEzD,2CAA2C;IAC3C,MAAM,iBAAiB;IAEvB,MAAM,WAAW,CAAC;QAChB,IAAI,CAAC,UAAU,OAAO;QACtB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,4TAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6GACA,YAAY,SAAS;;0BAGrB,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC,mIAAA,CAAA,UAAI;oBAAC,MAAM,YAAY,OAAO;oBAAM,SAAS,YAAY,SAAS;;;;;;;;;;;0BAIrE,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;8BACZ,eAAe,MAAM,KAAK,kBACzB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;0CAAE;;;;;;0CACH,4TAAC;;oCAAE;oCAAO,MAAM,aAAa;;;;;;;0CAC7B,4TAAC;;oCAAE;oCAAO,MAAM,QAAQ;;;;;;;;;;;;+BAG1B,eAAe,GAAG,CAAC,CAAC,wBAClB,4TAAC;4BAAwB,WAAU;;gCAChC,CAAC,2BACA,4TAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;gCAIjB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,4TAAC,8RAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI,IAAI;wCACnB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA,SAAS,KAAK,IAAI,IAAI,MAClB,oCACA;;0DAGN,4TAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CACpB,CAAC,2BACA,4TAAC;gDAAK,WAAU;0DAAmB,KAAK,KAAK;;;;;;;uCAX1C,KAAK,EAAE;;;;;;2BATR,QAAQ,KAAK;;;;;;;;;;;;;;;0BA+B/B,4TAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,4TAAC;oBAAI,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAOxE;GArWwB;;QACL,oQAAA,CAAA,cAAW;QACf,wHAAA,CAAA,iBAAc;;;KAFL", "debugId": null}}, {"offset": {"line": 1644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector, useAppDispatch } from '@/store'\nimport { selectUser, logoutAsync } from '@/store/slices/authSlice'\nimport { Button } from '@/components/ui/button'\nimport Sidebar from './Sidebar'\nimport { Menu, Bell, Search, LogOut, User, Settings, HelpCircle, ChevronDown } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)\n  const [userMenuOpen, setUserMenuOpen] = useState(false)\n  const router = useRouter()\n  const dispatch = useAppDispatch()\n  const user = useAppSelector(selectUser)\n\n  const handleLogout = async () => {\n    await dispatch(logoutAsync())\n    router.push('/login')\n  }\n\n  const toggleSidebar = () => {\n    setSidebarCollapsed(!sidebarCollapsed)\n  }\n\n  return (\n    <div className=\"h-screen bg-white overflow-hidden\">\n      <div className=\"flex h-full\">\n        {/* Sidebar */}\n        <Sidebar collapsed={sidebarCollapsed} onToggle={toggleSidebar} />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col h-full overflow-hidden\">\n          {/* Header */}\n          <header className=\"bg-gradient-to-r from-sky-500 to-sky-600 shadow-lg border-b border-sky-700 flex-shrink-0 z-40\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex justify-between items-center py-4\">\n                {/* Left side */}\n                <div className=\"flex items-center space-x-4\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={toggleSidebar}\n                    className=\"lg:hidden hover:bg-white/20 text-white\"\n                  >\n                    <Menu className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Search Bar */}\n                  <div className=\"hidden md:flex items-center space-x-2 bg-white/20 rounded-lg px-4 py-2.5 min-w-[350px] border border-white/30\">\n                    <Search className=\"h-4 w-4 text-white/70\" />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search users, properties, transactions...\"\n                      className=\"bg-transparent border-none outline-none flex-1 text-sm placeholder:text-white/70 text-white\"\n                    />\n                    <kbd className=\"hidden lg:inline-flex items-center px-2 py-1 text-xs font-medium text-sky-600 bg-white rounded border border-white/30\">\n                      ⌘K\n                    </kbd>\n                  </div>\n                </div>\n\n                {/* Right side */}\n                <div className=\"flex items-center space-x-3\">\n                  {/* Quick Actions */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"hover:bg-white/20 text-white\">\n                    <HelpCircle className=\"h-5 w-5\" />\n                  </Button>\n\n                  {/* Notifications */}\n                  <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-white/20 text-white\">\n                    <Bell className=\"h-5 w-5\" />\n                    <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-yellow-500 text-black text-xs rounded-full flex items-center justify-center font-semibold\">\n                      5\n                    </span>\n                  </Button>\n\n                  {/* User Menu */}\n                  <div className=\"relative\">\n                    <Button\n                      variant=\"ghost\"\n                      onClick={() => setUserMenuOpen(!userMenuOpen)}\n                      className=\"flex items-center space-x-3 px-3 py-2 hover:bg-white/20 rounded-lg text-white\"\n                    >\n                      <div className=\"hidden sm:block text-right\">\n                        <p className=\"text-sm font-medium text-white\">\n                          {user?.firstName} {user?.lastName}\n                        </p>\n                        <div className=\"flex items-center justify-end space-x-1\">\n                          <div className=\"bg-yellow-500 px-2 py-0.5 rounded-full\">\n                            <span className=\"text-xs font-medium text-black capitalize\">\n                              {user?.role}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"h-10 w-10 bg-white/20 rounded-full flex items-center justify-center shadow-sm border border-white/30\">\n                        <User className=\"h-5 w-5 text-white\" />\n                      </div>\n\n                      <ChevronDown className=\"h-4 w-4 text-white/70\" />\n                    </Button>\n\n                    {/* User Dropdown Menu */}\n                    {userMenuOpen && (\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-sky-200 py-2 z-50\">\n                        <div className=\"px-4 py-2 border-b border-sky-100\">\n                          <p className=\"text-sm font-medium text-gray-900\">\n                            {user?.firstName} {user?.lastName}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                        </div>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <User className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Profile\n                        </Button>\n\n                        <Button\n                          variant=\"ghost\"\n                          className=\"w-full justify-start px-4 py-2 text-sm hover:bg-sky-50 text-gray-700\"\n                          onClick={() => setUserMenuOpen(false)}\n                        >\n                          <Settings className=\"h-4 w-4 mr-3 text-sky-600\" />\n                          Settings\n                        </Button>\n\n                        <div className=\"border-t border-sky-100 mt-2 pt-2\">\n                          <Button\n                            variant=\"ghost\"\n                            className=\"w-full justify-start px-4 py-2 text-sm text-red-600 hover:bg-red-50\"\n                            onClick={handleLogout}\n                          >\n                            <LogOut className=\"h-4 w-4 mr-3\" />\n                            Logout\n                          </Button>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </header>\n\n          {/* Main Content Area */}\n          <main className=\"flex-1 overflow-y-auto bg-gray-50\">\n            {children}\n          </main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAce,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,OAAO,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAEtC,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC,0IAAA,CAAA,UAAO;oBAAC,WAAW;oBAAkB,UAAU;;;;;;8BAGhD,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAO,WAAU;sCAChB,cAAA,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDAEb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAIlB,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,4TAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;;;;;;sEAEZ,4TAAC;4DAAI,WAAU;sEAAwH;;;;;;;;;;;;;;;;;;sDAO3I,4TAAC;4CAAI,WAAU;;8DAEb,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;8DAC5C,cAAA,4TAAC,qTAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAIxB,4TAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAO,WAAU;;sEAC5C,4TAAC,yRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,4TAAC;4DAAK,WAAU;sEAAgI;;;;;;;;;;;;8DAMlJ,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS,IAAM,gBAAgB,CAAC;4DAChC,WAAU;;8EAEV,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAI,WAAU;sFACb,cAAA,4TAAC;gFAAI,WAAU;0FACb,cAAA,4TAAC;oFAAK,WAAU;8FACb,MAAM;;;;;;;;;;;;;;;;;;;;;;8EAMf,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAGlB,4TAAC,2SAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;wDAIxB,8BACC,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAE,WAAU;;gFACV,MAAM;gFAAU;gFAAE,MAAM;;;;;;;sFAE3B,4TAAC;4EAAE,WAAU;sFAAyB,MAAM;;;;;;;;;;;;8EAG9C,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,yRAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIhD,4TAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAU;oEACV,SAAS,IAAM,gBAAgB;;sFAE/B,4TAAC,iSAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAA8B;;;;;;;8EAIpD,4TAAC;oEAAI,WAAU;8EACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,WAAU;wEACV,SAAS;;0FAET,4TAAC,iSAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAarD,4TAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb;GArJwB;;QAGP,oQAAA,CAAA,YAAS;QACP,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;;;KALL", "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/adminKycApi.ts"], "sourcesContent": ["import { base<PERSON><PERSON>, <PERSON>piR<PERSON>po<PERSON>, BaseQueryParams, createQueryParams } from './baseApi'\n\n// KYC Document Interface\nexport interface KYCDocument {\n  _id: string\n  type: 'identity' | 'address' | 'income' | 'bank' | 'photo' | 'signature' | 'other'\n  subType: string\n  documentNumber?: string\n  issuedBy?: string\n  issuedDate?: string\n  expiryDate?: string\n  status: 'pending' | 'verified' | 'rejected'\n  rejectionReason?: string\n  fileUrl?: string\n  uploadedAt: string\n  verifiedAt?: string\n}\n\n// KYC Status Interface\nexport interface KYCStatus {\n  _id: string\n  userId: {\n    _id: string\n    firstName: string\n    lastName: string\n    email: string\n    phone?: string\n    profileImage?: string\n  }\n  status: 'not_started' | 'pending' | 'approved' | 'rejected' | 'under_review'\n  level: 'basic' | 'advanced' | 'premium'\n  submittedAt?: string\n  reviewedAt?: string\n  reviewedBy?: {\n    _id: string\n    firstName: string\n    lastName: string\n  }\n  rejectionReason?: string\n  documents: KYCDocument[]\n  personalInfo: {\n    nationality?: string\n    placeOfBirth?: string\n    gender?: string\n    maritalStatus?: string\n  }\n  address: {\n    street?: string\n    city?: string\n    state?: string\n    postalCode?: string\n    country?: string\n    addressType?: 'permanent' | 'current' | 'mailing'\n    residenceSince?: string\n  }\n  identityInfo: {\n    aadharNumber?: string\n    panNumber?: string\n    passportNumber?: string\n    drivingLicenseNumber?: string\n  }\n  bankInfo: {\n    accountNumber?: string\n    ifscCode?: string\n    bankName?: string\n    accountType?: string\n    accountHolderName?: string\n  }\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface KYCQueryParams extends BaseQueryParams {\n  status?: string\n  level?: string\n  userId?: string\n}\n\nexport interface KYCApprovalRequest {\n  status: 'approved' | 'rejected'\n  rejectionReason?: string\n  level?: 'basic' | 'advanced' | 'premium'\n  notes?: string\n}\n\nexport const adminKycApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get all KYC submissions for admin review\n    getAllKYCSubmissions: builder.query<ApiResponse<{\n      kycs: KYCStatus[]\n      total: number\n      page: number\n      limit: number\n      totalPages: number\n    }>, KYCQueryParams>({\n      query: (params = {}) => ({\n        url: '/admin/kyc',\n        params: createQueryParams(params)\n      }),\n      providesTags: ['KYC'],\n    }),\n\n    // Get KYC by ID for detailed review\n    getKYCById: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (id) => `/admin/kyc/${id}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Get KYC by User ID\n    getKYCByUserId: builder.query<ApiResponse<KYCStatus>, string>({\n      query: (userId) => `/kyc/user/${userId}`,\n      providesTags: (_, __, userId) => [{ type: 'KYC', id: userId }],\n    }),\n\n    // Approve or reject KYC\n    updateKYCStatus: builder.mutation<ApiResponse<KYCStatus>, { id: string; data: KYCApprovalRequest }>({\n      query: ({ id, data }) => ({\n        url: `/kyc/${id}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (_, __, { id }) => [{ type: 'KYC', id }, 'KYC', 'User'],\n    }),\n\n    // Get KYC statistics for admin dashboard\n    getKYCStats: builder.query<ApiResponse<{\n      totalSubmissions: number\n      pendingReview: number\n      approved: number\n      rejected: number\n      underReview: number\n      basicLevel: number\n      advancedLevel: number\n      premiumLevel: number\n      recentSubmissions: Array<{\n        _id: string\n        userId: {\n          firstName: string\n          lastName: string\n          email: string\n        }\n        status: string\n        submittedAt: string\n      }>\n    }>, void>({\n      query: () => '/admin/kyc/stats',\n      providesTags: ['KYC'],\n    }),\n\n    // Bulk approve/reject KYCs\n    bulkUpdateKYCStatus: builder.mutation<ApiResponse<{ updated: number }>, {\n      ids: string[]\n      data: KYCApprovalRequest\n    }>({\n      query: ({ ids, data }) => ({\n        url: '/admin/kyc/bulk-update',\n        method: 'PUT',\n        body: { ids, ...data },\n      }),\n      invalidatesTags: ['KYC', 'User'],\n    }),\n\n    // Get KYC document by ID\n    getKYCDocument: builder.query<ApiResponse<KYCDocument>, string>({\n      query: (documentId) => `/admin/kyc/document/${documentId}`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Verify/reject specific document\n    updateDocumentStatus: builder.mutation<ApiResponse<KYCDocument>, {\n      documentId: string\n      status: 'verified' | 'rejected'\n      rejectionReason?: string\n    }>({\n      query: ({ documentId, ...data }) => ({\n        url: `/admin/kyc/document/${documentId}/status`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: ['KYC'],\n    }),\n\n    // Get KYC history/audit trail\n    getKYCHistory: builder.query<ApiResponse<Array<{\n      action: string\n      status: string\n      timestamp: string\n      details?: string\n      performedBy: {\n        _id: string\n        firstName: string\n        lastName: string\n      }\n    }>>, string>({\n      query: (kycId) => `/admin/kyc/${kycId}/history`,\n      providesTags: (_, __, id) => [{ type: 'KYC', id }],\n    }),\n\n    // Download KYC PDF\n    downloadKYCPDF: builder.mutation<Blob, string>({\n      query: (kycId) => ({\n        url: `/kyc/${kycId}/download-pdf`,\n        method: 'GET',\n        responseHandler: (response) => response.blob(),\n      }),\n    }),\n\n    // Export KYC data\n    exportKYCData: builder.mutation<ApiResponse<{ downloadUrl: string }>, {\n      format: 'csv' | 'excel' | 'pdf'\n      filters?: KYCQueryParams\n    }>({\n      query: (data) => ({\n        url: '/admin/kyc/export',\n        method: 'POST',\n        body: data,\n      }),\n    }),\n  }),\n})\n\nexport const {\n  useGetAllKYCSubmissionsQuery,\n  useGetKYCByIdQuery,\n  useGetKYCByUserIdQuery,\n  useUpdateKYCStatusMutation,\n  useDownloadKYCPDFMutation,\n  useGetKYCStatsQuery,\n  useBulkUpdateKYCStatusMutation,\n  useGetKYCDocumentQuery,\n  useUpdateDocumentStatusMutation,\n  useGetKYCHistoryQuery,\n  useExportKYCDataMutation,\n} = adminKycApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAqFO,MAAM,cAAc,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACjD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,2CAA2C;YAC3C,sBAAsB,QAAQ,KAAK,CAMf;gBAClB,OAAO,CAAC,SAAS,CAAC,CAAC,GAAK,CAAC;wBACvB,KAAK;wBACL,QAAQ,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;oBAC5B,CAAC;gBACD,cAAc;oBAAC;iBAAM;YACvB;YAEA,oCAAoC;YACpC,YAAY,QAAQ,KAAK,CAAiC;gBACxD,OAAO,CAAC,KAAO,CAAC,WAAW,EAAE,IAAI;gBACjC,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,qBAAqB;YACrB,gBAAgB,QAAQ,KAAK,CAAiC;gBAC5D,OAAO,CAAC,SAAW,CAAC,UAAU,EAAE,QAAQ;gBACxC,cAAc,CAAC,GAAG,IAAI,SAAW;wBAAC;4BAAE,MAAM;4BAAO,IAAI;wBAAO;qBAAE;YAChE;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,QAAQ,CAAmE;gBAClG,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC;wBACxB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,GAAG,IAAI,EAAE,EAAE,EAAE,GAAK;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;wBAAG;wBAAO;qBAAO;YAC1E;YAEA,yCAAyC;YACzC,aAAa,QAAQ,KAAK,CAmBhB;gBACR,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAM;YACvB;YAEA,2BAA2B;YAC3B,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAK,CAAC;wBACzB,KAAK;wBACL,QAAQ;wBACR,MAAM;4BAAE;4BAAK,GAAG,IAAI;wBAAC;oBACvB,CAAC;gBACD,iBAAiB;oBAAC;oBAAO;iBAAO;YAClC;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAAmC;gBAC9D,OAAO,CAAC,aAAe,CAAC,oBAAoB,EAAE,YAAY;gBAC1D,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,kCAAkC;YAClC,sBAAsB,QAAQ,QAAQ,CAInC;gBACD,OAAO,CAAC,EAAE,UAAU,EAAE,GAAG,MAAM,GAAK,CAAC;wBACnC,KAAK,CAAC,oBAAoB,EAAE,WAAW,OAAO,CAAC;wBAC/C,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAM;YAC1B;YAEA,8BAA8B;YAC9B,eAAe,QAAQ,KAAK,CAUf;gBACX,OAAO,CAAC,QAAU,CAAC,WAAW,EAAE,MAAM,QAAQ,CAAC;gBAC/C,cAAc,CAAC,GAAG,IAAI,KAAO;wBAAC;4BAAE,MAAM;4BAAO;wBAAG;qBAAE;YACpD;YAEA,mBAAmB;YACnB,gBAAgB,QAAQ,QAAQ,CAAe;gBAC7C,OAAO,CAAC,QAAU,CAAC;wBACjB,KAAK,CAAC,KAAK,EAAE,MAAM,aAAa,CAAC;wBACjC,QAAQ;wBACR,iBAAiB,CAAC,WAAa,SAAS,IAAI;oBAC9C,CAAC;YACH;YAEA,kBAAkB;YAClB,eAAe,QAAQ,QAAQ,CAG5B;gBACD,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,4BAA4B,EAC5B,kBAAkB,EAClB,sBAAsB,EACtB,0BAA0B,EAC1B,yBAAyB,EACzB,mBAAmB,EACnB,8BAA8B,EAC9B,sBAAsB,EACtB,+BAA+B,EAC/B,qBAAqB,EACrB,wBAAwB,EACzB,GAAG", "debugId": null}}, {"offset": {"line": 2212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/userDetailsApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types for user-specific data\nexport interface UserInvestment {\n  _id: string\n  propertyId: string\n  propertyName: string\n  location: string\n  stocksOwned: number\n  stockPrice: number\n  totalInvestment: number\n  currentValue: number\n  returns: number\n  roi: number\n  purchaseDate: string\n  status: 'active' | 'sold'\n  expectedAnnualReturn: number\n  propertyImage?: string\n}\n\nexport interface UserTransaction {\n  _id: string\n  type: 'deposit' | 'withdrawal' | 'investment' | 'return' | 'refund'\n  amount: number\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  paymentMethod?: string\n  description: string\n  createdAt: string\n  updatedAt: string\n  propertyId?: string\n  propertyName?: string\n  reference?: string\n}\n\nexport interface UserActivity {\n  _id: string\n  action: string\n  entity: string\n  entityId?: string\n  description: string\n  ipAddress?: string\n  userAgent?: string\n  location?: string\n  createdAt: string\n  metadata?: any\n}\n\nexport interface UserStats {\n  totalInvestments: number\n  totalReturns: number\n  activeInvestments: number\n  totalTransactions: number\n  walletBalance: number\n  portfolioValue: number\n  roi: number\n  joinDate: string\n  lastActivity: string\n}\n\n// API endpoints for user details\nexport const userDetailsApi = baseApi.injectEndpoints({\n  overrideExisting: true,\n  endpoints: (builder) => ({\n    // Get user investments\n    getUserInvestments: builder.query<{\n      success: boolean\n      data: {\n        investments: UserInvestment[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      status?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/investments`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Investment'\n      ],\n    }),\n\n    // Get user transactions\n    getUserTransactions: builder.query<{\n      success: boolean\n      data: {\n        transactions: UserTransaction[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      type?: string\n      status?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/transactions`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Transaction'\n      ],\n    }),\n\n    // Get user activity log\n    getUserActivity: builder.query<{\n      success: boolean\n      data: {\n        activities: UserActivity[]\n        pagination: {\n          currentPage: number\n          totalPages: number\n          totalItems: number\n          itemsPerPage: number\n        }\n      }\n    }, {\n      userId: string\n      page?: number\n      limit?: number\n      action?: string\n      entity?: string\n      startDate?: string\n      endDate?: string\n      sortBy?: string\n      sortOrder?: 'asc' | 'desc'\n    }>({\n      query: ({ userId, ...params }) => ({\n        url: `/admin/users/${userId}/activity`,\n        params,\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Activity'\n      ],\n    }),\n\n    // Get user statistics\n    getUserStats: builder.query<{\n      success: boolean\n      data: UserStats\n    }, string>({\n      query: (userId) => `/admin/users/${userId}/stats`,\n      providesTags: (result, error, userId) => [\n        { type: 'User', id: userId },\n        'UserStats'\n      ],\n    }),\n\n    // Get user portfolio performance\n    getUserPortfolioPerformance: builder.query<{\n      success: boolean\n      data: {\n        chartData: Array<{\n          date: string\n          value: number\n          invested: number\n          returns: number\n        }>\n        summary: {\n          totalInvested: number\n          currentValue: number\n          totalReturns: number\n          roi: number\n          bestPerforming: UserInvestment\n          worstPerforming: UserInvestment\n        }\n      }\n    }, {\n      userId: string\n      period?: '1M' | '3M' | '6M' | '1Y' | 'ALL'\n    }>({\n      query: ({ userId, period = '1Y' }) => ({\n        url: `/admin/users/${userId}/portfolio-performance`,\n        params: { period },\n      }),\n      providesTags: (result, error, { userId }) => [\n        { type: 'User', id: userId },\n        'Portfolio'\n      ],\n    }),\n  }),\n})\n\nexport const {\n  useGetUserInvestmentsQuery,\n  useGetUserTransactionsQuery,\n  useGetUserActivityQuery,\n  useGetUserStatsQuery,\n  useGetUserPortfolioPerformanceQuery,\n} = userDetailsApi\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AA4DO,MAAM,iBAAiB,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IACpD,kBAAkB;IAClB,WAAW,CAAC,UAAY,CAAC;YACvB,uBAAuB;YACvB,oBAAoB,QAAQ,KAAK,CAkB9B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,YAAY,CAAC;wBACzC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,qBAAqB,QAAQ,KAAK,CAqB/B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,aAAa,CAAC;wBAC1C;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,wBAAwB;YACxB,iBAAiB,QAAQ,KAAK,CAqB3B;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,QAAQ,GAAK,CAAC;wBACjC,KAAK,CAAC,aAAa,EAAE,OAAO,SAAS,CAAC;wBACtC;oBACF,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,sBAAsB;YACtB,cAAc,QAAQ,KAAK,CAGhB;gBACT,OAAO,CAAC,SAAW,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC;gBACjD,cAAc,CAAC,QAAQ,OAAO,SAAW;wBACvC;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;YAEA,iCAAiC;YACjC,6BAA6B,QAAQ,KAAK,CAqBvC;gBACD,OAAO,CAAC,EAAE,MAAM,EAAE,SAAS,IAAI,EAAE,GAAK,CAAC;wBACrC,KAAK,CAAC,aAAa,EAAE,OAAO,sBAAsB,CAAC;wBACnD,QAAQ;4BAAE;wBAAO;oBACnB,CAAC;gBACD,cAAc,CAAC,QAAQ,OAAO,EAAE,MAAM,EAAE,GAAK;wBAC3C;4BAAE,MAAM;4BAAQ,IAAI;wBAAO;wBAC3B;qBACD;YACH;QACF,CAAC;AACH;AAEO,MAAM,EACX,0BAA0B,EAC1B,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,mCAAmC,EACpC,GAAG", "debugId": null}}, {"offset": {"line": 2306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/salesApi.ts"], "sourcesContent": ["import { baseApi } from './baseApi'\n\n// Types based on server models\nexport interface SalesTeamMember {\n  _id: string\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  role: 'sales' | 'sales_manager' | 'team_lead'\n  status: 'active' | 'inactive' | 'on_leave'\n  territory?: string\n  team?: string\n  manager?: string\n  joinDate: string\n  profileImage?: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesTask {\n  _id: string\n  title: string\n  description?: string\n  type: 'call' | 'email' | 'meeting' | 'follow_up' | 'demo' | 'proposal'\n  priority: 'low' | 'medium' | 'high' | 'urgent'\n  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'\n  dueDate: string\n  dueTime?: string\n  estimatedDuration?: number\n  actualDuration?: number\n  assignedTo: string\n  createdBy: string\n  relatedTo?: {\n    type: 'lead' | 'customer' | 'property'\n    id: string\n    name: string\n  }\n  tags: string[]\n  notes?: string\n  completedAt?: string\n  reminders: Array<{\n    time: string\n    sent: boolean\n  }>\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesTarget {\n  _id: string\n  salesRepId: string\n  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'\n  targetAmount: number\n  achievedAmount: number\n  targetLeads: number\n  achievedLeads: number\n  targetDeals: number\n  achievedDeals: number\n  startDate: string\n  endDate: string\n  status: 'active' | 'completed' | 'paused' | 'cancelled'\n  notes?: string\n  createdBy: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Commission {\n  _id: string\n  transactionId: string\n  commissionType: 'referral' | 'sales' | 'bonus'\n  earnerId: string\n  customerId: string\n  stockTransactionId: string\n  propertyId: string\n  stockQuantity: number\n  commissionRate?: number\n  commissionPerStock?: number\n  calculationMethod: 'percentage' | 'fixed_per_stock' | 'fixed_amount'\n  investmentAmount: number\n  commissionAmount: number\n  status: 'pending' | 'approved' | 'paid' | 'rejected'\n  rejectionReason?: string\n  approvedBy?: string\n  approvedAt?: string\n  paidAt?: string\n  notes?: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesCalendarEvent {\n  _id: string\n  title: string\n  description?: string\n  type: 'meeting' | 'call' | 'demo' | 'follow_up' | 'presentation' | 'site_visit'\n  startDate: string\n  startTime: string\n  endDate: string\n  endTime: string\n  location?: string\n  isAllDay: boolean\n  attendees: Array<{\n    userId: string\n    name: string\n    email: string\n    status: 'pending' | 'accepted' | 'declined' | 'tentative'\n  }>\n  relatedTo?: {\n    type: 'lead' | 'customer' | 'property' | 'deal'\n    id: string\n    name: string\n  }\n  meetingLink?: string\n  notes?: string\n  reminders: Array<{\n    time: string\n    sent: boolean\n  }>\n  createdBy: string\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface SalesStats {\n  totalSales: number\n  totalRevenue: number\n  totalCommission: number\n  conversionRate: number\n  totalLeads: number\n  convertedLeads: number\n  activeTargets: number\n  completedTargets: number\n  pendingTasks: number\n  completedTasks: number\n  teamMembers: number\n  activeMembers: number\n}\n\nexport interface ApiResponse<T> {\n  success: boolean\n  message: string\n  data: T\n  pagination?: {\n    currentPage: number\n    totalPages: number\n    totalItems: number\n    itemsPerPage: number\n  }\n}\n\nexport const salesApi = baseApi.injectEndpoints({\n  endpoints: (builder) => ({\n    // Sales Stats\n    getSalesStats: builder.query<ApiResponse<SalesStats>, void>({\n      query: () => '/sales/stats',\n      providesTags: ['Sales'],\n    }),\n\n    // Sales Team Management\n    getSalesTeam: builder.query<ApiResponse<SalesTeamMember[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      role?: string\n      search?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/team',\n        params\n      }),\n      providesTags: ['SalesTeam'],\n    }),\n\n    getSalesTeamMember: builder.query<ApiResponse<SalesTeamMember>, string>({\n      query: (id) => `/users/${id}`,\n      providesTags: (result, error, id) => [{ type: 'SalesTeam', id }],\n    }),\n\n    createSalesTeamMember: builder.mutation<ApiResponse<SalesTeamMember>, Partial<SalesTeamMember>>({\n      query: (data) => ({\n        url: '/users',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTeam'],\n    }),\n\n    updateSalesTeamMember: builder.mutation<ApiResponse<SalesTeamMember>, {\n      id: string\n      data: Partial<SalesTeamMember>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/users/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTeam', id },\n        'SalesTeam'\n      ],\n    }),\n\n    deleteSalesTeamMember: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/users/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['SalesTeam'],\n    }),\n\n    // Sales Tasks Management\n    getSalesTasks: builder.query<ApiResponse<SalesTask[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      priority?: string\n      type?: string\n      assignedTo?: string\n      search?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/tasks',\n        params,\n      }),\n      providesTags: ['SalesTask'],\n    }),\n\n    getSalesTask: builder.query<ApiResponse<SalesTask>, string>({\n      query: (id) => `/sales/tasks/${id}`,\n      providesTags: (result, error, id) => [{ type: 'SalesTask', id }],\n    }),\n\n    createSalesTask: builder.mutation<ApiResponse<SalesTask>, Partial<SalesTask>>({\n      query: (data) => ({\n        url: '/sales/tasks',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTask'],\n    }),\n\n    updateSalesTask: builder.mutation<ApiResponse<SalesTask>, {\n      id: string\n      data: Partial<SalesTask>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/tasks/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTask', id },\n        'SalesTask'\n      ],\n    }),\n\n    completeSalesTask: builder.mutation<ApiResponse<SalesTask>, string>({\n      query: (id) => ({\n        url: `/sales/tasks/${id}/complete`,\n        method: 'POST',\n      }),\n      invalidatesTags: (result, error, id) => [\n        { type: 'SalesTask', id },\n        'SalesTask'\n      ],\n    }),\n\n    deleteSalesTask: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/sales/tasks/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['SalesTask'],\n    }),\n\n    // Sales Targets Management\n    getSalesTargets: builder.query<ApiResponse<SalesTarget[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      period?: string\n      salesRepId?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/targets',\n        params,\n      }),\n      providesTags: ['SalesTarget'],\n    }),\n\n    createSalesTarget: builder.mutation<ApiResponse<SalesTarget>, Partial<SalesTarget>>({\n      query: (data) => ({\n        url: '/sales/targets',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['SalesTarget'],\n    }),\n\n    updateSalesTarget: builder.mutation<ApiResponse<SalesTarget>, {\n      id: string\n      data: Partial<SalesTarget>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/targets/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'SalesTarget', id },\n        'SalesTarget'\n      ],\n    }),\n\n    // Commissions Management\n    getCommissions: builder.query<ApiResponse<Commission[]>, {\n      page?: number\n      limit?: number\n      status?: string\n      earnerId?: string\n      startDate?: string\n      endDate?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/commissions',\n        params,\n      }),\n      providesTags: ['Commission'],\n    }),\n\n    approveCommission: builder.mutation<ApiResponse<Commission>, string>({\n      query: (id) => ({\n        url: `/sales/commissions/${id}/approve`,\n        method: 'POST',\n      }),\n      invalidatesTags: ['Commission'],\n    }),\n\n    rejectCommission: builder.mutation<ApiResponse<Commission>, {\n      id: string\n      reason: string\n    }>({\n      query: ({ id, reason }) => ({\n        url: `/sales/commissions/${id}/reject`,\n        method: 'POST',\n        body: { reason },\n      }),\n      invalidatesTags: ['Commission'],\n    }),\n\n    // Sales Calendar Management\n    getCalendarEvents: builder.query<ApiResponse<SalesCalendarEvent[]>, {\n      startDate: string\n      endDate: string\n      type?: string\n      attendeeId?: string\n    }>({\n      query: (params) => ({\n        url: '/sales/calendar/events',\n        params,\n      }),\n      providesTags: ['Calendar'],\n    }),\n\n    createCalendarEvent: builder.mutation<ApiResponse<SalesCalendarEvent>, Partial<SalesCalendarEvent>>({\n      query: (data) => ({\n        url: '/sales/calendar/events',\n        method: 'POST',\n        body: data,\n      }),\n      invalidatesTags: ['Calendar'],\n    }),\n\n    updateCalendarEvent: builder.mutation<ApiResponse<SalesCalendarEvent>, {\n      id: string\n      data: Partial<SalesCalendarEvent>\n    }>({\n      query: ({ id, data }) => ({\n        url: `/sales/calendar/events/${id}`,\n        method: 'PUT',\n        body: data,\n      }),\n      invalidatesTags: (result, error, { id }) => [\n        { type: 'Calendar', id },\n        'Calendar'\n      ],\n    }),\n\n    deleteCalendarEvent: builder.mutation<ApiResponse<void>, string>({\n      query: (id) => ({\n        url: `/sales/calendar/events/${id}`,\n        method: 'DELETE',\n      }),\n      invalidatesTags: ['Calendar'],\n    }),\n  }),\n})\n\nexport const {\n  useGetSalesStatsQuery,\n  useGetSalesTeamQuery,\n  useGetSalesTeamMemberQuery,\n  useCreateSalesTeamMemberMutation,\n  useUpdateSalesTeamMemberMutation,\n  useDeleteSalesTeamMemberMutation,\n  useGetSalesTasksQuery,\n  useGetSalesTaskQuery,\n  useCreateSalesTaskMutation,\n  useUpdateSalesTaskMutation,\n  useCompleteSalesTaskMutation,\n  useDeleteSalesTaskMutation,\n  useGetSalesTargetsQuery,\n  useCreateSalesTargetMutation,\n  useUpdateSalesTargetMutation,\n  useGetCommissionsQuery,\n  useApproveCommissionMutation,\n  useRejectCommissionMutation,\n  useGetCalendarEventsQuery,\n  useCreateCalendarEventMutation,\n  useUpdateCalendarEventMutation,\n  useDeleteCalendarEventMutation,\n} = salesApi\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAwJO,MAAM,WAAW,iIAAA,CAAA,UAAO,CAAC,eAAe,CAAC;IAC9C,WAAW,CAAC,UAAY,CAAC;YACvB,cAAc;YACd,eAAe,QAAQ,KAAK,CAAgC;gBAC1D,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAQ;YACzB;YAEA,wBAAwB;YACxB,cAAc,QAAQ,KAAK,CAMxB;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;YAEA,oBAAoB,QAAQ,KAAK,CAAuC;gBACtE,OAAO,CAAC,KAAO,CAAC,OAAO,EAAE,IAAI;gBAC7B,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAa;wBAAG;qBAAE;YAClE;YAEA,uBAAuB,QAAQ,QAAQ,CAAyD;gBAC9F,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,uBAAuB,QAAQ,QAAQ,CAGpC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,uBAAuB,QAAQ,QAAQ,CAA4B;gBACjE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,OAAO,EAAE,IAAI;wBACnB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,yBAAyB;YACzB,eAAe,QAAQ,KAAK,CAQzB;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAY;YAC7B;YAEA,cAAc,QAAQ,KAAK,CAAiC;gBAC1D,OAAO,CAAC,KAAO,CAAC,aAAa,EAAE,IAAI;gBACnC,cAAc,CAAC,QAAQ,OAAO,KAAO;wBAAC;4BAAE,MAAM;4BAAa;wBAAG;qBAAE;YAClE;YAEA,iBAAiB,QAAQ,QAAQ,CAA6C;gBAC5E,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,iBAAiB,QAAQ,QAAQ,CAG9B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,mBAAmB,QAAQ,QAAQ,CAAiC;gBAClE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC;wBAClC,QAAQ;oBACV,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,KAAO;wBACtC;4BAAE,MAAM;4BAAa;wBAAG;wBACxB;qBACD;YACH;YAEA,iBAAiB,QAAQ,QAAQ,CAA4B;gBAC3D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,aAAa,EAAE,IAAI;wBACzB,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,2BAA2B;YAC3B,iBAAiB,QAAQ,KAAK,CAM3B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAc;YAC/B;YAEA,mBAAmB,QAAQ,QAAQ,CAAiD;gBAClF,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAc;YAClC;YAEA,mBAAmB,QAAQ,QAAQ,CAGhC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,eAAe,EAAE,IAAI;wBAC3B,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAe;wBAAG;wBAC1B;qBACD;YACH;YAEA,yBAAyB;YACzB,gBAAgB,QAAQ,KAAK,CAO1B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAa;YAC9B;YAEA,mBAAmB,QAAQ,QAAQ,CAAkC;gBACnE,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,mBAAmB,EAAE,GAAG,QAAQ,CAAC;wBACvC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAa;YACjC;YAEA,kBAAkB,QAAQ,QAAQ,CAG/B;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAK,CAAC;wBAC1B,KAAK,CAAC,mBAAmB,EAAE,GAAG,OAAO,CAAC;wBACtC,QAAQ;wBACR,MAAM;4BAAE;wBAAO;oBACjB,CAAC;gBACD,iBAAiB;oBAAC;iBAAa;YACjC;YAEA,4BAA4B;YAC5B,mBAAmB,QAAQ,KAAK,CAK7B;gBACD,OAAO,CAAC,SAAW,CAAC;wBAClB,KAAK;wBACL;oBACF,CAAC;gBACD,cAAc;oBAAC;iBAAW;YAC5B;YAEA,qBAAqB,QAAQ,QAAQ,CAA+D;gBAClG,OAAO,CAAC,OAAS,CAAC;wBAChB,KAAK;wBACL,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;YAEA,qBAAqB,QAAQ,QAAQ,CAGlC;gBACD,OAAO,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,GAAK,CAAC;wBACxB,KAAK,CAAC,uBAAuB,EAAE,IAAI;wBACnC,QAAQ;wBACR,MAAM;oBACR,CAAC;gBACD,iBAAiB,CAAC,QAAQ,OAAO,EAAE,EAAE,EAAE,GAAK;wBAC1C;4BAAE,MAAM;4BAAY;wBAAG;wBACvB;qBACD;YACH;YAEA,qBAAqB,QAAQ,QAAQ,CAA4B;gBAC/D,OAAO,CAAC,KAAO,CAAC;wBACd,KAAK,CAAC,uBAAuB,EAAE,IAAI;wBACnC,QAAQ;oBACV,CAAC;gBACD,iBAAiB;oBAAC;iBAAW;YAC/B;QACF,CAAC;AACH;AAEO,MAAM,EACX,qBAAqB,EACrB,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,gCAAgC,EAChC,gCAAgC,EAChC,qBAAqB,EACrB,oBAAoB,EACpB,0BAA0B,EAC1B,0BAA0B,EAC1B,4BAA4B,EAC5B,0BAA0B,EAC1B,uBAAuB,EACvB,4BAA4B,EAC5B,4BAA4B,EAC5B,sBAAsB,EACtB,4BAA4B,EAC5B,2BAA2B,EAC3B,yBAAyB,EACzB,8BAA8B,EAC9B,8BAA8B,EAC9B,8BAA8B,EAC/B,GAAG", "debugId": null}}, {"offset": {"line": 2579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/store/api/index.ts"], "sourcesContent": ["// Export all API hooks and types for easy importing\n\n// Base API\nexport { baseApi } from './baseApi'\nexport type { ApiResponse, BaseQueryParams } from './baseApi'\n\n// Users API\nexport * from './usersApi'\n\n// Properties API - selective exports to avoid conflicts\nexport {\n  useGetPropertiesQuery,\n  useGetPropertiesStatsQuery, // Fixed: this exists in propertiesApi\n  useGetPropertyByIdQuery,\n  useCreatePropertyMutation,\n  useUpdatePropertyMutation,\n  useDeletePropertyMutation,\n  useBulkUpdatePropertiesMutation,\n  useExportPropertiesMutation,\n  useFeaturePropertyMutation,\n  useTogglePropertyFeaturedMutation,\n  useGetPresignedUrlMutation,\n  useConfirmFileUploadMutation,\n  useDeletePropertyFileMutation,\n  useAddPropertyOwnerMutation,\n  useRemovePropertyOwnerMutation,\n  useGetPropertyOwnersQuery,\n  useCreatePropertyStocksMutation,\n  useLazyGetPropertyByIdQuery,\n  // Note: useGetPropertyStocksQuery from propertiesApi is excluded to avoid conflict\n} from './propertiesApi'\n\n// Property Stocks API - selective exports to avoid conflicts\nexport {\n  useCreateStockMutation,\n  useGetPropertyStocksQuery, // This is the main one we want from propertyStocksApi\n  useGetPropertyStockByIdQuery,\n  useUpdatePropertyStockMutation,\n  useDeletePropertyStockMutation,\n  useGetStockTransactionsQuery,\n  useGetStockInvestorsQuery,\n} from './propertyStocksApi'\n\n// Admin KYC API\nexport * from './adminKycApi'\n\n// User Details API\nexport * from './userDetailsApi'\n\n// Leads API\nexport * from './leadsApi'\n\n// Dashboard API\nexport * from './dashboardApi'\n\n// Finance API\nexport * from './financeApi'\n\n// Stocks API - selective exports to avoid conflicts\nexport {\n  useGetStocksQuery,\n  useUpdateStockReturnsMutation,\n  useTransferStockMutation,\n  useSellStockMutation\n} from './stocksApi'\n\n// Property Owners API - selective exports to avoid conflicts\nexport {\n  useGetPropertyOwnersQuery as useGetPropertyOwnersListQuery,\n  useCreatePropertyOwnerMutation,\n  useUpdatePropertyOwnerMutation,\n  useVerifyPropertyOwnerMutation,\n  useGetPropertyOwnersStatsQuery,\n  useLinkPropertyToOwnerMutation,\n  useUnlinkPropertyFromOwnerMutation,\n  useSearchPropertyOwnersQuery\n} from './propertyOwnersApi'\n\n// Support API\nexport * from './supportApi'\n\n// Settings API\nexport * from './settingsApi'\n\n// Sales API\nexport * from './salesApi'\n\n// Analytics API\nexport * from './analyticsApi'\n\n// Wishlist API\nexport * from './wishlistApi'\n\n// Re-export common types\nexport type {\n  User,\n  Property,\n  Lead,\n  UserRole,\n  UserStatus,\n  PropertyStatus,\n  LeadStatus,\n  TransactionStatus,\n  TransactionType,\n} from '@/types'\n\n// Re-export Transaction from financeApi to avoid conflicts\nexport type { Transaction } from './financeApi'\n"], "names": [], "mappings": "AAAA,oDAAoD;AAEpD,WAAW;;AACX;AAGA,YAAY;AACZ;AAEA,wDAAwD;AACxD;AAsBA,6DAA6D;AAC7D;AAUA,gBAAgB;AAChB;AAEA,mBAAmB;AACnB;AAEA,YAAY;AACZ;AAEA,gBAAgB;AAChB;AAEA,cAAc;AACd;AAEA,oDAAoD;AACpD;AAOA,6DAA6D;AAC7D;AAWA,cAAc;AACd;AAEA,eAAe;AACf;AAEA,YAAY;AACZ;AAEA,gBAAgB;AAChB;AAEA,eAAe;AACf", "debugId": null}}, {"offset": {"line": 2662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/web-projects/builder/admin/src/app/properties/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAppSelector } from '@/store'\nimport { selectIsAuthenticated, selectUser } from '@/store/slices/authSlice'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Progress } from '@/components/ui/progress'\nimport { toast } from 'sonner'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\nimport {\n  useGetPropertiesQuery,\n  useDeletePropertyMutation,\n  useGetUsersQuery,\n  useTogglePropertyFeaturedMutation\n} from '@/store/api'\nimport { UserRole } from '@/types'\nimport { \n  Building,\n  Search,\n  Filter,\n  Download,\n  Upload,\n  Eye,\n  Edit,\n  MapPin,\n  TrendingUp,\n  CheckCircle,\n  Plus,\n  Home,\n  Building2,\n  DollarSign,\n  Users,\n  Trash2,\n  Star,\n  Calendar,\n  Target,\n  Award,\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight\n} from 'lucide-react'\n\nexport default function PropertiesPage() {\n  const router = useRouter()\n  const isAuthenticated = useAppSelector(selectIsAuthenticated)\n  const currentUser = useAppSelector(selectUser)\n\n  // State management\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedFilter, setSelectedFilter] = useState('all')\n  const [selectedProperties, setSelectedProperties] = useState<string[]>([])\n  const [currentPage, setCurrentPage] = useState(1)\n  const [pageSize, setPageSize] = useState(9)\n\n  // Global fallback images for properties (using reliable sources)\n  const fallbackImages = [\n    'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n    'https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',\n    'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'\n  ]\n\n  // API queries\n  const { data: propertiesResponse, isLoading, refetch } = useGetPropertiesQuery({\n    page: 1, // Get all properties for client-side pagination\n    limit: 1000, // Large limit to get all properties\n    ...(searchQuery && { search: searchQuery }),\n    ...(selectedFilter !== 'all' && { status: selectedFilter })\n  })\n\n  const { data: usersResponse } = useGetUsersQuery({})\n\n  // Mutations\n  const [deleteProperty] = useDeletePropertyMutation()\n  const [togglePropertyFeatured] = useTogglePropertyFeaturedMutation()\n\n  // Authentication check\n  useEffect(() => {\n    if (!isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, router])\n\n  const canManageProperties = currentUser?.role === UserRole.ADMIN || currentUser?.role === UserRole.SUBADMIN\n\n  if (!isAuthenticated || !canManageProperties) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"bg-white p-8 rounded-lg shadow-lg\">\n            <h1 className=\"text-2xl font-bold mb-4 text-gray-900\">Access Denied</h1>\n            <p className=\"text-gray-600 mb-4\">You don't have permission to access property management.</p>\n            <Button onClick={() => router.push('/dashboard')} className=\"bg-emerald-600 hover:bg-emerald-700\">\n              Go to Dashboard\n            </Button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // Extract data safely\n  const properties = Array.isArray(propertiesResponse?.data) ? propertiesResponse.data : []\n  const users = Array.isArray(usersResponse?.data) ? usersResponse.data : []\n\n  // Calculate analytics with proper data structure\n  const totalProperties = properties.length\n  const totalValue = properties.reduce((sum, prop: any) => {\n    const stockData = prop.stockInfo || {}\n    const price = stockData.stockPrice || 0\n    const stocks = stockData.totalStocks || 0\n    return sum + (price * stocks)\n  }, 0)\n  const totalStocks = properties.reduce((sum, prop: any) => {\n    const stockData = prop.stockInfo || {}\n    return sum + (stockData.totalStocks || 0)\n  }, 0)\n  const soldStocks = properties.reduce((sum, prop: any) => {\n    const stockData = prop.stockInfo || {}\n    return sum + (stockData.stocksSold || 0)\n  }, 0)\n  const occupancyRate = totalStocks > 0 ? ((soldStocks / totalStocks) * 100).toFixed(1) : '0'\n\n  // Filter properties\n  const filteredProperties = properties.filter((property: any) => {\n    const matchesSearch = !searchQuery ||\n                         property.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         property.location?.city?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         property.location?.address?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         property.location?.state?.toLowerCase().includes(searchQuery.toLowerCase())\n\n    const stockData = property.stockInfo || {}\n    const availableStocks = stockData.availableStocks || 0\n    const matchesFilter = selectedFilter === 'all' ||\n                         (selectedFilter === 'available' && availableStocks > 0) ||\n                         (selectedFilter === 'sold_out' && availableStocks === 0) ||\n                         (selectedFilter === 'coming_soon' && property.status === 'coming_soon')\n\n    return matchesSearch && matchesFilter\n  })\n\n  // Pagination\n  const totalPages = Math.ceil(filteredProperties.length / pageSize)\n  const startIndex = (currentPage - 1) * pageSize\n  const endIndex = startIndex + pageSize\n  const paginatedProperties = filteredProperties.slice(startIndex, endIndex)\n\n  // Helper functions\n  const formatCurrency = (amount: number) => {\n    if (amount >= 10000000) {\n      return `₹${(amount / 10000000).toFixed(1)}Cr`\n    } else if (amount >= 100000) {\n      return `₹${(amount / 100000).toFixed(1)}L`\n    } else {\n      return `₹${amount.toLocaleString()}`\n    }\n  }\n\n  const getTypeBadge = (type: string) => {\n    switch (type) {\n      case 'Residential':\n        return <Badge className=\"bg-purple-100 text-purple-800 border-purple-200\"><Home className=\"h-3 w-3 mr-1\" />Residential</Badge>\n      case 'Commercial':\n        return <Badge className=\"bg-orange-100 text-orange-800 border-orange-200\"><Building2 className=\"h-3 w-3 mr-1\" />Commercial</Badge>\n      default:\n        return <Badge variant=\"outline\">{type}</Badge>\n    }\n  }\n\n  const handlePropertySelect = (propertyId: string) => {\n    setSelectedProperties(prev => \n      prev.includes(propertyId) \n        ? prev.filter(id => id !== propertyId)\n        : [...prev, propertyId]\n    )\n  }\n\n  const handleSelectAllProperties = (checked: boolean) => {\n    if (checked) {\n      setSelectedProperties(paginatedProperties.map(p => p.id))\n    } else {\n      setSelectedProperties([])\n    }\n  }\n\n  const handleDeleteProperty = async (propertyId: string) => {\n    if (!confirm('Are you sure you want to delete this property? This action cannot be undone.')) return\n    try {\n      await deleteProperty(propertyId).unwrap()\n      toast.success('Property deleted successfully!')\n      refetch()\n    } catch (error) {\n      toast.error('Failed to delete property')\n      console.error('Delete property error:', error)\n    }\n  }\n\n  // Handle featured toggle\n  const handleToggleFeatured = async (propertyId: string, currentFeatured: boolean) => {\n    try {\n      await togglePropertyFeatured({\n        id: propertyId,\n        featured: !currentFeatured\n      }).unwrap()\n\n      toast.success(`Property ${!currentFeatured ? 'featured' : 'unfeatured'} successfully`)\n      refetch()\n    } catch (error) {\n      toast.error('Failed to update featured status')\n      console.error('Toggle featured error:', error)\n    }\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6 space-y-6\">\n        {/* Beautiful Header with Gradient */}\n        <div className=\"bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-lg p-6 text-white\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h1 className=\"text-3xl font-bold mb-2 flex items-center gap-3\">\n                <Building className=\"h-8 w-8\" />\n                Property Management\n              </h1>\n              <p className=\"text-emerald-100\">Manage properties, stocks, and real estate investments with advanced analytics</p>\n            </div>\n            \n            <div className=\"flex items-center gap-3\">\n              <Button \n                variant=\"secondary\"\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export Data\n              </Button>\n              \n              <Button \n                variant=\"secondary\"\n                className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              >\n                <Upload className=\"h-4 w-4 mr-2\" />\n                Import Properties\n              </Button>\n              \n              <Button \n                onClick={() => router.push('/properties/add')}\n                className=\"bg-yellow-500 hover:bg-yellow-600 text-black font-semibold\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Add Property\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Advanced Property Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-emerald-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Properties</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{totalProperties}</p>\n                  <p className=\"text-xs text-green-600 mt-1 flex items-center\">\n                    <TrendingUp className=\"h-3 w-3 mr-1\" />\n                    +12% from last month\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-emerald-100\">\n                  <Building className=\"h-6 w-6 text-emerald-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"border-blue-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Portfolio Value</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{formatCurrency(totalValue)}</p>\n                  <p className=\"text-xs text-blue-600 mt-1 flex items-center\">\n                    <DollarSign className=\"h-3 w-3 mr-1\" />\n                    Total investment\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-blue-100\">\n                  <DollarSign className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"border-purple-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Stocks</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{totalStocks.toLocaleString()}</p>\n                  <p className=\"text-xs text-purple-600 mt-1 flex items-center\">\n                    <Target className=\"h-3 w-3 mr-1\" />\n                    Available units\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-purple-100\">\n                  <Building2 className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card className=\"border-orange-200 hover:shadow-lg transition-all duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Occupancy Rate</p>\n                  <p className=\"text-3xl font-bold text-gray-900\">{occupancyRate}%</p>\n                  <p className=\"text-xs text-orange-600 mt-1 flex items-center\">\n                    <CheckCircle className=\"h-3 w-3 mr-1\" />\n                    {soldStocks.toLocaleString()} sold\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-orange-100\">\n                  <CheckCircle className=\"h-6 w-6 text-orange-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Bulk Actions Section */}\n        {selectedProperties.length > 0 && (\n          <Card className=\"border-emerald-200 bg-emerald-50\">\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center gap-3\">\n                  <CheckCircle className=\"h-5 w-5 text-emerald-600\" />\n                  <span className=\"font-medium text-emerald-800\">\n                    {selectedProperties.length} properties selected\n                  </span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"bg-emerald-100 border-emerald-300 text-emerald-700 hover:bg-emerald-200\"\n                  >\n                    <Users className=\"h-4 w-4 mr-2\" />\n                    Bulk Actions\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setSelectedProperties([])}\n                    className=\"border-gray-300 text-gray-600 hover:bg-gray-50\"\n                  >\n                    Clear Selection\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        )}\n\n        {/* Advanced Filters & Search */}\n        <Card className=\"border-emerald-200\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center text-emerald-700\">\n              <Filter className=\"h-5 w-5 mr-2\" />\n              Property Filters & Search\n            </CardTitle>\n            <CardDescription>\n              Filter and search through your property portfolio with advanced options\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6\">\n              {/* Search */}\n              <div className=\"lg:col-span-2\">\n                <Label className=\"text-sm font-medium mb-2 block\">Search Properties</Label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <Input\n                    placeholder=\"Search by name, location, or address...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"pl-10 border-emerald-200 focus:border-emerald-500\"\n                  />\n                </div>\n              </div>\n\n              {/* Status Filter */}\n              <div>\n                <Label className=\"text-sm font-medium mb-2 block\">Property Status</Label>\n                <Select value={selectedFilter} onValueChange={setSelectedFilter}>\n                  <SelectTrigger className=\"border-emerald-200 focus:border-emerald-500\">\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All Properties</SelectItem>\n                    <SelectItem value=\"available\">Available</SelectItem>\n                    <SelectItem value=\"sold_out\">Sold Out</SelectItem>\n                    <SelectItem value=\"coming_soon\">Coming Soon</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Quick Actions */}\n              <div>\n                <Label className=\"text-sm font-medium mb-2 block\">Quick Actions</Label>\n                <Button\n                  className=\"w-full bg-emerald-600 hover:bg-emerald-700 text-white\"\n                  onClick={() => router.push('/properties/add')}\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Property\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Properties Grid */}\n        <Card className=\"border-emerald-200\">\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle className=\"flex items-center text-emerald-700\">\n                  <Building className=\"h-5 w-5 mr-2\" />\n                  Property Portfolio\n                </CardTitle>\n                <CardDescription>\n                  {filteredProperties.length} of {totalProperties} properties\n                </CardDescription>\n              </div>\n              <div className=\"flex items-center gap-2\">\n                <Checkbox\n                  checked={selectedProperties.length === paginatedProperties.length && paginatedProperties.length > 0}\n                  onCheckedChange={handleSelectAllProperties}\n                />\n                <span className=\"text-sm text-gray-600\">Select All</span>\n              </div>\n            </div>\n          </CardHeader>\n          <CardContent>\n            {isLoading ? (\n              <div className=\"flex items-center justify-center py-12\">\n                <div className=\"text-center\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-3\"></div>\n                  <p className=\"text-gray-600\">Loading properties...</p>\n                </div>\n              </div>\n            ) : filteredProperties.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Building className=\"h-12 w-12 text-gray-300 mx-auto mb-4\" />\n                <p className=\"text-gray-500 mb-2\">No properties found</p>\n                <p className=\"text-sm text-gray-400 mb-4\">Start by adding your first property to the portfolio</p>\n                <Button\n                  onClick={() => router.push('/properties/add')}\n                  className=\"bg-emerald-600 hover:bg-emerald-700 text-white\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Add Your First Property\n                </Button>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {paginatedProperties.map((property: any, index) => {\n                  // Get real stock data from PropertyStock model\n                  const stockData = property.stockInfo || {}\n                  const totalStocks = stockData.totalStocks || 1000\n                  const stocksSold = stockData.stocksSold || 0\n                  const stocksReserved = stockData.stocksReserved || 0\n                  const availableStocks = stockData.availableStocks || (totalStocks - stocksSold - stocksReserved)\n                  const soldPercentage = stockData.soldPercentage || (totalStocks > 0 ? (stocksSold / totalStocks) * 100 : 0)\n                  const pricePerStock = stockData.stockPrice || 1000\n                  const expectedReturns = property.expectedReturns || 12\n                  const totalRevenue = stockData.totalRevenue || (stocksSold * pricePerStock)\n                  const riskLevel = stockData.riskLevel || 'low'\n\n                  // Get property image - handle both string and object formats\n                  const getPropertyImage = () => {\n                    if (property.images && property.images.length > 0) {\n                      const firstImage = property.images[0]\n                      if (typeof firstImage === 'string') {\n                        return firstImage\n                      } else if (firstImage && firstImage.url) {\n                        return firstImage.url\n                      }\n                    }\n                    return fallbackImages[index % fallbackImages.length]\n                  }\n\n                  return (\n                    <Card\n                      key={property.id}\n                      className={`border-emerald-200 hover:shadow-xl hover:border-emerald-300 transition-all duration-300 transform hover:-translate-y-1 ${\n                        selectedProperties.includes(property.id)\n                          ? 'ring-2 ring-emerald-500 bg-emerald-50/50 shadow-lg'\n                          : 'hover:bg-white/80'\n                      }`}\n                    >\n                      <CardContent className=\"p-6\">\n                        {/* Enhanced Property Header */}\n                        <div className=\"flex items-start justify-between mb-4\">\n                          <div className=\"flex items-start gap-3\">\n                            <Checkbox\n                              checked={selectedProperties.includes(property.id)}\n                              onCheckedChange={() => handlePropertySelect(property.id)}\n                              className=\"mt-1\"\n                            />\n                            <div className=\"flex-1\">\n                              <h3 className=\"text-lg font-bold text-gray-900 line-clamp-1 mb-1\">\n                                {property.name}\n                              </h3>\n                              <div className=\"flex items-center gap-2 mb-2\">\n                                <div className=\"flex items-center gap-1 text-sm text-gray-600\">\n                                  <MapPin className=\"h-3 w-3 text-emerald-600\" />\n                                  <span>{property.location?.city || 'Location not specified'}</span>\n                                </div>\n                                {property.location?.state && (\n                                  <Badge variant=\"outline\" className=\"text-xs\">\n                                    {property.location.state}\n                                  </Badge>\n                                )}\n                              </div>\n                              <div className=\"flex items-center gap-2 text-xs text-gray-500\">\n                                <Calendar className=\"h-3 w-3\" />\n                                <span>Added {new Date(property.createdAt).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex flex-col gap-2\">\n                            {getTypeBadge(property.propertyType || 'Residential')}\n                            <Badge className={`${\n                              pricePerStock > 100000\n                                ? 'bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 border-yellow-300'\n                                : 'bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border-blue-300'\n                            }`}>\n                              <Star className=\"h-3 w-3 mr-1\" />\n                              {pricePerStock > 100000 ? 'Premium' : 'Standard'}\n                            </Badge>\n                            {riskLevel && (\n                              <Badge className={`${\n                                riskLevel === 'low' ? 'bg-green-100 text-green-800' :\n                                riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                                'bg-red-100 text-red-800'\n                              }`}>\n                                {riskLevel.charAt(0).toUpperCase() + riskLevel.slice(1)} Risk\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n\n                        {/* Property Image */}\n                        <div className=\"relative mb-4 rounded-lg overflow-hidden group\">\n                          <img\n                            src={getPropertyImage()}\n                            alt={property.name || 'Property Image'}\n                            className=\"w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105\"\n                            onError={(e) => {\n                              // Fallback to next image if current one fails\n                              const target = e.target as HTMLImageElement\n                              const currentIndex = fallbackImages.findIndex(img => img === target.src)\n                              const nextIndex = (currentIndex + 1) % fallbackImages.length\n                              target.src = fallbackImages[nextIndex] || fallbackImages[0] || ''\n                            }}\n                          />\n                          {/* Image Overlay */}\n                          <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n                          {/* Status Badge */}\n                          <div className=\"absolute top-3 right-3\">\n                            <Badge className={`${\n                              availableStocks > 0\n                                ? 'bg-green-500 text-white border-green-400 shadow-lg'\n                                : 'bg-red-500 text-white border-red-400 shadow-lg'\n                            }`}>\n                              {availableStocks > 0 ? `${availableStocks} Available` : '✗ Sold Out'}\n                            </Badge>\n                          </div>\n\n                          {/* Quick Info Overlay */}\n                          <div className=\"absolute bottom-3 left-3 right-3 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                            <div className=\"bg-black/70 backdrop-blur-sm rounded-lg p-3\">\n                              <div className=\"flex items-center justify-between text-sm\">\n                                <span className=\"font-medium\">Total Value</span>\n                                <span className=\"font-bold\">{formatCurrency(totalStocks * pricePerStock)}</span>\n                              </div>\n                              {totalRevenue > 0 && (\n                                <div className=\"flex items-center justify-between text-sm mt-1\">\n                                  <span className=\"font-medium\">Revenue</span>\n                                  <span className=\"font-bold text-green-600\">{formatCurrency(totalRevenue)}</span>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Property Stats */}\n                        <div className=\"grid grid-cols-2 gap-3 mb-4\">\n                          <div className=\"bg-gradient-to-br from-emerald-50 to-emerald-100 p-4 rounded-xl border border-emerald-200 hover:shadow-md transition-all duration-200\">\n                            <div className=\"flex items-center justify-between\">\n                              <div>\n                                <p className=\"text-xs font-medium text-emerald-700 uppercase tracking-wide\">Total Stocks</p>\n                                <p className=\"text-xl font-bold text-emerald-900\">{totalStocks.toLocaleString()}</p>\n                              </div>\n                              <Building2 className=\"h-6 w-6 text-emerald-600\" />\n                            </div>\n                          </div>\n                          <div className=\"bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200 hover:shadow-md transition-all duration-200\">\n                            <div className=\"flex items-center justify-between\">\n                              <div>\n                                <p className=\"text-xs font-medium text-blue-700 uppercase tracking-wide\">Available</p>\n                                <p className=\"text-xl font-bold text-blue-900\">{availableStocks.toLocaleString()}</p>\n                              </div>\n                              <CheckCircle className=\"h-6 w-6 text-blue-600\" />\n                            </div>\n                          </div>\n                          <div className=\"bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200 hover:shadow-md transition-all duration-200\">\n                            <div className=\"flex items-center justify-between\">\n                              <div>\n                                <p className=\"text-xs font-medium text-purple-700 uppercase tracking-wide\">Price/Stock</p>\n                                <p className=\"text-lg font-bold text-purple-900\">{formatCurrency(pricePerStock)}</p>\n                              </div>\n                              <DollarSign className=\"h-6 w-6 text-purple-600\" />\n                            </div>\n                          </div>\n                          <div className=\"bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200 hover:shadow-md transition-all duration-200\">\n                            <div className=\"flex items-center justify-between\">\n                              <div>\n                                <p className=\"text-xs font-medium text-orange-700 uppercase tracking-wide\">Returns</p>\n                                <p className=\"text-xl font-bold text-orange-900\">{expectedReturns}%</p>\n                              </div>\n                              <TrendingUp className=\"h-6 w-6 text-orange-600\" />\n                            </div>\n                          </div>\n                        </div>\n\n                        {/* Enhanced Progress Bar */}\n                        <div className=\"mb-6 bg-gray-50 p-4 rounded-xl border border-gray-200\">\n                          <div className=\"flex items-center justify-between mb-3\">\n                            <div className=\"flex items-center gap-2\">\n                              <Target className=\"h-4 w-4 text-gray-600\" />\n                              <span className=\"text-sm font-semibold text-gray-700\">Sales Progress</span>\n                            </div>\n                            <div className=\"flex items-center gap-2\">\n                              <span className=\"text-lg font-bold text-gray-900\">{soldPercentage.toFixed(1)}%</span>\n                              <Badge className={`${\n                                soldPercentage >= 80 ? 'bg-green-100 text-green-800' :\n                                soldPercentage >= 50 ? 'bg-yellow-100 text-yellow-800' :\n                                'bg-red-100 text-red-800'\n                              }`}>\n                                {soldPercentage >= 80 ? 'Excellent' : soldPercentage >= 50 ? 'Good' : 'Low'}\n                              </Badge>\n                            </div>\n                          </div>\n                          <Progress value={soldPercentage} className=\"h-3 bg-gray-200\" />\n                          <div className=\"flex justify-between text-xs text-gray-500 mt-2\">\n                            <span>{soldStocks.toLocaleString()} sold</span>\n                            <span>{availableStocks.toLocaleString()} remaining</span>\n                          </div>\n                        </div>\n\n                        {/* Enhanced Action Buttons */}\n                        <div className=\"flex items-center gap-2 pt-2\">\n                          <Button\n                            size=\"sm\"\n                            onClick={() => router.push(`/properties/${property.id}`)}\n                            className=\"flex-1 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white shadow-md hover:shadow-lg transition-all duration-200\"\n                          >\n                            <Eye className=\"h-4 w-4 mr-2\" />\n                            View Details\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => handleToggleFeatured(property.id, property.featured || false)}\n                            className={`${\n                              property.featured\n                                ? 'border-yellow-300 text-yellow-700 bg-yellow-50 hover:bg-yellow-100'\n                                : 'border-gray-300 text-gray-700 hover:bg-gray-50'\n                            } transition-all duration-200`}\n                            title={property.featured ? 'Remove from featured' : 'Add to featured'}\n                          >\n                            <Star className={`h-4 w-4 ${property.featured ? 'fill-yellow-400 text-yellow-400' : ''}`} />\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => router.push(`/properties/${property.id}/edit`)}\n                            className=\"border-emerald-300 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-400 transition-all duration-200\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => handleDeleteProperty(property.id)}\n                            className=\"border-red-300 text-red-700 hover:bg-red-50 hover:border-red-400 transition-all duration-200\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  )\n                })}\n              </div>\n            )}\n\n            {/* Pagination Controls */}\n            {totalPages > 1 && (\n              <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n                <div className=\"flex items-center gap-2\">\n                  <Label className=\"text-sm font-medium\">Show:</Label>\n                  <Select value={pageSize.toString()} onValueChange={(value) => {\n                    setPageSize(parseInt(value))\n                    setCurrentPage(1)\n                  }}>\n                    <SelectTrigger className=\"w-20 border-emerald-200\">\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"6\">6</SelectItem>\n                      <SelectItem value=\"9\">9</SelectItem>\n                      <SelectItem value=\"12\">12</SelectItem>\n                      <SelectItem value=\"18\">18</SelectItem>\n                    </SelectContent>\n                  </Select>\n                  <span className=\"text-sm text-gray-600\">\n                    Showing {startIndex + 1}-{Math.min(endIndex, filteredProperties.length)} of {filteredProperties.length} properties\n                  </span>\n                </div>\n\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(1)}\n                    disabled={currentPage === 1}\n                    className=\"border-emerald-200 text-emerald-600 hover:bg-emerald-50\"\n                  >\n                    <ChevronsLeft className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(currentPage - 1)}\n                    disabled={currentPage === 1}\n                    className=\"border-emerald-200 text-emerald-600 hover:bg-emerald-50\"\n                  >\n                    <ChevronLeft className=\"h-4 w-4\" />\n                  </Button>\n\n                  <div className=\"flex items-center gap-1\">\n                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                      let pageNum\n                      if (totalPages <= 5) {\n                        pageNum = i + 1\n                      } else if (currentPage <= 3) {\n                        pageNum = i + 1\n                      } else if (currentPage >= totalPages - 2) {\n                        pageNum = totalPages - 4 + i\n                      } else {\n                        pageNum = currentPage - 2 + i\n                      }\n\n                      return (\n                        <Button\n                          key={pageNum}\n                          variant={currentPage === pageNum ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setCurrentPage(pageNum)}\n                          className={currentPage === pageNum\n                            ? \"bg-emerald-600 hover:bg-emerald-700 text-white\"\n                            : \"border-emerald-200 text-emerald-600 hover:bg-emerald-50\"\n                          }\n                        >\n                          {pageNum}\n                        </Button>\n                      )\n                    })}\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(currentPage + 1)}\n                    disabled={currentPage === totalPages}\n                    className=\"border-emerald-200 text-emerald-600 hover:bg-emerald-50\"\n                  >\n                    <ChevronRight className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(totalPages)}\n                    disabled={currentPage === totalPages}\n                    className=\"border-emerald-200 text-emerald-600 hover:bg-emerald-50\"\n                  >\n                    <ChevronsRight className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAvBA;;;;;;;;;;;;;;;;;;AAkDe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,wBAAqB;IAC5D,MAAM,cAAc,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,sIAAA,CAAA,aAAU;IAE7C,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,iEAAiE;IACjE,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;KACD;IAED,cAAc;IACd,MAAM,EAAE,MAAM,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,wBAAqB,AAAD,EAAE;QAC7E,MAAM;QACN,OAAO;QACP,GAAI,eAAe;YAAE,QAAQ;QAAY,CAAC;QAC1C,GAAI,mBAAmB,SAAS;YAAE,QAAQ;QAAe,CAAC;IAC5D;IAEA,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,CAAC;IAElD,YAAY;IACZ,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,uIAAA,CAAA,4BAAyB,AAAD;IACjD,MAAM,CAAC,uBAAuB,GAAG,CAAA,GAAA,uIAAA,CAAA,oCAAiC,AAAD;IAEjE,uBAAuB;IACvB,CAAA,GAAA,4RAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;mCAAG;QAAC;QAAiB;KAAO;IAE5B,MAAM,sBAAsB,aAAa,SAAS,wHAAA,CAAA,WAAQ,CAAC,KAAK,IAAI,aAAa,SAAS,wHAAA,CAAA,WAAQ,CAAC,QAAQ;IAE3G,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;QAC5C,qBACE,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;0BACb,cAAA,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,4TAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,4TAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAAe,WAAU;sCAAsC;;;;;;;;;;;;;;;;;;;;;;IAO5G;IAEA,sBAAsB;IACtB,MAAM,aAAa,MAAM,OAAO,CAAC,oBAAoB,QAAQ,mBAAmB,IAAI,GAAG,EAAE;IACzF,MAAM,QAAQ,MAAM,OAAO,CAAC,eAAe,QAAQ,cAAc,IAAI,GAAG,EAAE;IAE1E,iDAAiD;IACjD,MAAM,kBAAkB,WAAW,MAAM;IACzC,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,SAAS,IAAI,CAAC;QACrC,MAAM,QAAQ,UAAU,UAAU,IAAI;QACtC,MAAM,SAAS,UAAU,WAAW,IAAI;QACxC,OAAO,MAAO,QAAQ;IACxB,GAAG;IACH,MAAM,cAAc,WAAW,MAAM,CAAC,CAAC,KAAK;QAC1C,MAAM,YAAY,KAAK,SAAS,IAAI,CAAC;QACrC,OAAO,MAAM,CAAC,UAAU,WAAW,IAAI,CAAC;IAC1C,GAAG;IACH,MAAM,aAAa,WAAW,MAAM,CAAC,CAAC,KAAK;QACzC,MAAM,YAAY,KAAK,SAAS,IAAI,CAAC;QACrC,OAAO,MAAM,CAAC,UAAU,UAAU,IAAI,CAAC;IACzC,GAAG;IACH,MAAM,gBAAgB,cAAc,IAAI,CAAC,AAAC,aAAa,cAAe,GAAG,EAAE,OAAO,CAAC,KAAK;IAExF,oBAAoB;IACpB,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAC;QAC5C,MAAM,gBAAgB,CAAC,eACF,SAAS,IAAI,EAAE,cAAc,SAAS,YAAY,WAAW,OAC7D,SAAS,QAAQ,EAAE,MAAM,cAAc,SAAS,YAAY,WAAW,OACvE,SAAS,QAAQ,EAAE,SAAS,cAAc,SAAS,YAAY,WAAW,OAC1E,SAAS,QAAQ,EAAE,OAAO,cAAc,SAAS,YAAY,WAAW;QAE7F,MAAM,YAAY,SAAS,SAAS,IAAI,CAAC;QACzC,MAAM,kBAAkB,UAAU,eAAe,IAAI;QACrD,MAAM,gBAAgB,mBAAmB,SACnB,mBAAmB,eAAe,kBAAkB,KACpD,mBAAmB,cAAc,oBAAoB,KACrD,mBAAmB,iBAAiB,SAAS,MAAM,KAAK;QAE9E,OAAO,iBAAiB;IAC1B;IAEA,aAAa;IACb,MAAM,aAAa,KAAK,IAAI,CAAC,mBAAmB,MAAM,GAAG;IACzD,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,sBAAsB,mBAAmB,KAAK,CAAC,YAAY;IAEjE,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,UAAU;YACtB,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,QAAQ,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QAC/C,OAAO,IAAI,UAAU,QAAQ;YAC3B,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5C,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,OAAO,cAAc,IAAI;QACtC;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAAkD,4TAAC,0RAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAC7G,KAAK;gBACH,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAAkD,4TAAC,uSAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAClH;gBACE,qBAAO,4TAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB,CAAA,OACpB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,MAAM,4BAA4B,CAAC;QACjC,IAAI,SAAS;YACX,sBAAsB,oBAAoB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QACzD,OAAO;YACL,sBAAsB,EAAE;QAC1B;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,QAAQ,iFAAiF;QAC9F,IAAI;YACF,MAAM,eAAe,YAAY,MAAM;YACvC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,OAAO,YAAoB;QACtD,IAAI;YACF,MAAM,uBAAuB;gBAC3B,IAAI;gBACJ,UAAU,CAAC;YACb,GAAG,MAAM;YAET,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,kBAAkB,aAAa,aAAa,aAAa,CAAC;YACrF;QACF,EAAE,OAAO,OAAO;YACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,qBACE,4TAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,4TAAC;YAAI,WAAU;;8BAEb,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;;0DACZ,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,4TAAC;wCAAE,WAAU;kDAAmB;;;;;;;;;;;;0CAGlC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIvC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;;0DAEV,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;8BAQzC,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI3C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM5B,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,eAAe;;;;;;8DAChE,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,ySAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAI3C,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,ySAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;8DAAoC,YAAY,cAAc;;;;;;8DAC3E,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,6RAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;sDAIvC,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,uSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7B,4TAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;;8DACC,4TAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,4TAAC;oDAAE,WAAU;;wDAAoC;wDAAc;;;;;;;8DAC/D,4TAAC;oDAAE,WAAU;;sEACX,4TAAC,kTAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACtB,WAAW,cAAc;wDAAG;;;;;;;;;;;;;sDAGjC,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,kTAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQhC,mBAAmB,MAAM,GAAG,mBAC3B,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,4TAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,kTAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,4TAAC;4CAAK,WAAU;;gDACb,mBAAmB,MAAM;gDAAC;;;;;;;;;;;;;8CAG/B,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,4TAAC,2RAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,sBAAsB,EAAE;4CACvC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUX,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,4TAAC,mIAAA,CAAA,aAAU;;8CACT,4TAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,4TAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,4TAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,4TAAC;gCAAI,WAAU;;kDAEb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC;gDAAI,WAAU;;kEACb,4TAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,4TAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,4TAAC;;0DACC,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAgB,eAAe;;kEAC5C,4TAAC,qIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,4TAAC,qIAAA,CAAA,gBAAa;;0EACZ,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAY;;;;;;0EAC9B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;0EAC7B,4TAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;kDAMtC,4TAAC;;0DACC,4TAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAiC;;;;;;0DAClD,4TAAC,qIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC;;kEAE3B,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS3C,4TAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,4TAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;;0DACC,4TAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,4TAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,4TAAC,mIAAA,CAAA,kBAAe;;oDACb,mBAAmB,MAAM;oDAAC;oDAAK;oDAAgB;;;;;;;;;;;;;kDAGpD,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,uIAAA,CAAA,WAAQ;gDACP,SAAS,mBAAmB,MAAM,KAAK,oBAAoB,MAAM,IAAI,oBAAoB,MAAM,GAAG;gDAClG,iBAAiB;;;;;;0DAEnB,4TAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAI9C,4TAAC,mIAAA,CAAA,cAAW;;gCACT,0BACC,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAI,WAAU;;;;;;0DACf,4TAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;2CAG/B,mBAAmB,MAAM,KAAK,kBAChC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,iSAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,4TAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,4TAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;;8DAEV,4TAAC,yRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;yDAKrC,4TAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,UAAe;wCACvC,+CAA+C;wCAC/C,MAAM,YAAY,SAAS,SAAS,IAAI,CAAC;wCACzC,MAAM,cAAc,UAAU,WAAW,IAAI;wCAC7C,MAAM,aAAa,UAAU,UAAU,IAAI;wCAC3C,MAAM,iBAAiB,UAAU,cAAc,IAAI;wCACnD,MAAM,kBAAkB,UAAU,eAAe,IAAK,cAAc,aAAa;wCACjF,MAAM,iBAAiB,UAAU,cAAc,IAAI,CAAC,cAAc,IAAI,AAAC,aAAa,cAAe,MAAM,CAAC;wCAC1G,MAAM,gBAAgB,UAAU,UAAU,IAAI;wCAC9C,MAAM,kBAAkB,SAAS,eAAe,IAAI;wCACpD,MAAM,eAAe,UAAU,YAAY,IAAK,aAAa;wCAC7D,MAAM,YAAY,UAAU,SAAS,IAAI;wCAEzC,6DAA6D;wCAC7D,MAAM,mBAAmB;4CACvB,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,GAAG;gDACjD,MAAM,aAAa,SAAS,MAAM,CAAC,EAAE;gDACrC,IAAI,OAAO,eAAe,UAAU;oDAClC,OAAO;gDACT,OAAO,IAAI,cAAc,WAAW,GAAG,EAAE;oDACvC,OAAO,WAAW,GAAG;gDACvB;4CACF;4CACA,OAAO,cAAc,CAAC,QAAQ,eAAe,MAAM,CAAC;wCACtD;wCAEA,qBACE,4TAAC,mIAAA,CAAA,OAAI;4CAEH,WAAW,CAAC,uHAAuH,EACjI,mBAAmB,QAAQ,CAAC,SAAS,EAAE,IACnC,uDACA,qBACJ;sDAEF,cAAA,4TAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEAErB,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC,uIAAA,CAAA,WAAQ;wEACP,SAAS,mBAAmB,QAAQ,CAAC,SAAS,EAAE;wEAChD,iBAAiB,IAAM,qBAAqB,SAAS,EAAE;wEACvD,WAAU;;;;;;kFAEZ,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAG,WAAU;0FACX,SAAS,IAAI;;;;;;0FAEhB,4TAAC;gFAAI,WAAU;;kGACb,4TAAC;wFAAI,WAAU;;0GACb,4TAAC,iSAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;0GAClB,4TAAC;0GAAM,SAAS,QAAQ,EAAE,QAAQ;;;;;;;;;;;;oFAEnC,SAAS,QAAQ,EAAE,uBAClB,4TAAC,oIAAA,CAAA,QAAK;wFAAC,SAAQ;wFAAU,WAAU;kGAChC,SAAS,QAAQ,CAAC,KAAK;;;;;;;;;;;;0FAI9B,4TAAC;gFAAI,WAAU;;kGACb,4TAAC,iSAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;kGACpB,4TAAC;;4FAAK;4FAAO,IAAI,KAAK,SAAS,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0EAIlE,4TAAC;gEAAI,WAAU;;oEACZ,aAAa,SAAS,YAAY,IAAI;kFACvC,4TAAC,oIAAA,CAAA,QAAK;wEAAC,WAAW,GAChB,gBAAgB,SACZ,qFACA,4EACJ;;0FACA,4TAAC,yRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EACf,gBAAgB,SAAS,YAAY;;;;;;;oEAEvC,2BACC,4TAAC,oIAAA,CAAA,QAAK;wEAAC,WAAW,GAChB,cAAc,QAAQ,gCACtB,cAAc,WAAW,kCACzB,2BACA;;4EACC,UAAU,MAAM,CAAC,GAAG,WAAW,KAAK,UAAU,KAAK,CAAC;4EAAG;;;;;;;;;;;;;;;;;;;kEAOhE,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEACC,KAAK;gEACL,KAAK,SAAS,IAAI,IAAI;gEACtB,WAAU;gEACV,SAAS,CAAC;oEACR,8CAA8C;oEAC9C,MAAM,SAAS,EAAE,MAAM;oEACvB,MAAM,eAAe,eAAe,SAAS,CAAC,CAAA,MAAO,QAAQ,OAAO,GAAG;oEACvE,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,eAAe,MAAM;oEAC5D,OAAO,GAAG,GAAG,cAAc,CAAC,UAAU,IAAI,cAAc,CAAC,EAAE,IAAI;gEACjE;;;;;;0EAGF,4TAAC;gEAAI,WAAU;;;;;;0EAGf,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC,oIAAA,CAAA,QAAK;oEAAC,WAAW,GAChB,kBAAkB,IACd,uDACA,kDACJ;8EACC,kBAAkB,IAAI,GAAG,gBAAgB,UAAU,CAAC,GAAG;;;;;;;;;;;0EAK5D,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAK,WAAU;8FAAc;;;;;;8FAC9B,4TAAC;oFAAK,WAAU;8FAAa,eAAe,cAAc;;;;;;;;;;;;wEAE3D,eAAe,mBACd,4TAAC;4EAAI,WAAU;;8FACb,4TAAC;oFAAK,WAAU;8FAAc;;;;;;8FAC9B,4TAAC;oFAAK,WAAU;8FAA4B,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAQrE,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;;8FACC,4TAAC;oFAAE,WAAU;8FAA+D;;;;;;8FAC5E,4TAAC;oFAAE,WAAU;8FAAsC,YAAY,cAAc;;;;;;;;;;;;sFAE/E,4TAAC,uSAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAGzB,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;;8FACC,4TAAC;oFAAE,WAAU;8FAA4D;;;;;;8FACzE,4TAAC;oFAAE,WAAU;8FAAmC,gBAAgB,cAAc;;;;;;;;;;;;sFAEhF,4TAAC,kTAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAG3B,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;;8FACC,4TAAC;oFAAE,WAAU;8FAA8D;;;;;;8FAC3E,4TAAC;oFAAE,WAAU;8FAAqC,eAAe;;;;;;;;;;;;sFAEnE,4TAAC,ySAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAG1B,4TAAC;gEAAI,WAAU;0EACb,cAAA,4TAAC;oEAAI,WAAU;;sFACb,4TAAC;;8FACC,4TAAC;oFAAE,WAAU;8FAA8D;;;;;;8FAC3E,4TAAC;oFAAE,WAAU;;wFAAqC;wFAAgB;;;;;;;;;;;;;sFAEpE,4TAAC,ySAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAM5B,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;wEAAI,WAAU;;0FACb,4TAAC,6RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,4TAAC;gFAAK,WAAU;0FAAsC;;;;;;;;;;;;kFAExD,4TAAC;wEAAI,WAAU;;0FACb,4TAAC;gFAAK,WAAU;;oFAAmC,eAAe,OAAO,CAAC;oFAAG;;;;;;;0FAC7E,4TAAC,oIAAA,CAAA,QAAK;gFAAC,WAAW,GAChB,kBAAkB,KAAK,gCACvB,kBAAkB,KAAK,kCACvB,2BACA;0FACC,kBAAkB,KAAK,cAAc,kBAAkB,KAAK,SAAS;;;;;;;;;;;;;;;;;;0EAI5E,4TAAC,uIAAA,CAAA,WAAQ;gEAAC,OAAO;gEAAgB,WAAU;;;;;;0EAC3C,4TAAC;gEAAI,WAAU;;kFACb,4TAAC;;4EAAM,WAAW,cAAc;4EAAG;;;;;;;kFACnC,4TAAC;;4EAAM,gBAAgB,cAAc;4EAAG;;;;;;;;;;;;;;;;;;;kEAK5C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;gEACvD,WAAU;;kFAEV,4TAAC,uRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,qBAAqB,SAAS,EAAE,EAAE,SAAS,QAAQ,IAAI;gEACtE,WAAW,GACT,SAAS,QAAQ,GACb,uEACA,iDACL,4BAA4B,CAAC;gEAC9B,OAAO,SAAS,QAAQ,GAAG,yBAAyB;0EAEpD,cAAA,4TAAC,yRAAA,CAAA,OAAI;oEAAC,WAAW,CAAC,QAAQ,EAAE,SAAS,QAAQ,GAAG,oCAAoC,IAAI;;;;;;;;;;;0EAE1F,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;gEAC5D,WAAU;0EAEV,cAAA,4TAAC,kSAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,4TAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,SAAS,IAAM,qBAAqB,SAAS,EAAE;gEAC/C,WAAU;0EAEV,cAAA,4TAAC,iSAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2CA9MnB,SAAS,EAAE;;;;;oCAoNtB;;;;;;gCAKH,aAAa,mBACZ,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,oIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,4TAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO,SAAS,QAAQ;oDAAI,eAAe,CAAC;wDAClD,YAAY,SAAS;wDACrB,eAAe;oDACjB;;sEACE,4TAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sEAEd,4TAAC,qIAAA,CAAA,gBAAa;;8EACZ,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAI;;;;;;8EACtB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAI;;;;;;8EACtB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;8EACvB,4TAAC,qIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAK;;;;;;;;;;;;;;;;;;8DAG3B,4TAAC;oDAAK,WAAU;;wDAAwB;wDAC7B,aAAa;wDAAE;wDAAE,KAAK,GAAG,CAAC,UAAU,mBAAmB,MAAM;wDAAE;wDAAK,mBAAmB,MAAM;wDAAC;;;;;;;;;;;;;sDAI3G,4TAAC;4CAAI,WAAU;;8DACb,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe;oDAC9B,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,6SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,cAAc;oDAC5C,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,2SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAGzB,4TAAC;oDAAI,WAAU;8DACZ,MAAM,IAAI,CAAC;wDAAE,QAAQ,KAAK,GAAG,CAAC,GAAG;oDAAY,GAAG,CAAC,GAAG;wDACnD,IAAI;wDACJ,IAAI,cAAc,GAAG;4DACnB,UAAU,IAAI;wDAChB,OAAO,IAAI,eAAe,GAAG;4DAC3B,UAAU,IAAI;wDAChB,OAAO,IAAI,eAAe,aAAa,GAAG;4DACxC,UAAU,aAAa,IAAI;wDAC7B,OAAO;4DACL,UAAU,cAAc,IAAI;wDAC9B;wDAEA,qBACE,4TAAC,qIAAA,CAAA,SAAM;4DAEL,SAAS,gBAAgB,UAAU,YAAY;4DAC/C,MAAK;4DACL,SAAS,IAAM,eAAe;4DAC9B,WAAW,gBAAgB,UACvB,mDACA;sEAGH;2DATI;;;;;oDAYX;;;;;;8DAGF,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe,cAAc;oDAC5C,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,6SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,4TAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,eAAe;oDAC9B,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,4TAAC,+SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7C;GAlwBwB;;QACP,oQAAA,CAAA,YAAS;QACA,wHAAA,CAAA,iBAAc;QAClB,wHAAA,CAAA,iBAAc;QAmBuB,uIAAA,CAAA,wBAAqB;QAO9C,kIAAA,CAAA,mBAAgB;QAGvB,uIAAA,CAAA,4BAAyB;QACjB,uIAAA,CAAA,oCAAiC;;;KAjC5C", "debugId": null}}]}