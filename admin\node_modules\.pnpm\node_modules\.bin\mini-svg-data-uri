#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri/node_modules:/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules:/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules/mini-svg-data-uri/node_modules:/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/mini-svg-data-uri@1.4.4/node_modules:/mnt/c/Users/<USER>/Downloads/web-projects/builder/admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mini-svg-data-uri/cli.js" "$@"
else
  exec node  "$basedir/../mini-svg-data-uri/cli.js" "$@"
fi
