## [6.0.3](https://github.com/nfroidure/svg-pathdata/compare/v6.0.2...v6.0.3) (2021-09-18)



# [6.0.2](https://github.com/nfroidure/svg-pathdata/compare/v6.0.1...v6.0.2) (2021-09-17)


# [6.0.1](https://github.com/nfroidure/svg-pathdata/compare/v6.0.0...v6.0.1) (2021-09-12)


# [6.0.0](https://github.com/nfroidure/svg-pathdata/compare/v5.0.5...v6.0.0) (2021-04-02)


## [5.0.5](https://github.com/nfroidure/svg-pathdata/compare/v5.0.4...v5.0.5) (2020-06-06)



## [5.0.4](https://github.com/nfroidure/svg-pathdata/compare/v4.0.0...v5.0.4) (2020-02-14)


### Bug Fixes

* **parser:** fix parsing merged numbers with arc flags ([d55d9f0](https://github.com/nfroidure/svg-pathdata/commit/d55d9f0db00c1bb341eecaea786ca05dd2c51e95))



## [5.0.3](https://github.com/nfroidure/svg-pathdata/compare/v4.0.0...v5.0.3) (2020-02-04)



<a name="5.0.2"></a>
## [5.0.2](https://github.com/nfroidure/svg-pathdata/compare/v5.0.1...v5.0.2) (2018-06-05)



<a name="5.0.1"></a>
## [5.0.1](https://github.com/nfroidure/svg-pathdata/compare/v5.0.0...v5.0.1) (2018-06-03)



<a name="5.0.0"></a>
# [5.0.0](https://github.com/nfroidure/svg-pathdata/compare/v4.0.0...v5.0.0) (2018-06-02)



<a name="4.0.1"></a>
## [4.0.1](https://github.com/nfroidure/svg-pathdata/compare/v4.0.0...v4.0.1) (2017-08-22)



<a name="4.0.0"></a>
# [4.0.0](https://github.com/nfroidure/svg-pathdata/compare/v3.2.3...v4.0.0) (2017-08-22)



<a name="3.2.3"></a>
## [3.2.3](https://github.com/nfroidure/svg-pathdata/compare/v3.2.2...v3.2.3) (2017-08-13)



<a name="3.2.2"></a>
## [3.2.2](https://github.com/nfroidure/svg-pathdata/compare/v3.2.1...v3.2.2) (2017-08-13)



<a name="3.2.1"></a>
## [3.2.1](https://github.com/nfroidure/svg-pathdata/compare/v3.2.0...v3.2.1) (2017-08-13)



<a name="3.2.0"></a>
# [3.2.0](https://github.com/nfroidure/svg-pathdata/compare/v3.1.1...v3.2.0) (2017-08-12)



<a name="3.1.1"></a>
## [3.1.1](https://github.com/nfroidure/svg-pathdata/compare/v3.1.0...v3.1.1) (2017-08-12)



<a name="3.1.0"></a>
# [3.1.0](https://github.com/nfroidure/SVGPathData/compare/v3.0.0...v3.1.0) (2017-05-19)



<a name="3.0.0"></a>
# [3.0.0](https://github.com/nfroidure/SVGPathData/compare/v2.0.3...v3.0.0) (2017-05-02)



<a name="2.0.3"></a>
## [2.0.3](https://github.com/nfroidure/SVGPathData/compare/v2.0.2...v2.0.3) (2017-04-21)



<a name="2.0.2"></a>
## [2.0.2](https://github.com/nfroidure/SVGPathData/compare/v2.0.1...v2.0.2) (2017-04-05)


### Bug Fixes

* **build:** Fix tests and regenerate the changelog ([fa281fa](https://github.com/nfroidure/SVGPathData/commit/fa281fa))


### build

* **metapak-nfroidure:** Add metapak-nfroidure ([6be898c](https://github.com/nfroidure/SVGPathData/commit/6be898c))


### Code Refactoring

* **dist:** No more build ([8ce46da](https://github.com/nfroidure/SVGPathData/commit/8ce46da))


### BREAKING CHANGES

* **dist:** No more dist, please transpile in your own builds.
* **metapak-nfroidure:** Since the eslint --fix option were used, there may be some breaking features but
this very old codebase really need a total cleanup.



### v2.0.0 (2017/02/22 08:23 +00:00)
- [8a33721](https://github.com/nfroidure/SVGPathData/commit/8a33721a08ee1cf837ebf41699c6ab93648ad998) 2.0.0 (@NaridaL)
- [8c840ab](https://github.com/nfroidure/SVGPathData/commit/8c840ab66ee30139921a9d7d75c3f042d422e97a) Changed x1/y1 and x2/y2 around for C and c commands. (@NaridaL)
- [#20](https://github.com/nfroidure/SVGPathData/pull/20) Normalization and Sanitation Transformers (@NaridaL)
- [214f5ee](https://github.com/nfroidure/SVGPathData/commit/214f5ee4718792c17ef703ab4c34e3c0a2b6dfe0) Added transformers: (@NaridaL)

### v1.0.4 (2016/11/07 15:18 +00:00)
- [bb3beb7](https://github.com/nfroidure/SVGPathData/commit/bb3beb7fe18cf933254dd02ca22677bc95e4c993) 1.0.4 (@huerlisi)
- [#13](https://github.com/nfroidure/SVGPathData/pull/13) Ignore the coverage/ directory in git (@nfroidure)
- [#14](https://github.com/nfroidure/SVGPathData/pull/14) Use master branch travis badge in README (@nfroidure)
- [56cbd18](https://github.com/nfroidure/SVGPathData/commit/56cbd186d27a21fce3c21fddc3399e26e985e99a) Use master branch travis badge in README (@huerlisi)
- [5293f0b](https://github.com/nfroidure/SVGPathData/commit/5293f0bce88e4a2a18bc4e3679f4b3f53d9c86b6) Ignore the coverage/ directory in git (@huerlisi)
- [#9](https://github.com/nfroidure/SVGPathData/pull/9) Support transforming arc commands (@rhendric)
- [#11](https://github.com/nfroidure/SVGPathData/pull/11) Moveto commands start a new subpath (@rhendric)
- [#10](https://github.com/nfroidure/SVGPathData/pull/10) Remove Grunt references from Travis (@rhendric)
- [9906542](https://github.com/nfroidure/SVGPathData/commit/9906542f722e830e19ca401ccec1d33e6a8474ce) Support transforming arc commands (@rhendric)
- [25d1dc9](https://github.com/nfroidure/SVGPathData/commit/25d1dc90d2a66573e04b339d9139838625380b84) Fix test expectations (@rhendric)
- [c9e3747](https://github.com/nfroidure/SVGPathData/commit/c9e3747afdfd269a320e3ff9d474e83a56a2cecb) Remove Grunt references from Travis (@rhendric)
- [efc5f29](https://github.com/nfroidure/SVGPathData/commit/efc5f298340adb19374b967e11b40e09a892358f) Moveto commands start a new subpath (@rhendric)

### v1.0.3 (2015/11/20 20:08 +00:00)
- [d52943d](https://github.com/nfroidure/SVGPathData/commit/d52943d3aa4ef339f8e29fb321e5a23fb415c8c0) 1.0.3 (@nfroidure)
- [06fbcd1](https://github.com/nfroidure/SVGPathData/commit/06fbcd18a10ac0e4ed43fa2acb87e79ab0963355) Cannot use preversion lint right now, too much work (@nfroidure)
- [7d3d11e](https://github.com/nfroidure/SVGPathData/commit/7d3d11e4d9be6214d5e5a1da94761cb983ab9f80) Suppressing some linting errors (@nfroidure)
- [7337440](https://github.com/nfroidure/SVGPathData/commit/7337440e21037d83d66cfb051f60c2e7013c5c22) Updating deps (@nfroidure)
- [17a3934](https://github.com/nfroidure/SVGPathData/commit/17a39346a14b3aae7fca4240d818f16e5712bacb) Removing grunt (@nfroidure)
- [06d6a69](https://github.com/nfroidure/SVGPathData/commit/06d6a69abb42cdf01aff1ec40af50490c7e5c9c4) Adding eslint config file (@nfroidure)

### v1.0.2 (2015/10/10 10:57 +00:00)
- [3aa2cd6](https://github.com/nfroidure/SVGPathData/commit/3aa2cd603e9ef078b2394a5463dfcc6267fa1a21) 1.0.2 (@nfroidure)
- [179416d](https://github.com/nfroidure/SVGPathData/commit/179416d7ed7cbb7a5e4417a89b9528e865e31932) Fixing the arc reverted issue #44 (@nfroidure)

### v1.0.1 (2015/06/27 08:41 +00:00)
- [a8f1fb6](https://github.com/nfroidure/SVGPathData/commit/a8f1fb63ea3d1ace5038ec278911c50d807913cc) 1.0.1 (@nfroidure)
- [027ba05](https://github.com/nfroidure/SVGPathData/commit/027ba0540c2ee368df9b4adeb8e0c70a578264da) Suppressing hints (@nfroidure)
- [183ccd8](https://github.com/nfroidure/SVGPathData/commit/183ccd8b4dec15fa70a6bbcc8ed47d855689c83e) Cleaning up repo (@nfroidure)
- [7418a31](https://github.com/nfroidure/SVGPathData/commit/7418a3174c8b49e26e06229d7a0a06d0233afd81) Update deps, set the engines to Node > 0.10 (@nfroidure)

### v1.0.0 (2014/11/16 16:05 +00:00)
- [d043a52](https://github.com/nfroidure/SVGPathData/commit/d043a522d1e759a19e2b9a241772839f403a8f46) 1.0.0 (@nfroidure)
- [da9671b](https://github.com/nfroidure/SVGPathData/commit/da9671b5eed778a07b094e8a92d589e32b684943) Fixing rel 2 abs for multipath pathdatas (@nfroidure)
- [68d0e70](https://github.com/nfroidure/SVGPathData/commit/68d0e70a749de73c7d6d0279219b487b9f0e7d64) Adding the forgotten licence file closes #7 (@nfroidure)
- [2b7bd20](https://github.com/nfroidure/SVGPathData/commit/2b7bd200a3be95afb5cd9c534b08846f4edd7339) Updating version number (@nfroidure)
- [2b1e277](https://github.com/nfroidure/SVGPathData/commit/2b1e277a6e36f8fc6e5880d323b8534aa46dab56) Removing unecessary dependency (@nfroidure)
- [cf3f3e4](https://github.com/nfroidure/SVGPathData/commit/cf3f3e4ee2a0a9495eb7f1958a03d8037d488926) Do not use data event anymore (@nfroidure)
- [5cb5c80](https://github.com/nfroidure/SVGPathData/commit/5cb5c805a333d314641f2dbaa449385054e444f0) Improved transform functions (@nfroidure)
- [1f567e1](https://github.com/nfroidure/SVGPathData/commit/1f567e11c2d5e086dfa1689e6772eae2eeeef0d8) Improved transform function (@nfroidure)
- [4724965](https://github.com/nfroidure/SVGPathData/commit/47249651346f98e7ee30cab32579f6ac8edf3c2c) New version (@nfroidure)
- [2d726ec](https://github.com/nfroidure/SVGPathData/commit/2d726ecb8e2dcd3ac3d6006e53a84f82e8e03dff) Fucking grunt-browserify (@nfroidure)
- [3587d82](https://github.com/nfroidure/SVGPathData/commit/3587d82648965988f32053901c1669ac584c4621) Adding forgottent tests (@nfroidure)
- [5a29acb](https://github.com/nfroidure/SVGPathData/commit/5a29acb45605db5d715acedd4238a1e046bd3372) Removing errors that cannot happen (@nfroidure)
- [3425078](https://github.com/nfroidure/SVGPathData/commit/342507854c0ad5e1894108b7cf4a742e2c289e3e) Adding forgotten new operator for errors (@nfroidure)
- [54b7538](https://github.com/nfroidure/SVGPathData/commit/54b75386eecc0c518fb2f1dfb190bf91bfcb3ad3) No more EOF and STATE_ENDED for the parser (@nfroidure)
- [f2609e3](https://github.com/nfroidure/SVGPathData/commit/f2609e3dc78d801e1e112644b211e41ca246cbf7) Fixing encoder output to buffer modewq (@nfroidure)
- [2c03487](https://github.com/nfroidure/SVGPathData/commit/2c0348746bbacb4da7d2e97cd0e2d18fc5df5fd7) Improving code coverage, allowing constructors omission (@nfroidure)
- [237898e](https://github.com/nfroidure/SVGPathData/commit/237898e4cd1e9a762f836a265295a658c4a8f5f6) Updating dependencies (@nfroidure)
- [d35369c](https://github.com/nfroidure/SVGPathData/commit/d35369c12e93b6c20cbd9846f0eab15652ca034e) Fix matrix transformation formula closes #2 (@nfroidure)
- [e218385](https://github.com/nfroidure/SVGPathData/commit/e218385b4e2bb238231a5a86a05d0e604d125123) Fixing tests, improving A to C transformer (@nfroidure)
- [49fe80a](https://github.com/nfroidure/SVGPathData/commit/49fe80af60a4dedc68341466f86aa93d67d19882) New version 0.0.4 (@nfroidure)
- [a0e8a63](https://github.com/nfroidure/SVGPathData/commit/a0e8a63f160cc5dad0b4eb702dfb712785467f66) Added stats (@nfroidure)
- [#3](https://github.com/nfroidure/SVGPathData/pull/3) Depend on readable-stream from npm (@Raynos)
- [#4](https://github.com/nfroidure/SVGPathData/pull/4) clean up the scripts (@calvinmetcalf)
- [0c2cfde](https://github.com/nfroidure/SVGPathData/commit/0c2cfde170ca5f7621a4f6bc246f319aa5ae02d5) change | to && (@calvinmetcalf)
- [071215f](https://github.com/nfroidure/SVGPathData/commit/071215ff3d78db451e4aa2e39b8c9cee28b5668c) clean up the scripts (@calvinmetcalf)
- [19a4daa](https://github.com/nfroidure/SVGPathData/commit/19a4daaee107a25f9c422dc05c317c12808c94ca) convert other files to readable-stream too (@Raynos)
- [50dd97a](https://github.com/nfroidure/SVGPathData/commit/50dd97ab76f37c36ccf6519c2cd0ffd841ca0f98) depend on readable-stream in p.json (@Raynos)
- [181f63b](https://github.com/nfroidure/SVGPathData/commit/181f63b7eb1e79f80a258626da913123af5f73c3) Use readable-stream instead (@Raynos)
- [ce11d07](https://github.com/nfroidure/SVGPathData/commit/ce11d07e8d04ef446d4e49f83c582a88a1f49c68) Readme fix (@nfroidure)
- [c521987](https://github.com/nfroidure/SVGPathData/commit/c5219871629b8fafb8cea85a0ed158080af8acb0) Added coverage tests (@nfroidure)
- [11d22fc](https://github.com/nfroidure/SVGPathData/commit/11d22fc3360cd44e166e4a46935f94196ba19dce) Added a round transformation (@nfroidure)
- [0bf6fa4](https://github.com/nfroidure/SVGPathData/commit/0bf6fa4a20deba65e61b63d33a3e56ab7fdef675) Do not parse incomplete L,M,S incomplete commands anymore (@nfroidure)
- [653a4d3](https://github.com/nfroidure/SVGPathData/commit/653a4d3b214e13682aa0ecba80b71aaf069e540a) Improved sapegin test (@nfroidure)
- [60ee34e](https://github.com/nfroidure/SVGPathData/commit/60ee34ef8da8a798f2dfd7642555dcb71b83186e) Added sapegin test (@nfroidure)
- [908da2a](https://github.com/nfroidure/SVGPathData/commit/908da2a4ce2cf69990d73a267e7e124ca955008c) Added arc to curve conversionwq (@nfroidure)
- [b341fde](https://github.com/nfroidure/SVGPathData/commit/b341fdef1503ff0eb414a5b1bbcd9eb40cd75dbd) Main file is the src not the build (@nfroidure)
- [0d8608e](https://github.com/nfroidure/SVGPathData/commit/0d8608e9ead3fff8b22e37581098bdda7eb6dc99) Version updated (@nfroidure)
- [28a1cd1](https://github.com/nfroidure/SVGPathData/commit/28a1cd11205e8876d8a17112fcb5bd261a337685) Tests updated (@nfroidure)
- [63ac182](https://github.com/nfroidure/SVGPathData/commit/63ac182d17c8f99cf8cba8c978bfc05102832d1c) Fix for X/Y transforms (@nfroidure)
- [b4196ce](https://github.com/nfroidure/SVGPathData/commit/b4196ce375c43e1dd66659abf3b25e1c2f6e6d8e) Removed unnecessary absolute conversions (@nfroidure)
- [4a38ad0](https://github.com/nfroidure/SVGPathData/commit/4a38ad09fe38c32a97a790e88605b0a28efc3c92) Added x symetry (@nfroidure)
- [e34a0e3](https://github.com/nfroidure/SVGPathData/commit/e34a0e34ae7ccc1572920dc43e3c58c993885513) Based y symetry on other transformations (@nfroidure)
- [392152d](https://github.com/nfroidure/SVGPathData/commit/392152dc8aafcb3709d5c83915437e0c56638f51) Checking transformations arguments (@nfroidure)
- [177f586](https://github.com/nfroidure/SVGPathData/commit/177f5867e376fd8bcc6c8da77c8829c3296e07ee) Updated REAMDE and package.json (@nfroidure)
- [60a2819](https://github.com/nfroidure/SVGPathData/commit/60a2819bc2ce39c039f34169b58791195d73df56) Some transformations converted to their idioins matrixes (@nfroidure)
- [a24a2a5](https://github.com/nfroidure/SVGPathData/commit/a24a2a55efd1a5b740133967786cddc5cc6c4823) Skew transformations added2 (@nfroidure)
- [930b9c4](https://github.com/nfroidure/SVGPathData/commit/930b9c4f9a823b57a22a1f2061d4cd7c35a83c9f) Skew transformations added (@nfroidure)
- [7bc0390](https://github.com/nfroidure/SVGPathData/commit/7bc03902ffd6723f53f263d5ae875d727803f961) Rotation transformation added (@nfroidure)
- [3191815](https://github.com/nfroidure/SVGPathData/commit/31918152a99e30b856cd2c527aecb437d076cb41) Added scale transformation (@nfroidure)
- [0e15916](https://github.com/nfroidure/SVGPathData/commit/0e1591638e0c0e9da0de9f4de59f3cb0e68ff4c4) Added translation transformation (@nfroidure)
- [6cfd826](https://github.com/nfroidure/SVGPathData/commit/6cfd826478cbe52b100a7632027046dfca59fd5b) Fix for y symetry problems (@nfroidure)
- [7d0576c](https://github.com/nfroidure/SVGPathData/commit/7d0576c6f83c61d449fac26b67d6b54ee3ea7655) Support decpoint separated numbers 2 (@nfroidure)
- [92391ec](https://github.com/nfroidure/SVGPathData/commit/92391eca9f73a2007c3f155977a19a7d7dd56e0e) Support sign separated numbers 2 (@nfroidure)
- [5b536c1](https://github.com/nfroidure/SVGPathData/commit/5b536c17b9e754d5d8dd569422bb4b30d7a41739) Support sign separated numbers (@nfroidure)
- [d37ea0a](https://github.com/nfroidure/SVGPathData/commit/d37ea0aee50de8fc311d652dd76c43094d1dcfd5) Added ySymetry transformer2 (@nfroidure)
- [647fdf4](https://github.com/nfroidure/SVGPathData/commit/647fdf4ce9f99411d2433247af5bbdde32ea2759) Added ySymetry transformer (@nfroidure)
- [377518f](https://github.com/nfroidure/SVGPathData/commit/377518fc292a08ec6b292262f46454f7d91071b5) Added toRel transformer2 (@nfroidure)
- [dd00037](https://github.com/nfroidure/SVGPathData/commit/dd0003784e04086890f76a1abadd4d4871a8695e) Added toAbs transformer2 (@nfroidure)
- [d0d8332](https://github.com/nfroidure/SVGPathData/commit/d0d833242c9121dfe0a1f171ebea9f7744631958) Added toAbs transformer (@nfroidure)
- [b1e24a6](https://github.com/nfroidure/SVGPathData/commit/b1e24a63c4b3646e5af996b12c80abd53b4614cc) Now based on Node stream module (@nfroidure)
- [1bfbeea](https://github.com/nfroidure/SVGPathData/commit/1bfbeea6b429762477473b958e90739cb782b6aa) Updated dependencies (@nfroidure)
- [a267550](https://github.com/nfroidure/SVGPathData/commit/a267550c013538d23b2ba292d0f9358d950f4b8c) Added the distribution file (@nfroidure)
- [b8f0c7e](https://github.com/nfroidure/SVGPathData/commit/b8f0c7ee00367aac72177b689455d049c3ff2915) Added encoding use samples (@nfroidure)
- [bbd8955](https://github.com/nfroidure/SVGPathData/commit/bbd8955eb82ff57f1dd57cdb0f3cdff4868070a4) Switch to line to n+1 move to declarated commands (@nfroidure)
- [667f390](https://github.com/nfroidure/SVGPathData/commit/667f390209048f63e4e2cca3ae714e5821cbe70c) Added an encoder (@nfroidure)
- [78ef5ce](https://github.com/nfroidure/SVGPathData/commit/78ef5ce99598a6fc0edf9f83306f86e4ff9d3571) Contributing (@nfroidure)
- [c6d4db4](https://github.com/nfroidure/SVGPathData/commit/c6d4db4cc5879fdefd7697d2bba44b60f337e6e3) Added usage in the README file (@nfroidure)
- [bd69b1c](https://github.com/nfroidure/SVGPathData/commit/bd69b1ce492492d4b1b60d5e1d632558427bc1bd) Const naming uniformized (@nfroidure)
- [3b21f06](https://github.com/nfroidure/SVGPathData/commit/3b21f06e40c91a1761b5c5a86b5fb36d0044f690) New Grunt/Package files (@nfroidure)
- [f84959e](https://github.com/nfroidure/SVGPathData/commit/f84959e2833af79024ed356a5cdabbc331a5e028) Small refactoring (@nfroidure)
- [726e85f](https://github.com/nfroidure/SVGPathData/commit/726e85f575550ef13ff85a74ba312ee48fb2ac78) Added eliptic arc commands error detection tests (@nfroidure)
- [7bb6671](https://github.com/nfroidure/SVGPathData/commit/7bb6671b59ad35f73fa81d61cbc75f5424a099c0) Added eliptic arc commands (@nfroidure)
- [7099de1](https://github.com/nfroidure/SVGPathData/commit/7099de12fda674ad43f9200d1f92d81110dfe43f) Added smooth quadratic curve to commands (@nfroidure)
- [97c7575](https://github.com/nfroidure/SVGPathData/commit/97c7575823eb6f0a4215ba8c0b2387724ce75d5f) Added quadratic curve to commands (@nfroidure)
- [0499dcb](https://github.com/nfroidure/SVGPathData/commit/0499dcbe01caf609fb55e888376b2fd6aab14f00) Added curve to commands2 (@nfroidure)
- [fdb154e](https://github.com/nfroidure/SVGPathData/commit/fdb154e6fd07eab2b49737c32e2c14e2c9bd603a) Added curve to commands (@nfroidure)
- [111c42c](https://github.com/nfroidure/SVGPathData/commit/111c42cd3f4c115dfa93c171acc7fcc8bf605ea0) Better syntax error handling (@nfroidure)
- [52727ae](https://github.com/nfroidure/SVGPathData/commit/52727ae6303a7ca4c3f2817d6e41051ea679698b) Added smooth curveto commands (@nfroidure)
- [4fae609](https://github.com/nfroidure/SVGPathData/commit/4fae609bb17b1f866af6b47074762c9613d271c7) Added line to and closepath commands parsing (@nfroidure)
- [d7cdfad](https://github.com/nfroidure/SVGPathData/commit/d7cdfadf1024eb4d7bd51f6d6d487a4577b5a524) Move to commands parsing added (@nfroidure)
- [d10264e](https://github.com/nfroidure/SVGPathData/commit/d10264eda8d10b5200cffdfe8d08a239b4679c42) parse mthod returns commands (@nfroidure)
- [280ffe0](https://github.com/nfroidure/SVGPathData/commit/280ffe072cebe7abc2ec3bab2fb563e76a326bca) Build info (@nfroidure)
- [ac6b856](https://github.com/nfroidure/SVGPathData/commit/ac6b856c9c0338042ec10f45b0da9ce151d42411) Added vertical/horizontal commands2 (@nfroidure)
- [f785f6b](https://github.com/nfroidure/SVGPathData/commit/f785f6bf1a558caf1e60bb4d561b37139a082f61) Added vertical/horizontal commands (@nfroidure)
- [2f9b8da](https://github.com/nfroidure/SVGPathData/commit/2f9b8da11568d0079c101277ea8ec103e2434df1) Full support for numbers parsing (@nfroidure)
- [699bf87](https://github.com/nfroidure/SVGPathData/commit/699bf87bb27ed93b5075155308b48a1a574b015a) Fixed tests (@nfroidure)
- [04450cc](https://github.com/nfroidure/SVGPathData/commit/04450cc0938a5d883338362ceb167ca59e9d1c7f) First Commit (@nfroidure)
