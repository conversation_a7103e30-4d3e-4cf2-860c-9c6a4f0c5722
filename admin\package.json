{"name": "sgm-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 1730", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.848.0", "@aws-sdk/s3-request-presigner": "^3.848.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@reduxjs/toolkit": "^2.8.2", "@types/js-cookie": "^3.0.6", "@types/react-dropzone": "^5.1.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "need": "^0.0.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-redux": "^9.2.0", "recharts": "^3.0.2", "redux-persist": "^6.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}