'use client'

import React, { useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { toast } from 'sonner'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { useCreatePropertyMutation } from '@/store/api/propertiesApi'
import LocationPicker from '@/components/forms/property/LocationPicker'
import FinancialDetails from '@/components/forms/property/FinancialDetails'
import ConstructionTimeline from '@/components/forms/property/ConstructionTimeline'
import OwnerDeveloperSelector from '@/components/forms/property/OwnerDeveloperSelector'
import EnhancedFeaturesAmenities from '@/components/forms/property/EnhancedFeaturesAmenities'
import LegalDocumentsUpload from '@/components/forms/property/LegalDocumentsUpload'
import PropertyImagesUpload from '@/components/forms/property/PropertyImagesUpload'
import BookingForm from '@/components/forms/property/BookingForm'
import { PropertyImageFormData, LocationData, ConstructionData } from '@/types/shared'
// Simple form data interface
interface PropertyFormData {
  name: string
  description: string
  propertyType: string
  location: LocationData
  expectedReturns: number
  maturityPeriodMonths: number
  totalStocks: number
  pricePerStock: number
  constructionStatus: string
  launchDate: string
  expectedCompletion: string
  actualCompletion?: string
  constructionTimeline?: string
  ownerId?: string
  developerId?: string
  images: PropertyImageFormData[]
  documents?: any[]
  videos?: any[]
  legalDocuments?: any[]
  features?: string[]
  amenities?: string[]
  specifications?: any
  status?: string
  featured?: boolean
  priorityOrder?: number
  referralCommissionRate?: number
  salesCommissionRate?: number
  referralCommissionPerStock?: number
  salesCommissionPerStock?: number
  commissionType?: string
  [key: string]: any
}
import {
  ArrowLeft,
  ArrowRight,
  Save,
  Building,
  MapPin,
  DollarSign,
  Calendar,
  User,
  Upload,
  AlertCircle,
  FileText,
  CreditCard
} from 'lucide-react'

const propertyTypes = [
  { value: 'residential', label: 'Residential', icon: '🏠' },
  { value: 'commercial', label: 'Commercial', icon: '🏢' },
  { value: 'industrial', label: 'Industrial', icon: '🏭' },
  { value: 'land', label: 'Land', icon: '🌍' },
  { value: 'mixed', label: 'Mixed Use', icon: '🏘️' },
  { value: 'luxury', label: 'Luxury', icon: '✨' },
  { value: 'eco_friendly', label: 'Eco-Friendly', icon: '🌱' }
]

const amenitiesList = [
  // Basic Amenities
  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Playground',
  'Club House', 'Power Backup', 'Elevator', 'CCTV', 'Intercom', 'Water Supply',

  // Premium Amenities
  'Spa & Wellness Center', 'Rooftop Terrace', 'Business Center', 'Conference Room',
  'Library', 'Meditation Room', 'Yoga Studio', 'Indoor Games Room',
  'Outdoor Sports Court', 'Jogging Track', 'Cycling Track', 'Pet Park',

  // Safety & Security
  '24/7 Security', 'Gated Community', 'Fire Safety', 'Emergency Exit',
  'Smoke Detectors', 'Sprinkler System', 'Security Cameras', 'Access Control',

  // Convenience
  'Concierge Service', 'Housekeeping', 'Laundry Service', 'Valet Parking',
  'Car Wash', 'ATM', 'Shopping Center', 'Food Court',

  // Utilities
  'High Speed Internet', 'Cable TV', 'Intercom System', 'Video Door Phone',
  'Centralized Gas', 'Solar Power', 'Rainwater Harvesting', 'Waste Management',

  // Outdoor Features
  'Landscaped Gardens', 'Water Features', 'BBQ Area', 'Outdoor Seating',
  'Kids Play Area', 'Senior Citizen Area', 'Amphitheater', 'Gazebo'
]

const featuresList = [
  // Structural Features
  'Modular Kitchen', 'Walk-in Closet', 'Master Bedroom', 'Guest Room',
  'Study Room', 'Prayer Room', 'Servant Room', 'Store Room',
  'Balcony', 'Terrace', 'Basement', 'Attic',

  // Interior Features
  'Wooden Flooring', 'Marble Flooring', 'Vitrified Tiles', 'False Ceiling',
  'Designer Lighting', 'Built-in Wardrobes', 'Kitchen Cabinets', 'Granite Countertops',
  'Modular Switches', 'Premium Fittings', 'Designer Bathroom', 'Jacuzzi',

  // Technology Features
  'Smart Home Automation', 'Video Door Bell', 'Smart Locks', 'Motion Sensors',
  'Smart Lighting', 'Climate Control', 'Home Theater Setup', 'Sound System',

  // Energy Efficiency
  'Solar Panels', 'Energy Efficient Windows', 'LED Lighting', 'Insulation',
  'Double Glazed Windows', 'Ventilation System', 'Natural Lighting', 'Cross Ventilation',

  // Luxury Features
  'Private Pool', 'Private Garden', 'Private Elevator', 'Butler Service',
  'Wine Cellar', 'Home Office', 'Guest House', 'Driver Room',

  // Accessibility
  'Wheelchair Accessible', 'Ramp Access', 'Wide Doorways', 'Accessible Bathroom',
  'Emergency Call System', 'Braille Signage', 'Audio Visual Alerts', 'Grab Bars'
]



export default function AddEnhancedPropertyPage() {
  const router = useRouter()
  const [createProperty, { isLoading: isCreating }] = useCreatePropertyMutation()
  const [currentStep, setCurrentStep] = useState(1)
  const [errors, setErrors] = useState<any>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState<PropertyFormData>({
    // Basic Information
    name: '',
    description: '',
    propertyType: 'residential',
    
    // Location
    location: {
      address: '',
      city: '',
      state: '',
      country: 'India',
      pincode: '',
      coordinates: {
        latitude: 0,
        longitude: 0
      }
    },
    
    // Financial Details
    expectedReturns: 0,
    maturityPeriodMonths: 12,
    totalStocks: 100,
    pricePerStock: 1000,
    availableStocks: 100,
    stockPrefix: 'PROP',
    stockStartNumber: 1,
    referralCommissionRate: 2,
    salesCommissionRate: 1,
    referralCommissionPerStock: 20,
    salesCommissionPerStock: 10,
    commissionType: 'percentage',
    
    // Construction Details
    constructionStatus: 'planning',
    launchDate: '',
    expectedCompletion: '',
    actualCompletion: '',
    constructionTimeline: '',
    
    // Owner and Developer
    ownerId: '',
    developerId: '',
    
    // Property Features
    amenities: [],
    features: [],
    specifications: {},
    
    // Media Files
    images: [] as PropertyImageFormData[],
    documents: [],
    videos: [],
    legalDocuments: [],
    
    // Administrative
    status: 'active',
    featured: false,
    priorityOrder: 0
  })

  const steps = [
    { id: 1, title: 'Basic Information', shortTitle: 'Basic Info', icon: Building },
    { id: 2, title: 'Location Details', shortTitle: 'Location', icon: MapPin },
    { id: 3, title: 'Owner & Developer', shortTitle: 'Owner/Dev', icon: User },
    { id: 4, title: 'Financial Details', shortTitle: 'Financial', icon: DollarSign },
    { id: 5, title: 'Construction Timeline', shortTitle: 'Timeline', icon: Calendar },
    { id: 6, title: 'Property Images', shortTitle: 'Images', icon: Upload },
    { id: 7, title: 'Features & Amenities', shortTitle: 'Features', icon: Upload },
    { id: 8, title: 'Document Upload', shortTitle: 'Documents', icon: Upload },
    { id: 9, title: 'Booking Form', shortTitle: 'Booking', icon: CreditCard }
  ]

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const keys = field.split('.')
      setFormData(prev => {
        const updated = { ...prev }
        let current = updated as any
        for (let i = 0; i < keys.length - 1; i++) {
          const key = keys[i]
          if (key !== undefined) {
            current = current[key]
          }
        }
        const lastKey = keys[keys.length - 1]
        if (lastKey !== undefined) {
          current[lastKey] = value
        }
        return updated
      })
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
    
    // Clear error for this field
    if (errors[field]) {
      setErrors((prev: any) => ({
        ...prev,
        [field]: undefined
      }))
    }
  }

  const handleLocationChange = (location: any) => {
    setFormData(prev => ({
      ...prev,
      location
    }))
  }

  const handleFinancialChange = (financial: any) => {
    setFormData(prev => ({
      ...prev,
      ...financial
    }))
  }

  const handleConstructionChange = (construction: any) => {
    setFormData(prev => ({
      ...prev,
      ...construction
    }))
  }

  const handleOwnerDeveloperChange = (ownerDeveloper: any) => {
    setFormData(prev => ({
      ...prev,
      ...ownerDeveloper
    }))
  }



  const validateCurrentStep = () => {
    // Step-specific validation only
    switch (currentStep) {
      case 1: // Basic Information
        if (!formData.name?.trim()) {
          toast.error('Property name is required')
          return false
        }
        if (!formData.description?.trim()) {
          toast.error('Property description is required')
          return false
        }
        if (!formData.propertyType) {
          toast.error('Property type is required')
          return false
        }
        break

      case 2: // Location Details
        if (!formData.location?.address?.trim()) {
          toast.error('Property address is required (minimum 3 characters)')
          return false
        }
        if (formData.location.address.trim().length < 3) {
          toast.error('Property address must be at least 3 characters long')
          return false
        }
        if (!formData.location?.city?.trim()) {
          toast.error('City is required (minimum 2 characters)')
          return false
        }
        if (formData.location.city.trim().length < 2) {
          toast.error('City must be at least 2 characters long')
          return false
        }
        if (!formData.location?.state?.trim()) {
          toast.error('State is required (minimum 2 characters)')
          return false
        }
        if (formData.location.state.trim().length < 2) {
          toast.error('State must be at least 2 characters long')
          return false
        }
        // Pincode is optional in backend validation
        break

      case 3: // Owner & Developer
        if (!formData.ownerId && !formData.developerId) {
          toast.error('Please select either a property owner or developer')
          return false
        }
        break

      case 4: // Financial Details
        if (!formData.expectedReturns || formData.expectedReturns <= 0) {
          toast.error('Expected returns is required and must be greater than 0')
          return false
        }
        if (formData.expectedReturns > 100) {
          toast.error('Expected returns cannot exceed 100%')
          return false
        }
        if (!formData.totalStocks || formData.totalStocks <= 0) {
          toast.error('Total stocks is required and must be greater than 0')
          return false
        }
        if (!formData.pricePerStock || formData.pricePerStock <= 0) {
          toast.error('Price per stock is required and must be greater than 0')
          return false
        }
        if (!formData.maturityPeriodMonths || formData.maturityPeriodMonths <= 0) {
          toast.error('Maturity period is required and must be at least 1 month')
          return false
        }
        break

      case 5: // Construction Timeline
        if (!formData.constructionStatus) {
          toast.error('Construction status is required')
          return false
        }
        if (!formData.launchDate) {
          toast.error('Launch date is required')
          return false
        }
        if (!formData.expectedCompletion) {
          toast.error('Expected completion date is required')
          return false
        }
        break

      case 6: // Property Images
        if (!formData.images || formData.images.length === 0) {
          toast.error('At least 1 property image is required')
          return false
        }
        if (formData.images.length > 10) {
          toast.error('Maximum 10 images allowed')
          return false
        }
        break

      case 7: // Features & Amenities (optional)
        // No validation required - features and amenities are optional
        break

      case 8: // Document Upload (optional)
        // No validation required - legal documents are optional
        break

      case 9: // Booking Form (optional)
        // No validation required - booking form is optional demonstration
        break

      default:
        break
    }

    return true
  }

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (currentStep < 9) {
        setCurrentStep(currentStep + 1)
      } else {
        handleSubmit()
      }
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    if (!validateCurrentStep()) {
      toast.error('Please fix the validation errors before submitting')
      return
    }

    // Validate required fields
    if (!formData.ownerId?.trim() && !formData.developerId?.trim()) {
      toast.error('Please select at least one owner or developer')
      return
    }

    if (formData.images.length === 0) {
      toast.error('Please upload at least one property image')
      return
    }

    try {
      setIsSubmitting(true)
      // Transform data for API compatibility - Backend model expects location as object
      const apiFormData = {
        ...formData,
        // Backend model expects location as object with address, city, state, etc.
        location: {
          address: formData.location?.address?.trim() || '',
          city: formData.location?.city?.trim() || extractCityFromAddress(formData.location?.address) || '',
          state: formData.location?.state?.trim() || extractStateFromAddress(formData.location?.address) || '',
          country: formData.location?.country?.trim() || 'India',
          pincode: formData.location?.pincode?.trim() || '',
          // Add coordinates if available
          ...(formData.location?.coordinates?.latitude && formData.location?.coordinates?.longitude && {
            coordinates: {
              latitude: formData.location.coordinates.latitude,
              longitude: formData.location.coordinates.longitude
            }
          })
        },
        // Also add flat city and state for backward compatibility
        city: formData.location?.city?.trim() || extractCityFromAddress(formData.location?.address) || '',
        state: formData.location?.state?.trim() || extractStateFromAddress(formData.location?.address) || '',
        // Stock configuration data
        totalStocks: formData.totalStocks || 1000,
        stockPrice: formData.pricePerStock || 1000,
        minimumPurchase: 1,
        referralCommissionRate: formData.referralCommissionRate || 2,
        salesCommissionRate: formData.salesCommissionRate || 1,
        referralCommissionPerStock: formData.referralCommissionPerStock || 20,
        salesCommissionPerStock: formData.salesCommissionPerStock || 10,
        commissionType: formData.commissionType || 'percentage',
        // Fix construction status mapping
        constructionStatus: mapConstructionStatus(formData.constructionStatus),
        // Fix images format - send as array of objects with url and name
        images: formData.images.map((img, index) => {
          if (typeof img === 'string') {
            return {
              url: img,
              name: `Property Image ${index + 1}`,
              type: 'image/jpeg',
              key: `image-${index + 1}`
            }
          } else {
            return {
              url: img.url || img,
              name: img.name || `Property Image ${index + 1}`,
              type: img.type || 'image/jpeg',
              key: img.key || `image-${index + 1}`
            }
          }
        }),
        // Fix legal documents format - send as object
        legalDocuments: Array.isArray(formData.legalDocuments) && formData.legalDocuments.length > 0
          ? { approvals: formData.legalDocuments, certificates: [], agreements: [] }
          : { approvals: [], certificates: [], agreements: [] },
        // Set owner/developer IDs (only include if not empty)
        ...(formData.ownerId?.trim() && { ownerId: formData.ownerId.trim() }),
        ...(formData.developerId?.trim() && { developerId: formData.developerId.trim() }),
        // Set completion dates
        actualCompletion: formData.actualCompletion,
        // Transform documents array - send as array of objects
        documents: formData.documents?.map((doc, index) => {
          if (typeof doc === 'string') {
            return {
              url: doc,
              name: `Document ${index + 1}`,
              type: 'application/pdf',
              key: `doc-${index + 1}`
            }
          } else {
            return {
              url: doc.url || doc,
              name: doc.name || `Document ${index + 1}`,
              type: doc.type || 'application/pdf',
              key: doc.key || `doc-${index + 1}`
            }
          }
        }),
        videos: formData.videos?.map(vid => typeof vid === 'string' ? vid : vid.url) || [],
        status: formData.status as any
      }

      // Helper functions
      function extractCityFromAddress(address: string): string {
        if (!address) return ''
        const parts = address.split(',').map(p => p.trim())
        return parts[0] || ''
      }

      function extractStateFromAddress(address: string): string {
        if (!address) return ''
        const parts = address.split(',').map(p => p.trim())
        // Look for state in the address parts
        for (const part of parts) {
          if (part.toLowerCase().includes('uttar pradesh') || part.toLowerCase().includes('up')) return 'Uttar Pradesh'
          if (part.toLowerCase().includes('delhi')) return 'Delhi'
          if (part.toLowerCase().includes('haryana')) return 'Haryana'
          if (part.toLowerCase().includes('rajasthan')) return 'Rajasthan'
          // Add more states as needed
        }
        return parts[parts.length - 2] || ''
      }

      function mapConstructionStatus(status: string): string {
        const statusMap: Record<string, string> = {
          'structure': 'under_construction',
          'planning': 'planning',
          'foundation': 'under_construction',
          'completed': 'completed',
          'ready_to_move': 'ready_to_move'
        }
        return statusMap[status] || 'planning'
      }

      // Remove empty fields
      const cleanedData: any = { ...apiFormData }
      if (!cleanedData.ownerId || cleanedData.ownerId.trim() === '') {
        delete cleanedData.ownerId
      }
      if (!cleanedData.developerId || cleanedData.developerId.trim() === '') {
        delete cleanedData.developerId
      }
      if (!cleanedData.actualCompletion || cleanedData.actualCompletion.trim() === '') {
        delete cleanedData.actualCompletion
      }

      console.log('=== PROPERTY CREATION DEBUG ===')
      console.log('Form data location:', formData.location)
      console.log('API data location:', cleanedData.location)
      console.log('Location structure:', {
        address: cleanedData.location?.address,
        city: cleanedData.location?.city,
        state: cleanedData.location?.state,
        country: cleanedData.location?.country,
        pincode: cleanedData.location?.pincode
      })
      console.log('Required fields check:', {
        name: cleanedData.name,
        propertyType: cleanedData.propertyType,
        totalStocks: cleanedData.totalStocks,
        stockPrice: cleanedData.stockPrice,
        expectedReturns: cleanedData.expectedReturns,
        maturityPeriodMonths: cleanedData.maturityPeriodMonths,
        imagesCount: cleanedData.images?.length
      })
      console.log('Full API payload:', JSON.stringify(cleanedData, null, 2))

      // Backend expects flat structure, no need to restructure location
      const finalData = {
        ...cleanedData
      }

      // Remove any undefined or null values
      Object.keys(finalData).forEach(key => {
        if (finalData[key] === undefined || finalData[key] === null || finalData[key] === '') {
          delete finalData[key]
        }
      })

      console.log('Final data location:', finalData.location)
      await createProperty(finalData).unwrap()
      toast.success('Property created successfully!')
      router.push('/properties')
    } catch (error: any) {
      console.error('Property creation failed:', error)
      console.error('Error details:', JSON.stringify(error, null, 2))

      if (error?.data?.details) {
        // Show validation errors with field names
        const validationErrors = error.data.details.map((err: any) =>
          `${err.path || err.param || 'Field'}: ${err.msg}`
        ).join('\n')
        toast.error(`Validation Errors:\n${validationErrors}`, { duration: 8000 })
      } else if (error?.data?.message) {
        toast.error(`Error: ${error.data.message}`)
      } else if (error?.message) {
        toast.error(`Error: ${error.message}`)
      } else {
        toast.error('Failed to create property. Please check all required fields and try again.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the property's basic details and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Property Name *</Label>
                  <Input
                    id="name"
                    placeholder="Enter property name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.name?.[0]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="propertyType">Property Type *</Label>
                  <Select
                    value={formData.propertyType}
                    onValueChange={(value) => handleInputChange('propertyType', value)}
                  >
                    <SelectTrigger className={errors.propertyType ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <span>{type.icon}</span>
                            {type.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.propertyType && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-4 w-4" />
                      {errors.propertyType?.[0]}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Enter detailed property description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className={errors.description ? 'border-red-500' : ''}
                  rows={4}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.description?.[0]}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )

      case 2:
        return (
          <LocationPicker
            value={formData.location}
            onChange={handleLocationChange}
            errors={errors.location}
          />
        )

      case 3:
        return (
          <OwnerDeveloperSelector
            value={{
              ...(formData.ownerId && { ownerId: formData.ownerId }),
              ...(formData.developerId && { developerId: formData.developerId })
            }}
            onChange={handleOwnerDeveloperChange}
            errors={{
              ownerId: errors.ownerId,
              developerId: errors.developerId
            }}
          />
        )

      case 4:
        return (
          <FinancialDetails
            value={{
              expectedReturns: formData.expectedReturns,
              maturityPeriodMonths: formData.maturityPeriodMonths,
              totalStocks: formData.totalStocks,
              pricePerStock: formData.pricePerStock,
              availableStocks: formData.availableStocks,
              stockPrefix: formData.stockPrefix,
              stockStartNumber: formData.stockStartNumber,
              referralCommissionRate: formData.referralCommissionRate || 0,
              salesCommissionRate: formData.salesCommissionRate || 0,
              referralCommissionPerStock: formData.referralCommissionPerStock || 0,
              salesCommissionPerStock: formData.salesCommissionPerStock || 0,
              commissionType: (formData.commissionType as 'percentage' | 'fixed') || 'percentage'
            }}
            onChange={handleFinancialChange}
            errors={errors}
          />
        )

      case 5:
        return (
          <ConstructionTimeline
            value={{
              constructionStatus: formData.constructionStatus,
              launchDate: formData.launchDate,
              expectedCompletion: formData.expectedCompletion,
              ...(formData.actualCompletion && { actualCompletion: formData.actualCompletion }),
              ...(formData.constructionTimeline && { constructionTimeline: formData.constructionTimeline })
            }}
            onChange={handleConstructionChange}
            errors={{
              constructionStatus: errors.constructionStatus,
              launchDate: errors.launchDate,
              expectedCompletion: errors.expectedCompletion,
              actualCompletion: errors.actualCompletion,
              constructionTimeline: errors.constructionTimeline
            }}
          />
        )

      case 6:
        return (
          <PropertyImagesUpload
            images={formData.images}
            onImagesChange={(images) => handleInputChange('images', images)}
          />
        )

      case 7:
        return (
          <EnhancedFeaturesAmenities
            features={formData.features || []}
            amenities={formData.amenities || []}
            onFeaturesChange={(features) => handleInputChange('features', features)}
            onAmenitiesChange={(amenities) => handleInputChange('amenities', amenities)}
            featuresList={featuresList}
            amenitiesList={amenitiesList}
          />
        )

      case 8:
        return (
          <LegalDocumentsUpload
            legalDocuments={formData.legalDocuments || []}
            onLegalDocumentsChange={(documents) => handleInputChange('legalDocuments', documents)}
          />
        )

      case 9:
        return (
          <BookingForm
            propertyData={{
              name: formData.name,
              location: {
                address: formData.location.address,
                city: formData.location.city,
                state: formData.location.state,
                pincode: formData.location.pincode
              },
              pricePerStock: formData.pricePerStock,
              totalStocks: formData.totalStocks,
              expectedReturns: formData.expectedReturns,
              maturityPeriodMonths: formData.maturityPeriodMonths,
              propertyType: formData.propertyType
            }}
            onBookingComplete={(bookingData) => {
              console.log('Booking completed:', bookingData)
              toast.success('Booking form completed successfully!')
            }}
          />
        )

      default:
        return null
    }
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg p-6 text-white">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-start gap-4 flex-1">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="flex-shrink-0 bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 hover:text-white transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <div className="min-w-0 flex-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2 flex items-center gap-3">
                  <div className="p-2 bg-white/20 backdrop-blur-sm rounded-xl flex-shrink-0">
                    <Building className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <span className="leading-tight">Add New Property</span>
                </h1>
                <p className="text-green-100 text-sm sm:text-base max-w-2xl leading-relaxed">
                  Create a comprehensive property listing with images, features, amenities, and legal documents
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 flex-shrink-0">
              <Badge className="bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30 transition-all duration-200">
                Step {currentStep} of 8
              </Badge>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
          <div className="mb-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
              {/* Desktop Progress Steps */}
              <div className="hidden lg:flex items-center justify-between">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center flex-1">
                    <div className="flex items-center">
                      <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                        currentStep >= step.id
                          ? 'bg-green-500 border-green-500 text-white shadow-lg'
                          : currentStep === step.id - 1
                          ? 'border-green-300 text-green-500 bg-green-50'
                          : 'border-gray-300 text-gray-400 bg-gray-50'
                      }`}>
                        {React.createElement(step.icon, { className: "h-6 w-6" })}
                      </div>
                      <div className="ml-4">
                        <p className={`text-sm font-semibold transition-colors ${
                          currentStep >= step.id ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          Step {step.id}
                        </p>
                        <p className={`text-xs transition-colors ${
                          currentStep >= step.id ? 'text-gray-700' : 'text-gray-400'
                        }`}>
                          <span className="hidden xl:inline">{step.title}</span>
                          <span className="xl:hidden">{step.shortTitle}</span>
                        </p>
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`flex-1 h-0.5 mx-6 transition-all duration-300 ${
                        currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>

              {/* Mobile Progress Steps */}
              <div className="lg:hidden">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-green-500 text-white shadow-lg">
                      {steps[currentStep - 1] && React.createElement(steps[currentStep - 1]!.icon, { className: "h-5 w-5" })}
                    </div>
                    <div>
                      <p className="text-sm font-semibold text-green-600">
                        Step {currentStep} of {steps.length}
                      </p>
                      <p className="text-xs text-gray-600">
                        <span className="hidden sm:inline">{steps[currentStep - 1]?.title}</span>
                        <span className="sm:hidden">{steps[currentStep - 1]?.shortTitle}</span>
                      </p>
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(currentStep / steps.length) * 100}%` }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Step Content */}
          <div className="mb-6 sm:mb-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 lg:p-8">
              {renderStepContent()}
            </div>
          </div>

          {/* Navigation */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="w-full sm:w-auto border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex items-center gap-3 w-full sm:w-auto">
                {currentStep < steps.length ? (
                  <Button
                    onClick={handleNext}
                    className="w-full sm:w-auto bg-green-500 hover:bg-green-600 text-white shadow-lg transition-all duration-200"
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting || isCreating}
                    className="w-full sm:w-auto bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg transition-all duration-200 disabled:opacity-50"
                  >
                    {isSubmitting || isCreating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating Property...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Property
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
      </div>
    </DashboardLayout>
  )
}
