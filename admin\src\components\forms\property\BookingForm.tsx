'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'
import {
  Download,
  CreditCard,
  FileText,
  User,
  IndianRupee,
  Building,
  CheckCircle
} from 'lucide-react'
import { jsPDF } from "jspdf";

interface BookingFormData {
  // Customer Details
  customerName: string
  customerEmail: string
  customerPhone: string
  customerAddress: string
  
  // Booking Details
  bookingAmount: number
  paymentMode: 'online' | 'cheque' | 'cash' | 'bank_transfer'
  chequeNumber?: string
  bankName?: string
  transactionId?: string
  
  // Property Preferences
  floorPreference: string
  unitType: string
  facingPreference: string
  
  // Financial Details
  loanRequired: boolean
  monthlyIncome?: number
  
  // Nominee Details
  nomineeName?: string
  nomineeRelation?: string
  nomineePhone?: string
  
  // Additional
  specialRequests?: string
}

interface BookingFormProps {
  propertyData: {
    name: string
    location: {
      address: string
      city: string
      state: string
      pincode: string
    }
    pricePerStock: number
    totalStocks: number
    expectedReturns: number
    maturityPeriodMonths: number
    propertyType: string
  }
  onBookingComplete?: (bookingData: BookingFormData) => void
}

export default function BookingForm({ propertyData, onBookingComplete }: BookingFormProps) {
  const [formData, setFormData] = useState<BookingFormData>({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerAddress: '',
    bookingAmount: Math.round(propertyData.pricePerStock * 0.1), // 10% of price per stock
    paymentMode: 'online',
    floorPreference: '',
    unitType: '',
    facingPreference: '',
    loanRequired: false,
    specialRequests: ''
  })

  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [bookingId, setBookingId] = useState('')

  useEffect(() => {
    // Generate unique booking ID
    const generateBookingId = () => {
      const timestamp = Date.now().toString(36)
      const random = Math.random().toString(36).substring(2, 7)
      return `BK${timestamp}${random}`.toUpperCase()
    }
    setBookingId(generateBookingId())
  }, [])

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const generateBookingPDF = async () => {
    setIsGeneratingPDF(true)

    try {
      // Create PDF using static import
      const pdf = new jsPDF()
      const pageWidth = pdf.internal.pageSize.width
      const margin = 20
      let yPosition = 30

      // Header
      pdf.setFontSize(20)
      pdf.setFont('helvetica', 'bold')
      pdf.text('PROPERTY BOOKING RECEIPT', pageWidth / 2, yPosition, { align: 'center' })
      
      yPosition += 20
      pdf.setFontSize(12)
      pdf.setFont('helvetica', 'normal')
      pdf.text(`Booking ID: ${bookingId}`, pageWidth / 2, yPosition, { align: 'center' })
      
      yPosition += 10
      pdf.text(`Date: ${new Date().toLocaleDateString('en-IN')}`, pageWidth / 2, yPosition, { align: 'center' })
      
      yPosition += 25

      // Property Details Section
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('PROPERTY DETAILS', margin, yPosition)
      yPosition += 15

      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'normal')
      
      const propertyDetails = [
        ['Property Name:', propertyData.name],
        ['Property Type:', propertyData.propertyType],
        ['Location:', `${propertyData.location.address}, ${propertyData.location.city}`],
        ['City/State:', `${propertyData.location.city}, ${propertyData.location.state} - ${propertyData.location.pincode}`],
        ['Price per Stock:', `₹${propertyData.pricePerStock.toLocaleString('en-IN')}`],
        ['Total Stocks Available:', propertyData.totalStocks.toString()],
        ['Expected Returns:', `${propertyData.expectedReturns}% per annum`],
        ['Maturity Period:', `${propertyData.maturityPeriodMonths} months`]
      ]

      propertyDetails.forEach(([label, value]) => {
        pdf.text(label || '', margin, yPosition)
        pdf.text(value || '', margin + 60, yPosition)
        yPosition += 12
      })

      yPosition += 10

      // Customer Details Section
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('CUSTOMER DETAILS', margin, yPosition)
      yPosition += 15

      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'normal')
      
      const customerDetails = [
        ['Name:', formData.customerName],
        ['Email:', formData.customerEmail],
        ['Phone:', formData.customerPhone],
        ['Address:', formData.customerAddress],
        ['Floor Preference:', formData.floorPreference || 'No preference'],
        ['Unit Type:', formData.unitType || 'Standard'],
        ['Facing Preference:', formData.facingPreference || 'No preference']
      ]

      customerDetails.forEach(([label, value]) => {
        pdf.text(label || '', margin, yPosition)
        pdf.text(value || '', margin + 60, yPosition)
        yPosition += 12
      })

      yPosition += 10

      // Booking Details Section
      pdf.setFontSize(14)
      pdf.setFont('helvetica', 'bold')
      pdf.text('BOOKING DETAILS', margin, yPosition)
      yPosition += 15

      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'normal')
      
      const bookingDetails = [
        ['Booking Amount:', `₹${formData.bookingAmount.toLocaleString('en-IN')}`],
        ['Payment Mode:', formData.paymentMode.toUpperCase()],
        ...(formData.chequeNumber ? [['Cheque Number:', formData.chequeNumber]] : []),
        ...(formData.bankName ? [['Bank Name:', formData.bankName]] : []),
        ...(formData.transactionId ? [['Transaction ID:', formData.transactionId]] : []),
        ['Loan Required:', formData.loanRequired ? 'Yes' : 'No']
      ]

      bookingDetails.forEach(([label, value]) => {
        pdf.text(label || '', margin, yPosition)
        pdf.text(value || '', margin + 60, yPosition)
        yPosition += 12
      })

      if (formData.nomineeName) {
        yPosition += 10
        pdf.setFontSize(14)
        pdf.setFont('helvetica', 'bold')
        pdf.text('NOMINEE DETAILS', margin, yPosition)
        yPosition += 15

        pdf.setFontSize(10)
        pdf.setFont('helvetica', 'normal')
        
        const nomineeDetails = [
          ['Nominee Name:', formData.nomineeName],
          ['Relation:', formData.nomineeRelation || ''],
          ['Phone:', formData.nomineePhone || '']
        ]

        nomineeDetails.forEach(([label, value]) => {
          if (value) {
            pdf.text(label || '', margin, yPosition)
            pdf.text(value || '', margin + 60, yPosition)
            yPosition += 12
          }
        })
      }

      // Terms and Conditions
      yPosition += 20
      pdf.setFontSize(12)
      pdf.setFont('helvetica', 'bold')
      pdf.text('TERMS & CONDITIONS', margin, yPosition)
      yPosition += 15

      pdf.setFontSize(9)
      pdf.setFont('helvetica', 'normal')
      
      const terms = [
        '1. This booking amount is adjustable against the total property cost.',
        '2. Booking amount is refundable within 15 days of booking (cooling period).',
        '3. Final allotment subject to availability and completion of documentation.',
        '4. All payments should be made through proper banking channels.',
        '5. Property registration charges and stamp duty are additional.',
        '6. Possession will be given as per the construction timeline.',
        '7. This receipt is computer generated and does not require signature.'
      ]

      terms.forEach(term => {
        const lines = pdf.splitTextToSize(term, pageWidth - 2 * margin)
        pdf.text(lines, margin, yPosition)
        yPosition += lines.length * 8
      })

      // Footer
      yPosition += 20
      pdf.setFontSize(10)
      pdf.setFont('helvetica', 'italic')
      pdf.text('Thank you for choosing our property. For any queries, please contact our sales team.', 
               pageWidth / 2, yPosition, { align: 'center' })

      // Save PDF
      pdf.save(`Booking_Receipt_${bookingId}.pdf`)
      
      toast.success('Booking receipt downloaded successfully!')
      
      if (onBookingComplete) {
        onBookingComplete(formData)
      }
      
    } catch (error) {
      console.error('Error generating PDF:', error)
      toast.error('Failed to generate booking receipt')
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleSubmit = async () => {
    // Validate required fields
    if (!formData.customerName || !formData.customerEmail || !formData.customerPhone) {
      toast.error('Please fill all required customer details')
      return
    }

    if (!formData.bookingAmount || formData.bookingAmount <= 0) {
      toast.error('Please enter a valid booking amount')
      return
    }

    await generateBookingPDF()
  }

  return (
    <div className="space-y-6">
      {/* Property Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Property Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="font-semibold">{propertyData.name}</p>
              <p className="text-sm text-gray-600">{propertyData.location.address}</p>
              <p className="text-sm text-gray-600">{propertyData.location.city}, {propertyData.location.state}</p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Price per Stock:</span>
                <span className="font-semibold">₹{propertyData.pricePerStock.toLocaleString('en-IN')}</span>
              </div>
              <div className="flex justify-between">
                <span>Expected Returns:</span>
                <span className="font-semibold">{propertyData.expectedReturns}% p.a.</span>
              </div>
              <div className="flex justify-between">
                <span>Maturity Period:</span>
                <span className="font-semibold">{propertyData.maturityPeriodMonths} months</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Booking ID Display */}
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <Badge variant="outline" className="text-lg px-4 py-2">
              Booking ID: {bookingId}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Customer Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Customer Details
          </CardTitle>
          <CardDescription>
            Please provide accurate customer information for booking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerName">Full Name *</Label>
              <Input
                id="customerName"
                value={formData.customerName}
                onChange={(e) => handleInputChange('customerName', e.target.value)}
                placeholder="Enter customer full name"
                required
              />
            </div>
            <div>
              <Label htmlFor="customerEmail">Email Address *</Label>
              <Input
                id="customerEmail"
                type="email"
                value={formData.customerEmail}
                onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerPhone">Phone Number *</Label>
              <Input
                id="customerPhone"
                value={formData.customerPhone}
                onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                placeholder="+91 9876543210"
                required
              />
            </div>
            <div>
              <Label htmlFor="customerAddress">Address</Label>
              <Input
                id="customerAddress"
                value={formData.customerAddress}
                onChange={(e) => handleInputChange('customerAddress', e.target.value)}
                placeholder="Complete address"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Property Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="floorPreference">Floor Preference</Label>
              <Select value={formData.floorPreference} onValueChange={(value) => handleInputChange('floorPreference', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select floor" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ground">Ground Floor</SelectItem>
                  <SelectItem value="1-3">1st to 3rd Floor</SelectItem>
                  <SelectItem value="4-7">4th to 7th Floor</SelectItem>
                  <SelectItem value="8-12">8th to 12th Floor</SelectItem>
                  <SelectItem value="above-12">Above 12th Floor</SelectItem>
                  <SelectItem value="no-preference">No Preference</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="unitType">Unit Type</Label>
              <Select value={formData.unitType} onValueChange={(value) => handleInputChange('unitType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select unit type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1bhk">1 BHK</SelectItem>
                  <SelectItem value="2bhk">2 BHK</SelectItem>
                  <SelectItem value="3bhk">3 BHK</SelectItem>
                  <SelectItem value="4bhk">4 BHK</SelectItem>
                  <SelectItem value="penthouse">Penthouse</SelectItem>
                  <SelectItem value="studio">Studio</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="facingPreference">Facing Preference</Label>
              <Select value={formData.facingPreference} onValueChange={(value) => handleInputChange('facingPreference', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select facing" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="north">North Facing</SelectItem>
                  <SelectItem value="south">South Facing</SelectItem>
                  <SelectItem value="east">East Facing</SelectItem>
                  <SelectItem value="west">West Facing</SelectItem>
                  <SelectItem value="north-east">North-East</SelectItem>
                  <SelectItem value="south-east">South-East</SelectItem>
                  <SelectItem value="north-west">North-West</SelectItem>
                  <SelectItem value="south-west">South-West</SelectItem>
                  <SelectItem value="no-preference">No Preference</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Booking Amount & Payment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Booking Amount & Payment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="bookingAmount">Booking Amount (₹) *</Label>
              <Input
                id="bookingAmount"
                type="number"
                value={formData.bookingAmount}
                onChange={(e) => handleInputChange('bookingAmount', parseInt(e.target.value) || 0)}
                placeholder="Enter booking amount"
                min="1000"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                Suggested: ₹{Math.round(propertyData.pricePerStock * 0.1).toLocaleString('en-IN')} (10% of stock price)
              </p>
            </div>

            <div>
              <Label htmlFor="paymentMode">Payment Mode *</Label>
              <Select value={formData.paymentMode} onValueChange={(value) => handleInputChange('paymentMode', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment mode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="online">Online Transfer</SelectItem>
                  <SelectItem value="cheque">Cheque</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Conditional Payment Details */}
          {formData.paymentMode === 'cheque' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="chequeNumber">Cheque Number</Label>
                <Input
                  id="chequeNumber"
                  value={formData.chequeNumber || ''}
                  onChange={(e) => handleInputChange('chequeNumber', e.target.value)}
                  placeholder="Enter cheque number"
                />
              </div>
              <div>
                <Label htmlFor="bankName">Bank Name</Label>
                <Input
                  id="bankName"
                  value={formData.bankName || ''}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  placeholder="Enter bank name"
                />
              </div>
            </div>
          )}

          {(formData.paymentMode === 'online' || formData.paymentMode === 'bank_transfer') && (
            <div>
              <Label htmlFor="transactionId">Transaction ID / Reference Number</Label>
              <Input
                id="transactionId"
                value={formData.transactionId || ''}
                onChange={(e) => handleInputChange('transactionId', e.target.value)}
                placeholder="Enter transaction ID or reference number"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Financial Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IndianRupee className="h-5 w-5" />
            Financial Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="loanRequired"
              checked={formData.loanRequired}
              onCheckedChange={(checked) => handleInputChange('loanRequired', checked === true)}
            />
            <Label htmlFor="loanRequired">Loan Required</Label>
          </div>

          {formData.loanRequired && (
            <div>
              <Label htmlFor="monthlyIncome">Monthly Income (₹)</Label>
              <Input
                id="monthlyIncome"
                type="number"
                value={formData.monthlyIncome || ''}
                onChange={(e) => handleInputChange('monthlyIncome', parseInt(e.target.value) || 0)}
                placeholder="Enter monthly income"
              />
              <p className="text-sm text-gray-500 mt-1">
                This helps us suggest suitable loan options
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Nominee Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Nominee Details (Optional)
          </CardTitle>
          <CardDescription>
            Nominee information for legal purposes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="nomineeName">Nominee Name</Label>
              <Input
                id="nomineeName"
                value={formData.nomineeName || ''}
                onChange={(e) => handleInputChange('nomineeName', e.target.value)}
                placeholder="Enter nominee name"
              />
            </div>

            <div>
              <Label htmlFor="nomineeRelation">Relation</Label>
              <Select value={formData.nomineeRelation || ''} onValueChange={(value) => handleInputChange('nomineeRelation', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select relation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="spouse">Spouse</SelectItem>
                  <SelectItem value="father">Father</SelectItem>
                  <SelectItem value="mother">Mother</SelectItem>
                  <SelectItem value="son">Son</SelectItem>
                  <SelectItem value="daughter">Daughter</SelectItem>
                  <SelectItem value="brother">Brother</SelectItem>
                  <SelectItem value="sister">Sister</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="nomineePhone">Nominee Phone</Label>
              <Input
                id="nomineePhone"
                value={formData.nomineePhone || ''}
                onChange={(e) => handleInputChange('nomineePhone', e.target.value)}
                placeholder="Enter nominee phone"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Special Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Special Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div>
            <Label htmlFor="specialRequests">Additional Requirements or Comments</Label>
            <Textarea
              id="specialRequests"
              value={formData.specialRequests || ''}
              onChange={(e) => handleInputChange('specialRequests', e.target.value)}
              placeholder="Any special requests, requirements, or comments..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>

      {/* Generate Booking Receipt */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Generate Booking Receipt
          </CardTitle>
          <CardDescription>
            Review all details and generate your booking receipt
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Booking Summary</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div>Customer: {formData.customerName || 'Not provided'}</div>
                <div>Phone: {formData.customerPhone || 'Not provided'}</div>
                <div>Email: {formData.customerEmail || 'Not provided'}</div>
                <div>Booking Amount: ₹{formData.bookingAmount.toLocaleString('en-IN')}</div>
                <div>Payment Mode: {formData.paymentMode.toUpperCase()}</div>
                <div>Booking ID: {bookingId}</div>
              </div>
            </div>

            <Button
              onClick={handleSubmit}
              disabled={isGeneratingPDF}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              size="lg"
            >
              {isGeneratingPDF ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating Receipt...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Generate & Download Booking Receipt
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
