import mongoose, { Schema, Document, Types } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { UserRole, UserStatus, KYCStatus, BaseDocument } from '../types';

export interface IUser extends BaseDocument {
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: UserRole;
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  kycStatus: KYCStatus;
  referralCode?: string;
  referredBy?: Types.ObjectId;
  profileImage?: string;
  avatar?: {
    key: string;
    name: string;
    type: string;
    url: string;
    uploadedAt: Date;
  };
  documents?: Array<{
    key: string;
    name: string;
    type: string;
    url: string;
    uploadedAt: Date;
  }>;
  dateOfBirth?: Date;
  lastLogin?: Date;
  wallet?: {
    balance: number;
    totalInvested: number;
    totalReturns: number;
    totalDeposited: number;
    totalWithdrawn: number;
  };
  walletAddress?: string;
  walletPrivateKey?: string;

  // Virtual fields
  fullName: string;
  defaultAvatar: string | null;

  // Instance methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateReferralCode(): string;
  toJSON(): any;
}

export interface IUserModel extends mongoose.Model<IUser> {
  findWithDefaultAvatar(query: any): Promise<any[]>;
  findOneWithDefaultAvatar(query: any): Promise<any>;
}

const userSchema = new Schema<IUser>({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  passwordHash: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long'],
    select: false // Don't include password in queries by default
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [100, 'First name cannot exceed 100 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [100, 'Last name cannot exceed 100 characters']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER
  },
  status: {
    type: String,
    enum: Object.values(UserStatus),
    default: UserStatus.PENDING_VERIFICATION
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  phoneVerified: {
    type: Boolean,
    default: false
  },
  kycStatus: {
    type: String,
    enum: Object.values(KYCStatus),
    default: KYCStatus.NOT_STARTED
  },
  referralCode: {
    type: String,
    unique: true,
    sparse: true,
    uppercase: true
  },
  referredBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  profileImage: {
    type: String,
    trim: true
  },
  avatar: {
    key: {
      type: String,
      trim: true
    },
    name: {
      type: String,
      trim: true
    },
    type: {
      type: String,
      trim: true
    },
    url: {
      type: String,
      trim: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  },
  documents: [{
    key: {
      type: String,
      required: true,
      trim: true
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      required: true,
      trim: true
    },
    url: {
      type: String,
      required: true,
      trim: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  dateOfBirth: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        return !value || value < new Date();
      },
      message: 'Date of birth cannot be in the future'
    }
  },
  lastLogin: {
    type: Date
  },
  wallet: {
    balance: {
      type: Number,
      default: 0,
      min: 0
    },
    totalInvested: {
      type: Number,
      default: 0,
      min: 0
    },
    totalReturns: {
      type: Number,
      default: 0
    },
    totalDeposited: {
      type: Number,
      default: 0,
      min: 0
    },
    totalWithdrawn: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  walletAddress: {
    type: String,
    trim: true,
    match: [/^0x[a-fA-F0-9]{40}$/, 'Please enter a valid Ethereum wallet address'],
    select: false // Don't include in queries by default for security
  },
  walletPrivateKey: {
    type: String,
    trim: true,
    match: [/^0x[a-fA-F0-9]{64}$/, 'Please enter a valid private key'],
    select: false // Don't include in queries by default for security
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (email and referralCode already have unique indexes)
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ kycStatus: 1 });
userSchema.index({ referredBy: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function(this: IUser) {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for default avatar (uses KYC selfie if no profile image)
userSchema.virtual('defaultAvatar').get(function(this: IUser) {
  // If user has uploaded a profile image or avatar, use that
  if (this.profileImage) {
    return this.profileImage;
  }
  if (this.avatar?.url) {
    return this.avatar.url;
  }

  // Otherwise, return null - will be populated by populate hook
  return null;
});

// Post-find hook to automatically add default avatar to all user queries
userSchema.post(['find', 'findOne', 'findOneAndUpdate'], async function(result: any) {
  if (!result) return;

  const UserKYC = mongoose.model('UserKYC');
  const users = Array.isArray(result) ? result : [result];

  // Only process users that don't already have a profile image or avatar
  const usersNeedingKYC = users.filter((user: any) =>
    user && typeof user === 'object' && user._id && !user.profileImage && !user.avatar?.url
  );

  if (usersNeedingKYC.length === 0) {
    // Set defaultAvatar for users who already have profile images or avatars
    users.forEach((user: any) => {
      if (user && typeof user === 'object') {
        user.defaultAvatar = user.profileImage || user.avatar?.url || null;
      }
    });
    return;
  }

  // Batch fetch KYC data for users who need it
  const userIds = usersNeedingKYC.map((user: any) => user._id);
  const kycData = await UserKYC.find({
    userId: { $in: userIds },
    'documents.type': 'selfie'
  }).select('userId documents').lean();

  // Create a map for quick lookup
  const kycMap = new Map();
  kycData.forEach((kyc: any) => {
    const selfieDoc = kyc.documents?.find((doc: any) => doc.type === 'selfie');
    if (selfieDoc?.documentUrl) {
      kycMap.set(kyc.userId.toString(), selfieDoc.documentUrl);
    }
  });

  // Set defaultAvatar for all users
  users.forEach((user: any) => {
    if (user && typeof user === 'object') {
      if (user.profileImage) {
        user.defaultAvatar = user.profileImage;
      } else if (user.avatar?.url) {
        user.defaultAvatar = user.avatar.url;
      } else {
        user.defaultAvatar = kycMap.get(user._id.toString()) || null;
      }
    }
  });
});

// Static method to get user with KYC selfie as default avatar
userSchema.statics.findWithDefaultAvatar = async function(query: any) {
  const UserKYC = mongoose.model('UserKYC');

  const users = await this.find(query).lean();

  // For each user, if they don't have a profile image, try to get their KYC selfie
  const usersWithAvatars = await Promise.all(
    users.map(async (user: any) => {
      if (!user.profileImage && !user.avatar?.url) {
        try {
          const kyc: any = await UserKYC.findOne({ userId: user._id }).lean();
          if (kyc && kyc.documents && Array.isArray(kyc.documents)) {
            const selfieDoc = kyc.documents.find((doc: any) => doc.type === 'selfie');
            if (selfieDoc && selfieDoc.documentUrl) {
              user.defaultAvatar = selfieDoc.documentUrl;
            }
          }
        } catch (error) {
          console.error('Error fetching KYC selfie for user:', user._id, error);
        }
      } else {
        user.defaultAvatar = user.profileImage || user.avatar?.url;
      }
      return user;
    })
  );

  return usersWithAvatars;
};

// Static method to get single user with KYC selfie as default avatar
userSchema.statics.findOneWithDefaultAvatar = async function(query: any) {
  const users = await (this as any).findWithDefaultAvatar(query);
  return users.length > 0 ? users[0] : null;
};

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('passwordHash')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.passwordHash = await bcrypt.hash(this.passwordHash, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Pre-save middleware to generate referral code
userSchema.pre('save', function(next) {
  if (!this.referralCode && this.isNew) {
    this.referralCode = this.generateReferralCode();
  }
  next();
});

// Instance method to compare password
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    if (!candidatePassword || !this.passwordHash) {
      return false;
    }

    return await bcrypt.compare(candidatePassword, this.passwordHash);
  } catch (error) {
    console.error('Password comparison error:', error);
    return false;
  }
};

// Instance method to generate referral code
userSchema.methods.generateReferralCode = function(): string {
  const prefix = 'SGM';
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}-${timestamp}${random}`;
};

// Override toJSON to exclude sensitive fields
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.passwordHash;
  delete userObject.__v;
  return userObject;
};

// Static methods
userSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByReferralCode = function(referralCode: string) {
  return this.findOne({ referralCode: referralCode.toUpperCase() });
};

export const User = mongoose.model<IUser, IUserModel>('User', userSchema);
export default User;
