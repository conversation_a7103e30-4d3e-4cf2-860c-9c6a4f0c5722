import mongoose, { Schema, Types } from 'mongoose';
import { BaseDocument } from '../types';

export interface IUserWallet extends BaseDocument {
  userId: Types.ObjectId;
  balance: number;
  totalInvested: number;
  totalReturns: number;
  totalReferralEarnings: number;
  totalDeposited: number;
  totalWithdrawn: number;
  pendingWithdrawals: number;
  lifetimeEarnings: number;
  currency: string;
  lastTransactionAt?: Date;
  isActive?: boolean;
  notes?: string;

  // Crypto wallet fields
  walletAddress?: string;
  walletPrivateKey?: string;
  cryptoBalance?: {
    BNB: number;
    USDT: number;
    lastUpdated: Date;
  };

  // Virtual fields
  availableBalance: number;
  totalEarnings: number;
}

const userWalletSchema = new Schema<IUserWallet>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    unique: true
  },
  balance: {
    type: Number,
    default: 0.00,
    min: [0, 'Balance cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100 // Round to 2 decimal places
  },
  totalInvested: {
    type: Number,
    default: 0.00,
    min: [0, 'Total invested cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  totalReturns: {
    type: Number,
    default: 0.00,
    min: [0, 'Total returns cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  totalReferralEarnings: {
    type: Number,
    default: 0.00,
    min: [0, 'Total referral earnings cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  totalDeposited: {
    type: Number,
    default: 0.00,
    min: [0, 'Total deposited cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  totalWithdrawn: {
    type: Number,
    default: 0.00,
    min: [0, 'Total withdrawn cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  pendingWithdrawals: {
    type: Number,
    default: 0.00,
    min: [0, 'Pending withdrawals cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  lifetimeEarnings: {
    type: Number,
    default: 0.00,
    min: [0, 'Lifetime earnings cannot be negative'],
    set: (value: number) => Math.round(value * 100) / 100
  },
  currency: {
    type: String,
    default: 'USD',
    required: true
  },
  lastTransactionAt: {
    type: Date,
    required: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  notes: {
    type: String,
    required: false
  },
  walletAddress: {
    type: String,
    trim: true,
    match: [/^0x[a-fA-F0-9]{40}$/, 'Please enter a valid Ethereum wallet address'],
    select: false, // Don't include in queries by default for security
    sparse: true // Allow multiple null values but unique non-null values
  },
  walletPrivateKey: {
    type: String,
    trim: true,
    match: [/^0x[a-fA-F0-9]{64}$/, 'Please enter a valid private key'],
    select: false // Don't include in queries by default for security
  },
  cryptoBalance: {
    BNB: {
      type: Number,
      default: 0,
      min: [0, 'BNB balance cannot be negative'],
      set: (value: number) => Math.round(value * 1000000) / 1000000 // Round to 6 decimal places
    },
    USDT: {
      type: Number,
      default: 0,
      min: [0, 'USDT balance cannot be negative'],
      set: (value: number) => Math.round(value * 1000000) / 1000000 // Round to 6 decimal places
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes (userId already has unique: true in schema definition)

// Virtual for available balance (balance minus pending withdrawals)
userWalletSchema.virtual('availableBalance').get(function(this: IUserWallet) {
  return Math.max(0, this.balance - this.pendingWithdrawals);
});

// Virtual for total earnings (returns + referral earnings)
userWalletSchema.virtual('totalEarnings').get(function(this: IUserWallet) {
  return this.totalReturns + this.totalReferralEarnings;
});

// Instance methods
userWalletSchema.methods.addBalance = function(amount: number) {
  this.balance += amount;
  return this.save();
};

userWalletSchema.methods.deductBalance = function(amount: number) {
  if (this.availableBalance < amount) {
    throw new Error('Insufficient balance');
  }
  this.balance -= amount;
  return this.save();
};

userWalletSchema.methods.addInvestment = function(amount: number) {
  this.totalInvested += amount;
  return this.save();
};

userWalletSchema.methods.addReturns = function(amount: number) {
  this.totalReturns += amount;
  this.balance += amount;
  this.lifetimeEarnings += amount;
  return this.save();
};

userWalletSchema.methods.addReferralEarnings = function(amount: number) {
  this.totalReferralEarnings += amount;
  this.balance += amount;
  this.lifetimeEarnings += amount;
  return this.save();
};

userWalletSchema.methods.addPendingWithdrawal = function(amount: number) {
  if (this.availableBalance < amount) {
    throw new Error('Insufficient available balance for withdrawal');
  }
  this.pendingWithdrawals += amount;
  return this.save();
};

userWalletSchema.methods.completePendingWithdrawal = function(amount: number) {
  this.pendingWithdrawals = Math.max(0, this.pendingWithdrawals - amount);
  this.balance = Math.max(0, this.balance - amount);
  return this.save();
};

userWalletSchema.methods.cancelPendingWithdrawal = function(amount: number) {
  this.pendingWithdrawals = Math.max(0, this.pendingWithdrawals - amount);
  return this.save();
};

// Crypto wallet methods
userWalletSchema.methods.updateCryptoBalance = function(currency: 'BNB' | 'USDT', balance: number) {
  if (!this.cryptoBalance) {
    this.cryptoBalance = { BNB: 0, USDT: 0, lastUpdated: new Date() };
  }
  this.cryptoBalance[currency] = balance;
  this.cryptoBalance.lastUpdated = new Date();
  return this.save();
};

userWalletSchema.methods.getCryptoBalance = function(currency?: 'BNB' | 'USDT') {
  if (!this.cryptoBalance) {
    return currency ? 0 : { BNB: 0, USDT: 0 };
  }
  return currency ? this.cryptoBalance[currency] : { BNB: this.cryptoBalance.BNB, USDT: this.cryptoBalance.USDT };
};

userWalletSchema.methods.hasWalletAddress = function() {
  return !!(this.walletAddress && this.walletPrivateKey);
};

// Static methods
userWalletSchema.statics.findByUserId = function(userId: string | Types.ObjectId) {
  return this.findOne({ userId });
};

userWalletSchema.statics.createForUser = function(userId: string | Types.ObjectId) {
  return this.create({ userId });
};

userWalletSchema.statics.findByWalletAddress = function(walletAddress: string) {
  return this.findOne({ walletAddress }).select('+walletAddress');
};

// Override toJSON to exclude sensitive fields
userWalletSchema.methods.toJSON = function() {
  const walletObject = this.toObject();
  delete walletObject.walletPrivateKey; // Never expose private key
  delete walletObject.__v;
  return walletObject;
};

export const UserWallet = mongoose.model<IUserWallet>('UserWallet', userWalletSchema);
