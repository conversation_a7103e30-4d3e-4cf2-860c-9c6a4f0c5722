import { Router, Request, Response } from 'express';
import { cryptoWalletService } from '../services/CryptoWalletService';
import { authenticateToken } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { body } from 'express-validator';

const router = Router();

// Generate new crypto wallet for user
router.post('/generate', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.sub;
    
    // Check if user already has a crypto wallet
    const existingWallet = await cryptoWalletService.getUserCryptoWallet(userId);
    if (existingWallet?.address) {
      return res.status(200).json({
        success: true,
        data: {
          address: existingWallet.address,
          cryptoBalance: existingWallet.cryptoBalance
        },
        message: 'Using existing wallet',
        existing: true
      });
    }

    // Generate new wallet
    const newWallet = await cryptoWalletService.generateWalletForUser(userId);
    
    res.status(201).json({
      success: true,
      data: {
        address: newWallet.address,
        privateKey: newWallet.privateKey // Only return on creation
      },
      message: 'Crypto wallet generated successfully',
      existing: false
    });
  } catch (error) {
    console.error('Error generating crypto wallet:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate crypto wallet',
      error: (error as Error).message
    });
  }
});

// Save crypto wallet for user
router.post('/save', 
  authenticateToken,
  [
    body('walletAddress')
      .matches(/^0x[a-fA-F0-9]{40}$/)
      .withMessage('Invalid wallet address format'),
    body('walletPrivateKey')
      .matches(/^0x[a-fA-F0-9]{64}$/)
      .withMessage('Invalid private key format')
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      const userId = (req as any).user.sub;
      const { walletAddress, walletPrivateKey } = req.body;

      // Get or create user wallet
      const { UserWallet } = require('../models/UserWallet');
      let userWallet = await UserWallet.findOne({ userId });
      
      if (!userWallet) {
        userWallet = await UserWallet.create({
          userId,
          balance: 0,
          currency: 'USD',
          isActive: true
        });
      }

      // Update with crypto wallet details
      userWallet.walletAddress = walletAddress;
      userWallet.walletPrivateKey = walletPrivateKey;
      userWallet.cryptoBalance = {
        BNB: 0,
        USDT: 0,
        lastUpdated: new Date()
      };

      await userWallet.save();

      res.status(200).json({
        success: true,
        message: 'Crypto wallet saved successfully'
      });
    } catch (error) {
      console.error('Error saving crypto wallet:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save crypto wallet',
        error: (error as Error).message
      });
    }
  }
);

// Get user's crypto wallet info
router.get('/info', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.sub;
    
    const walletInfo = await cryptoWalletService.getUserCryptoWallet(userId);
    
    if (!walletInfo) {
      return res.status(404).json({
        success: false,
        message: 'No crypto wallet found for user'
      });
    }

    res.status(200).json({
      success: true,
      data: walletInfo
    });
  } catch (error) {
    console.error('Error getting crypto wallet info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get crypto wallet info',
      error: (error as Error).message
    });
  }
});

// Get crypto balances for user's wallet
router.get('/balance', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.sub;
    
    const walletInfo = await cryptoWalletService.getUserCryptoWallet(userId);
    
    if (!walletInfo?.address) {
      return res.status(404).json({
        success: false,
        message: 'No crypto wallet found for user'
      });
    }

    // Get live balances from blockchain
    const liveBalances = await cryptoWalletService.getCryptoBalances(walletInfo.address);
    
    // Update stored balances
    await cryptoWalletService.updateCryptoBalance(userId, 'BNB', liveBalances.BNB);
    await cryptoWalletService.updateCryptoBalance(userId, 'USDT', liveBalances.USDT);

    res.status(200).json({
      success: true,
      data: {
        address: walletInfo.address,
        balances: liveBalances
      }
    });
  } catch (error) {
    console.error('Error getting crypto balances:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get crypto balances',
      error: (error as Error).message
    });
  }
});

// Monitor wallet for incoming transactions
router.post('/monitor', authenticateToken, async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.sub;
    
    const result = await cryptoWalletService.monitorWallet(userId);
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error monitoring wallet:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to monitor wallet',
      error: (error as Error).message
    });
  }
});

// Update crypto balance (admin only)
router.put('/balance/:currency', 
  authenticateToken,
  [
    body('balance').isNumeric().withMessage('Balance must be a number'),
    body('userId').isMongoId().withMessage('Invalid user ID')
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    try {
      // Check if user is admin (you may need to adjust this based on your auth system)
      const currentUser = (req as any).user;
      if (currentUser.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Admin privileges required.'
        });
      }

      const { currency } = req.params;
      const { balance, userId } = req.body;

      if (!['BNB', 'USDT'].includes(currency)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid currency. Must be BNB or USDT.'
        });
      }

      const updatedWallet = await cryptoWalletService.updateCryptoBalance(
        userId, 
        currency as 'BNB' | 'USDT', 
        balance
      );

      res.status(200).json({
        success: true,
        data: updatedWallet,
        message: `${currency} balance updated successfully`
      });
    } catch (error) {
      console.error('Error updating crypto balance:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update crypto balance',
        error: (error as Error).message
      });
    }
  }
);

export default router;
