import { Router, Request, Response } from 'express';
import authRoutes from './auth';
import userRoutes from './users';
import propertyRoutes from './properties';
import kycRoutes from './kyc';
import transactionRoutes from './transactions';
import supportRoutes from './support';
import faqRoutes from './faq';
import walletRoutes from './walletRoutes';
import paymentRoutes from './payments';
import testRoutes from './test';
import adminPaymentRoutes from './adminPayments';
import adminUserRoutes from './adminUsers';
import adminPropertyOwnerRoutes from './adminPropertyOwners';
import referralRoutes from './referralRoutes';
import propertyOwnerRoutes from './propertyOwnerRoutes';
import wishlistRoutes from './wishlistRoutes';
import featuredPropertyRoutes from './featuredPropertyRoutes';
import leadRoutes from './leadRoutes';
import s3Routes from './s3Routes';
import s3DirectRoutes from './s3Direct';
import authTestRoutes from './authTest';
import dashboardRoutes from './dashboardRoutes';
import settingsRoutes from './settings';
import adminSettingsRoutes from './adminSettings';
import stockRoutes from './stockRoutes';
import salesRoutes from './sales';
import { stockMaturityRoutes } from './stockMaturityRoutes';
import cryptoWalletRoutes from './cryptoWallet';

import { database } from '../config/database';
import { ApiResponse } from '../types';

const router: any = Router();

/**
 * Health check endpoint
 */
router.get('/health', async (req: Request, res: Response) => {
  try {
    const dbHealth = await database.healthCheck();
    
    const response: ApiResponse = {
      success: true,
      message: 'Server is healthy',
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: dbHealth,
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
      }
    };

    res.status(200).json(response);
  } catch (error) {
    res.status(503).json({
      success: false,
      message: 'Server health check failed',
      error: 'HEALTH_CHECK_ERROR'
    });
  }
});

/**
 * API Info endpoint
 */
router.get('/info', (req: Request, res: Response) => {
  const response: ApiResponse = {
    success: true,
    message: 'SGM  API Information',
    data: {
      name: 'SGM  API',
      version: '1.0.0',
      description: 'Real Estate Investment Platform API',
      environment: process.env.NODE_ENV || 'development',
      endpoints: {
        auth: '/api/auth',
        users: '/api/users',
        properties: '/api/properties',
        kyc: '/api/kyc',
        transactions: '/api/transactions',
        support: '/api/support',
        faq: '/api/faq',
        wallet: '/api/wallet',
        cryptoWallet: '/api/crypto-wallet',
        payments: '/api/payments',
        test: '/api/test',
        adminPayments: '/api/admin/payments',
        adminUsers: '/api/admin/users',
        adminPropertyOwners: '/api/admin/property-owners',
        referrals: '/api/referrals',
        propertyOwners: '/api/property-owners',
        wishlist: '/api/wishlist',
        featured: '/api/featured',
        leads: '/api/leads',
        sales: '/api/sales',

        s3: '/api/s3',
        // dashboard: '/api/dashboard',
        authTest: '/api/auth-test',
        settings: '/api/settings',
        health: '/api/health'
      },
      documentation: '/api/docs'
    }
  };

  res.status(200).json(response);
});

/**
 * Mount route modules
 */
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/properties', propertyRoutes);
router.use('/kyc', kycRoutes);
router.use('/transactions', transactionRoutes);
router.use('/support', supportRoutes);
router.use('/faq', faqRoutes);
router.use('/wallet', walletRoutes);
router.use('/crypto-wallet', cryptoWalletRoutes);
router.use('/payments', paymentRoutes);
router.use('/test', testRoutes);
router.use('/admin/payments', adminPaymentRoutes);
router.use('/admin/users', adminUserRoutes);
router.use('/admin/property-owners', adminPropertyOwnerRoutes);
router.use('/referrals', referralRoutes);
router.use('/property-owners', propertyOwnerRoutes);
router.use('/wishlist', wishlistRoutes);
router.use('/featured', featuredPropertyRoutes);
router.use('/leads', leadRoutes);
router.use('/s3', s3Routes);
router.use('/s3-direct', s3DirectRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/auth-test', authTestRoutes);
router.use('/settings', settingsRoutes);
router.use('/admin/settings', adminSettingsRoutes);
router.use('/stocks', stockRoutes);
router.use('/sales', salesRoutes);
router.use('/stock-maturity', stockMaturityRoutes);


/**
 * 404 handler for API routes
 */
router.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    message: `API endpoint ${req.originalUrl} not found`,
    error: 'ENDPOINT_NOT_FOUND'
  });
});

export default router;
