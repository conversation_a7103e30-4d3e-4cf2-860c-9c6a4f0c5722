import { Types } from 'mongoose';
import { UserWallet, IUserWallet } from '../models/UserWallet';
import { BaseService } from './BaseService';

// Import the crypto wallet functionality from own_pay
const { WalletGenerator, WalletMonitor } = require('../own_pay/own_pay');

export class CryptoWalletService extends BaseService<IUserWallet> {
  constructor() {
    super(UserWallet);
  }

  /**
   * Generate a new crypto wallet for user
   */
  async generateWalletForUser(userId: string | Types.ObjectId): Promise<{ address: string; privateKey: string }> {
    try {
      // Check if user already has a crypto wallet
      const existingWallet = await UserWallet.findOne({ userId }).select('+walletAddress +walletPrivateKey');
      
      if (existingWallet?.walletAddress && existingWallet?.walletPrivateKey) {
        return {
          address: existingWallet.walletAddress,
          privateKey: existingWallet.walletPrivateKey
        };
      }

      // Generate new wallet
      const generator = new WalletGenerator();
      const newWallet = generator.generateWallet();

      // Get or create user wallet
      let userWallet = existingWallet;
      if (!userWallet) {
        userWallet = await UserWallet.create({
          userId: new Types.ObjectId(userId as string),
          balance: 0,
          currency: 'USD',
          isActive: true
        });
      }

      // Update with crypto wallet details
      userWallet.walletAddress = newWallet.address;
      userWallet.walletPrivateKey = newWallet.privateKey;
      userWallet.cryptoBalance = {
        BNB: 0,
        USDT: 0,
        lastUpdated: new Date()
      };

      await userWallet.save();

      return {
        address: newWallet.address,
        privateKey: newWallet.privateKey
      };
    } catch (error) {
      throw this.handleError(error, 'generateWalletForUser');
    }
  }

  /**
   * Get crypto wallet for user
   */
  async getUserCryptoWallet(userId: string | Types.ObjectId): Promise<{ address?: string; cryptoBalance?: any } | null> {
    try {
      const wallet = await UserWallet.findOne({ userId }).select('+walletAddress');
      
      if (!wallet?.walletAddress) {
        return null;
      }

      return {
        address: wallet.walletAddress,
        cryptoBalance: wallet.cryptoBalance || { BNB: 0, USDT: 0 }
      };
    } catch (error) {
      throw this.handleError(error, 'getUserCryptoWallet');
    }
  }

  /**
   * Update crypto balance for user
   */
  async updateCryptoBalance(
    userId: string | Types.ObjectId, 
    currency: 'BNB' | 'USDT', 
    balance: number
  ): Promise<IUserWallet> {
    try {
      const wallet = await UserWallet.findOne({ userId });
      
      if (!wallet) {
        throw new Error('User wallet not found');
      }

      await wallet.updateCryptoBalance(currency, balance);
      return wallet;
    } catch (error) {
      throw this.handleError(error, 'updateCryptoBalance');
    }
  }

  /**
   * Monitor wallet for incoming transactions
   */
  async monitorWallet(userId: string | Types.ObjectId): Promise<any> {
    try {
      const wallet = await UserWallet.findOne({ userId }).select('+walletAddress +walletPrivateKey');
      
      if (!wallet?.walletAddress || !wallet?.walletPrivateKey) {
        throw new Error('User does not have a crypto wallet');
      }

      // Get settings for monitoring
      const { settingDbHandler } = require('../services/db');
      const keySettings = await settingDbHandler.getByQuery({ name: "Keys" });
      
      if (!keySettings || keySettings.length === 0) {
        throw new Error('Crypto wallet settings not configured');
      }

      const settings = keySettings[0].value;
      const monitor = new WalletMonitor(
        settings.usdtReceiveWallet,
        settings.gasWallet,
        settings.gasPrivateKey
      );

      // Monitor the user's wallet
      const result = await monitor.monitorAndTransfer({
        address: wallet.walletAddress,
        privateKey: wallet.walletPrivateKey
      });

      // Update crypto balances if transaction was found
      if (result.found && result.amount) {
        await this.updateCryptoBalance(userId, 'USDT', 0); // Balance should be 0 after transfer
        
        // Update main wallet balance with the deposited amount
        wallet.balance += result.amount;
        wallet.totalDeposited += result.amount;
        await wallet.save();
      }

      return result;
    } catch (error) {
      throw this.handleError(error, 'monitorWallet');
    }
  }

  /**
   * Get crypto balance for wallet address
   */
  async getCryptoBalances(walletAddress: string): Promise<{ BNB: number; USDT: number }> {
    try {
      const { settingDbHandler } = require('../services/db');
      const keySettings = await settingDbHandler.getByQuery({ name: "Keys" });
      
      if (!keySettings || keySettings.length === 0) {
        throw new Error('Crypto wallet settings not configured');
      }

      const settings = keySettings[0].value;
      const monitor = new WalletMonitor(
        settings.usdtReceiveWallet,
        settings.gasWallet,
        settings.gasPrivateKey
      );

      const [bnbBalance, usdtBalance] = await Promise.all([
        monitor.getBNBBalance(walletAddress),
        monitor.getUSDTBalance(walletAddress)
      ]);

      return {
        BNB: bnbBalance,
        USDT: usdtBalance
      };
    } catch (error) {
      throw this.handleError(error, 'getCryptoBalances');
    }
  }

  /**
   * Find user by wallet address
   */
  async findUserByWalletAddress(walletAddress: string): Promise<IUserWallet | null> {
    try {
      return await UserWallet.findByWalletAddress(walletAddress);
    } catch (error) {
      throw this.handleError(error, 'findUserByWalletAddress');
    }
  }
}

export const cryptoWalletService = new CryptoWalletService();
