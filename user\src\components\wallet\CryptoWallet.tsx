import React, { useState, useEffect } from 'react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { toast } from 'react-hot-toast';

interface CryptoWalletProps {
  className?: string;
}

interface WalletInfo {
  address?: string;
  cryptoBalance?: {
    BNB: number;
    USDT: number;
    lastUpdated: string;
  };
}

interface WalletBalances {
  address: string;
  balances: {
    BNB: number;
    USDT: number;
  };
}

export const CryptoWallet: React.FC<CryptoWalletProps> = ({ className }) => {
  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [balances, setBalances] = useState<WalletBalances | null>(null);
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [monitoring, setMonitoring] = useState(false);

  // Fetch wallet info on component mount
  useEffect(() => {
    fetchWalletInfo();
  }, []);

  const fetchWalletInfo = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/crypto-wallet/info', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setWalletInfo(data.data);
      } else if (response.status === 404) {
        // No wallet found - this is normal for new users
        setWalletInfo(null);
      } else {
        throw new Error('Failed to fetch wallet info');
      }
    } catch (error) {
      console.error('Error fetching wallet info:', error);
      // Don't show error toast for 404 - it's expected for new users
    } finally {
      setLoading(false);
    }
  };

  const generateWallet = async () => {
    try {
      setGenerating(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/crypto-wallet/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setWalletInfo(data.data);
        toast.success(data.message);
        
        // Show private key only once for new wallets
        if (!data.existing && data.data.privateKey) {
          toast.success(
            `Wallet created! Private Key: ${data.data.privateKey.substring(0, 10)}... (Save this securely!)`,
            { duration: 10000 }
          );
        }
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error generating wallet:', error);
      toast.error('Failed to generate wallet');
    } finally {
      setGenerating(false);
    }
  };

  const fetchBalances = async () => {
    if (!walletInfo?.address) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/crypto-wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setBalances(data.data);
        toast.success('Balances updated');
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error fetching balances:', error);
      toast.error('Failed to fetch balances');
    } finally {
      setLoading(false);
    }
  };

  const monitorWallet = async () => {
    if (!walletInfo?.address) return;

    try {
      setMonitoring(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch('/api/crypto-wallet/monitor', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        const result = data.data;
        if (result.found) {
          toast.success(`Transaction detected! Amount: ${result.amount} ${result.currency}`);
          // Refresh balances after successful monitoring
          await fetchBalances();
        } else {
          toast.info(result.message || 'No transactions found');
        }
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error monitoring wallet:', error);
      toast.error('Failed to monitor wallet');
    } finally {
      setMonitoring(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const formatAddress = (address: string) => {
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Crypto Wallet</h3>
          <Badge variant="outline">BSC Network</Badge>
        </div>

        {!walletInfo?.address ? (
          <div className="text-center space-y-4">
            <p className="text-gray-600">No crypto wallet found. Generate one to start using crypto payments.</p>
            <Button 
              onClick={generateWallet} 
              disabled={generating}
              className="w-full"
            >
              {generating ? 'Generating...' : 'Generate Crypto Wallet'}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <Label>Wallet Address</Label>
              <div className="flex items-center space-x-2 mt-1">
                <Input 
                  value={walletInfo.address} 
                  readOnly 
                  className="font-mono text-sm"
                />
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => copyToClipboard(walletInfo.address!)}
                >
                  Copy
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Send USDT (BEP-20) to this address to add funds
              </p>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Crypto Balances</h4>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={fetchBalances}
                  disabled={loading}
                >
                  {loading ? 'Updating...' : 'Refresh'}
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm text-gray-600">BNB Balance</div>
                  <div className="text-lg font-semibold">
                    {balances?.balances.BNB?.toFixed(6) || walletInfo.cryptoBalance?.BNB?.toFixed(6) || '0.000000'}
                  </div>
                </div>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="text-sm text-gray-600">USDT Balance</div>
                  <div className="text-lg font-semibold">
                    {balances?.balances.USDT?.toFixed(2) || walletInfo.cryptoBalance?.USDT?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="font-medium">Actions</h4>
              <div className="grid grid-cols-1 gap-2">
                <Button 
                  variant="outline"
                  onClick={monitorWallet}
                  disabled={monitoring}
                  className="w-full"
                >
                  {monitoring ? 'Monitoring...' : 'Check for Deposits'}
                </Button>
              </div>
              <p className="text-xs text-gray-500">
                Click "Check for Deposits" after sending USDT to automatically detect and process your deposit.
              </p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};
