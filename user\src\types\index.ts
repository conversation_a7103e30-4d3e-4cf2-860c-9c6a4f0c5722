// User Types
export interface User {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  role: UserRole
  status: UserStatus
  emailVerified: boolean
  phoneVerified: boolean
  kycStatus: KYCStatus
  profileImage?: string
  avatar?: FileData
  documents?: FileData[]
  dateOfBirth?: string
  address?: Address
  referralCode?: string
  referredBy?: string
  wallet?: Wallet
  createdAt: string
  updatedAt: string
}

export enum UserRole {
  ADMIN = 'admin',
  SUBADMIN = 'subadmin',
  SALES = 'sales',
  USER = 'user'
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING_VERIFICATION = 'pending_verification'
}

export enum KYCStatus {
  NOT_STARTED = 'not_started',
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface Address {
  street: string
  city: string
  state: string
  country: string
  zipCode: string
}

export interface FileData {
  key: string
  name: string
  type: string
  url: string
  uploadedAt: string
}

// Property Types
export interface Property {
  _id: string
  title: string
  name?: string // For backward compatibility
  description: string
  location: {
    address: string
    city: string
    state: string
    country: string
    coordinates?: {
      lat: number
      lng: number
    }
  } | string // For backward compatibility
  category: string
  type?: PropertyType // For backward compatibility
  status: PropertyStatus
  price?: {
    total: number
    pricePerShare: number
    currency: string
  }
  investment?: {
    totalShares: number
    availableShares: number
    minimumInvestment: number
    expectedReturn: number
    rentalYield: number
  }
  // Legacy fields for backward compatibility
  totalStocks?: number
  soldStocks?: number
  availableStocks?: number
  pricePerStock?: number
  totalValue?: number
  roi?: number
  featured: boolean
  launchDate?: string
  completionDate?: string
  developer: {
    name: string
    logo?: string
    rating?: number
    contact?: string
    email?: string
    experience?: number
  } | string // For backward compatibility
  images: string[] | FileData[]
  documents: Array<{
    type: string
    url: string
    name: string
  }> | FileData[]
  videos?: FileData[]
  amenities: string[]
  specifications?: {
    area: number
    bedrooms: number
    bathrooms: number
    floors: number
    parking: number
    yearBuilt?: number
  }
  stockInfo?: {
    stockPrice: number
    totalStocks: number
    availableStocks: number
    stocksSold: number
    minimumPurchase?: number
    expectedROI?: number
  }
  expectedReturns?: number
  createdAt: string
  updatedAt: string
}

export enum PropertyType {
  RESIDENTIAL = 'residential',
  COMMERCIAL = 'commercial',
  MIXED = 'mixed'
}

export enum PropertyStatus {
  ACTIVE = 'active',
  SOLD_OUT = 'sold_out',
  COMING_SOON = 'coming_soon',
  INACTIVE = 'inactive',
  // Legacy values for backward compatibility
  SOLD = 'sold',
  UPCOMING = 'upcoming',
  COMPLETED = 'completed'
}

// Investment Types
export interface Investment {
  _id: string
  userId: string
  propertyId: string
  property: Property
  stocksPurchased: number
  pricePerStock: number
  totalAmount: number
  purchaseDate: string
  status: InvestmentStatus
  returns?: number
  maturityDate?: string
  createdAt: string
  updatedAt: string
}

export enum InvestmentStatus {
  ACTIVE = 'active',
  MATURED = 'matured',
  CANCELLED = 'cancelled'
}

// Wallet Types
export interface Wallet {
  _id: string
  userId: string
  balance: number
  totalDeposited: number
  totalWithdrawn: number
  totalEarnings: number
  totalInvested?: number
  totalReturns?: number
  walletAddress?: string
  cryptoBalance?: {
    BNB: number
    USDT: number
    lastUpdated: string
  }
  transactions: Transaction[]
  createdAt: string
  updatedAt: string
}

export interface Transaction {
  _id: string
  walletId: string
  type: TransactionType
  amount: number
  description: string
  status: TransactionStatus
  referenceId?: string
  metadata?: any
  createdAt: string
  updatedAt: string
}

export enum TransactionType {
  DEPOSIT = 'deposit',
  WITHDRAWAL = 'withdrawal',
  INVESTMENT = 'investment',
  STOCK_PURCHASE = 'stock_purchase',
  RETURN = 'return',
  REFERRAL_BONUS = 'referral_bonus',
  COMMISSION = 'commission'
}

export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Referral Types
export interface Referral {
  _id: string
  referrerId: string
  referredUserId: string
  referredUser: User
  status: ReferralStatus
  commissionEarned: number
  commissionPaid: boolean
  createdAt: string
  updatedAt: string
}

export enum ReferralStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed'
}

// Support Types
export interface SupportTicket {
  _id: string
  userId: string
  user: User
  subject: string
  description: string
  category: TicketCategory
  priority: TicketPriority
  status: TicketStatus
  assignedTo?: string
  responses: TicketResponse[]
  attachments?: FileData[]
  ticket?: SupportTicket
  messages?: TicketResponse[]
  createdAt: string
  updatedAt: string
}

export interface TicketResponse {
  _id: string
  ticketId: string
  userId: string
  user: User
  message: string
  isStaff: boolean
  attachments?: FileData[]
  createdAt: string
}

export enum TicketCategory {
  TECHNICAL = 'technical',
  BILLING = 'billing',
  INVESTMENT = 'investment',
  ACCOUNT = 'account',
  GENERAL = 'general'
}

export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

// Notification Types
export interface Notification {
  _id: string
  id: string
  userId: string
  title: string
  message: string
  type: NotificationType
  read: boolean
  data?: any
  createdAt: string
  updatedAt: string
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INVESTMENT = 'investment',
  KYC = 'kyc',
  WALLET = 'wallet'
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  errors?: any[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Dashboard Types
export interface DashboardStats {
  totalInvestment: number
  currentValue: number
  totalReturns: number
  expectedReturns: number
  totalStocks: number
  activeInvestments: number
  walletBalance: number
  referralEarnings: number
}

export interface PortfolioSummary {
  invested: string
  current: string
  returns: string
  expected: string
  stocks: string
}

export interface FeaturedInvestment {
  name: string
  location: string
  stockPrice: string
  minimum: string
  returns: string
  available: string
  amenities: string
}

export interface InvestmentOpportunity {
  name: string
  location: string
  stockPrice: string
  available: string
  returns: string
}

// Form Types
export interface LoginForm {
  email: string
  password: string
  rememberMe?: boolean
  referralCode?: string
}

export interface RegisterForm {
  firstName: string
  lastName: string
  email: string
  phone: string
  password: string
  confirmPassword: string
  referralCode?: string
  agreeToTerms: boolean
}

export interface InvestmentForm {
  propertyId: string
  stockQuantity: number
  totalAmount: number
  paymentMethod: string
}

export interface WithdrawalForm {
  amount: number
  bankAccount: string
  reason?: string
}

export interface SupportTicketForm {
  subject: string
  description: string
  category: TicketCategory
  priority: TicketPriority
  attachments?: File[]
}
